# Documentation Examples

This directory contains code examples that are referenced in the API documentation.

## Structure

- `basic/` - Basic usage examples
- `advanced/` - Advanced usage patterns
- `integration/` - Integration examples with other libraries
- `tutorials/` - Step-by-step tutorials

## Example Categories

### Basic Examples
- Creating a simple PDF viewer
- Loading and displaying documents
- Basic navigation and zoom
- Simple annotation creation

### Advanced Examples
- Custom annotation types
- Performance optimization
- Memory management
- Multi-threaded operations
- Plugin development

### Integration Examples
- Qt Designer integration
- CMake integration
- CI/CD pipeline setup
- Testing frameworks

### Tutorials
- Getting started guide
- Building your first PDF viewer
- Adding custom features
- Deployment strategies

## File Naming Convention

Use descriptive names that clearly indicate the example's purpose:
- `basic_pdf_viewer.cpp` - Basic PDF viewer implementation
- `custom_annotations.cpp` - Custom annotation examples
- `performance_tips.cpp` - Performance optimization examples
- `memory_management.cpp` - Memory management best practices

## Documentation Integration

Examples are automatically included in the generated documentation through:
- `@example` Doxygen commands in header files
- `@include` commands for code snippets
- `@snippet` commands for partial code examples

## Code Quality

All examples should:
- Compile without warnings
- Follow the project's coding standards
- Include comprehensive comments
- Demonstrate best practices
- Handle errors appropriately
- Be self-contained when possible

## Testing

Examples should be:
- Tested with the current codebase
- Updated when APIs change
- Verified to work on all supported platforms
- Included in automated testing where appropriate

## Contributing

When adding new examples:
1. Follow the existing structure
2. Include comprehensive documentation
3. Test on multiple platforms
4. Update this README if adding new categories
5. Reference examples in relevant API documentation
