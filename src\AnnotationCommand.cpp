#include "AnnotationCommand.h"
#include "AnnotationManager.h"
#include <QDebug>

// Base AnnotationCommand
AnnotationCommand::AnnotationCommand(AnnotationManager* manager, QUndoCommand* parent)
    : QUndoCommand(parent)
    , m_annotationManager(manager)
{
}

// CreateAnnotationCommand
CreateAnnotationCommand::CreateAnnotationCommand(AnnotationManager* manager, std::unique_ptr<Annotation> annotation, QUndoCommand* parent)
    : AnnotationCommand(manager, parent)
    , m_annotation(std::move(annotation))
{
    if (m_annotation) {
        m_annotationId = m_annotation->getId();
        setText(QObject::tr("Create %1").arg(getAnnotationTypeName(m_annotation->getType())));
    }
}

void CreateAnnotationCommand::undo()
{
    if (m_annotationManager && !m_annotationId.isEmpty()) {
        // Store the annotation before removing it
        Annotation* annotation = m_annotationManager->getAnnotation(m_annotationId);
        if (annotation) {
            // Create a copy for storage
            m_annotation = AnnotationFactory::createAnnotation(annotation->getType());
            if (m_annotation) {
                m_annotation->fromJson(annotation->toJson());
            }
        }
        m_annotationManager->removeAnnotation(m_annotationId);
    }
}

void CreateAnnotationCommand::redo()
{
    if (m_annotationManager && m_annotation) {
        if (m_isFirstRedo) {
            // First redo - add the original annotation
            std::unique_ptr<Annotation> annotationCopy = AnnotationFactory::createAnnotation(m_annotation->getType());
            if (annotationCopy) {
                annotationCopy->fromJson(m_annotation->toJson());
                m_annotationManager->addAnnotation(std::move(annotationCopy));
            }
            m_isFirstRedo = false;
        } else {
            // Subsequent redos - recreate from stored data
            std::unique_ptr<Annotation> annotationCopy = AnnotationFactory::createAnnotation(m_annotation->getType());
            if (annotationCopy) {
                annotationCopy->fromJson(m_annotation->toJson());
                m_annotationManager->addAnnotation(std::move(annotationCopy));
            }
        }
    }
}

// DeleteAnnotationCommand
DeleteAnnotationCommand::DeleteAnnotationCommand(AnnotationManager* manager, const QString& annotationId, QUndoCommand* parent)
    : AnnotationCommand(manager, parent)
    , m_annotationId(annotationId)
{
    setText(QObject::tr("Delete Annotation"));
    
    // Store annotation data before deletion
    if (m_annotationManager) {
        Annotation* annotation = m_annotationManager->getAnnotation(annotationId);
        if (annotation) {
            m_annotationData = annotation->toJson();
        }
    }
}

void DeleteAnnotationCommand::undo()
{
    if (m_annotationManager && !m_annotationData.isEmpty()) {
        // Recreate annotation from stored data
        AnnotationType type = static_cast<AnnotationType>(m_annotationData["type"].toInt());
        auto annotation = AnnotationFactory::createAnnotation(type);
        if (annotation) {
            annotation->fromJson(m_annotationData);
            m_annotationManager->addAnnotation(std::move(annotation));
        }
    }
}

void DeleteAnnotationCommand::redo()
{
    if (m_annotationManager && !m_annotationId.isEmpty()) {
        m_annotationManager->removeAnnotation(m_annotationId);
    }
}

// MoveAnnotationCommand
MoveAnnotationCommand::MoveAnnotationCommand(AnnotationManager* manager, const QString& annotationId, 
                                           const QPointF& oldPosition, const QPointF& newPosition, QUndoCommand* parent)
    : AnnotationCommand(manager, parent)
    , m_annotationId(annotationId)
    , m_oldPosition(oldPosition)
    , m_newPosition(newPosition)
{
    setText(QObject::tr("Move Annotation"));
}

void MoveAnnotationCommand::undo()
{
    if (m_annotationManager) {
        Annotation* annotation = m_annotationManager->getAnnotation(m_annotationId);
        if (annotation) {
            QPointF currentPos = annotation->getBoundingRect().topLeft();
            QPointF offset = m_oldPosition - currentPos;
            annotation->move(offset);
        }
    }
}

void MoveAnnotationCommand::redo()
{
    if (m_annotationManager) {
        Annotation* annotation = m_annotationManager->getAnnotation(m_annotationId);
        if (annotation) {
            QPointF currentPos = annotation->getBoundingRect().topLeft();
            QPointF offset = m_newPosition - currentPos;
            annotation->move(offset);
        }
    }
}

// ResizeAnnotationCommand
ResizeAnnotationCommand::ResizeAnnotationCommand(AnnotationManager* manager, const QString& annotationId, 
                                                 const QRectF& oldBounds, const QRectF& newBounds, QUndoCommand* parent)
    : AnnotationCommand(manager, parent)
    , m_annotationId(annotationId)
    , m_oldBounds(oldBounds)
    , m_newBounds(newBounds)
{
    setText(QObject::tr("Resize Annotation"));
}

void ResizeAnnotationCommand::undo()
{
    if (m_annotationManager) {
        Annotation* annotation = m_annotationManager->getAnnotation(m_annotationId);
        if (annotation) {
            annotation->resize(m_oldBounds);
        }
    }
}

void ResizeAnnotationCommand::redo()
{
    if (m_annotationManager) {
        Annotation* annotation = m_annotationManager->getAnnotation(m_annotationId);
        if (annotation) {
            annotation->resize(m_newBounds);
        }
    }
}

// ChangeAnnotationPropertyCommand
ChangeAnnotationPropertyCommand::ChangeAnnotationPropertyCommand(AnnotationManager* manager, const QString& annotationId, 
                                                                 PropertyType propertyType, const QVariant& oldValue, 
                                                                 const QVariant& newValue, QUndoCommand* parent)
    : AnnotationCommand(manager, parent)
    , m_annotationId(annotationId)
    , m_propertyType(propertyType)
    , m_oldValue(oldValue)
    , m_newValue(newValue)
{
    QString propertyName;
    switch (propertyType) {
    case PropertyType::Color:
        propertyName = "Color";
        break;
    case PropertyType::Opacity:
        propertyName = "Opacity";
        break;
    case PropertyType::LineWidth:
        propertyName = "Line Width";
        break;
    case PropertyType::Content:
        propertyName = "Content";
        break;
    case PropertyType::Font:
        propertyName = "Font";
        break;
    }
    setText(QObject::tr("Change %1").arg(propertyName));
}

void ChangeAnnotationPropertyCommand::undo()
{
    applyProperty(m_oldValue);
}

void ChangeAnnotationPropertyCommand::redo()
{
    applyProperty(m_newValue);
}

void ChangeAnnotationPropertyCommand::applyProperty(const QVariant& value)
{
    if (!m_annotationManager) return;
    
    Annotation* annotation = m_annotationManager->getAnnotation(m_annotationId);
    if (!annotation) return;
    
    switch (m_propertyType) {
    case PropertyType::Color:
        annotation->setColor(value.value<QColor>());
        break;
    case PropertyType::Opacity:
        annotation->setOpacity(value.toDouble());
        break;
    case PropertyType::LineWidth:
        annotation->setLineWidth(value.toDouble());
        break;
    case PropertyType::Content:
        annotation->setContent(value.toString());
        break;
    case PropertyType::Font:
        if (auto textAnnotation = dynamic_cast<TextAnnotation*>(annotation)) {
            textAnnotation->setFont(value.value<QFont>());
        }
        break;
    }
}

// BatchAnnotationCommand
BatchAnnotationCommand::BatchAnnotationCommand(AnnotationManager* manager, const QString& text, QUndoCommand* parent)
    : AnnotationCommand(manager, parent)
{
    setText(text);
}

BatchAnnotationCommand::~BatchAnnotationCommand()
{
    for (auto* command : m_commands) {
        delete command;
    }
}

void BatchAnnotationCommand::addCommand(std::unique_ptr<AnnotationCommand> command)
{
    m_commands.append(command.release());
}

void BatchAnnotationCommand::undo()
{
    // Undo commands in reverse order
    for (int i = m_commands.size() - 1; i >= 0; --i) {
        m_commands[i]->undo();
    }
}

void BatchAnnotationCommand::redo()
{
    // Redo commands in forward order
    for (auto* command : m_commands) {
        command->redo();
    }
}

// Helper function to get annotation type name
QString getAnnotationTypeName(AnnotationType type)
{
    switch (type) {
    case AnnotationType::Highlight:
        return QObject::tr("Highlight");
    case AnnotationType::Note:
        return QObject::tr("Note");
    case AnnotationType::Drawing:
        return QObject::tr("Drawing");
    case AnnotationType::Rectangle:
        return QObject::tr("Rectangle");
    case AnnotationType::Circle:
        return QObject::tr("Circle");
    case AnnotationType::Arrow:
        return QObject::tr("Arrow");
    case AnnotationType::Text:
        return QObject::tr("Text");
    default:
        return QObject::tr("Annotation");
    }
}
