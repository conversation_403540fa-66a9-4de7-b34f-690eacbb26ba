// ElaIntegration.cpp
#include "ElaIntegration.h"
#include <QApplication>
#include <QDebug>

namespace ElaIntegration {

void initializeElaApplication() {
#ifdef ELA_WIDGETS_ENABLED
    // Initialize ElaApplication for enhanced styling
    ElaApplication::getInstance()->init();
    qDebug() << "ElaWidgetTools initialized successfully";
#else
    qDebug() << "ElaWidgetTools not available, using standard Qt widgets";
#endif
}

void applyElaTheme() {
#ifdef ELA_WIDGETS_ENABLED
    // Apply ElaWidgetTools theme
    ElaTheme* theme = ElaTheme::getInstance();
    if (theme) {
        // Set default theme mode (can be customized)
        theme->setThemeMode(ElaThemeType::Light);
        qDebug() << "ElaWidgetTools theme applied";
    }
#else
    qDebug() << "ElaWidgetTools theme not available";
#endif
}

bool isElaWidgetsAvailable() {
#ifdef ELA_WIDGETS_ENABLED
    return true;
#else
    return false;
#endif
}

} // namespace ElaIntegration
