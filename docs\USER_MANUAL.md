# Optimized PDF Viewer - User Manual

> **Version 1.0** | **Last Updated:** December 2024

Welcome to the comprehensive user manual for Optimized PDF Viewer, a high-performance PDF viewing application with advanced annotation capabilities and a modern, intuitive interface.

## 📋 Table of Contents

1. [🚀 Getting Started](#getting-started)
2. [🖥️ Interface Overview](#interface-overview)
3. [📂 Opening and Managing Documents](#opening-and-managing-documents)
4. [🧭 Navigation and Viewing](#navigation-and-viewing)
5. [✏️ Annotations and Markup](#annotations-and-markup)
6. [🔍 Search and Find](#search-and-find)
7. [⚙️ Customization and Settings](#customization-and-settings)
8. [⌨️ Keyboard Shortcuts](#keyboard-shortcuts)
9. [🔧 Troubleshooting](#troubleshooting)
10. [📞 Support and Resources](#support-and-resources)

---

## 🚀 Getting Started

### System Requirements

Before installing Optimized PDF Viewer, ensure your system meets these requirements:

**Minimum Requirements:**
- **Operating System:** Windows 10, macOS 10.15, or Linux (Ubuntu 18.04+)
- **Memory:** 4 GB RAM
- **Storage:** 500 MB available space
- **Graphics:** DirectX 11 compatible or OpenGL 3.3 support

**Recommended Requirements:**
- **Operating System:** Windows 11, macOS 12+, or Linux (Ubuntu 20.04+)
- **Memory:** 8 GB RAM or more
- **Storage:** 1 GB available space
- **Graphics:** Dedicated graphics card with 1 GB VRAM

### Installation

#### Quick Installation
1. Download the installer from the official website
2. Run the installer with administrator privileges
3. Follow the installation wizard
4. Launch the application from the Start menu or Applications folder

#### Advanced Installation Options
- **Portable Installation:** Extract the portable version to any folder
- **Silent Installation:** Use `/S` flag for automated deployment
- **Custom Installation Path:** Specify installation directory during setup

### Welcome Screen

When you first launch PDF Viewer, you'll be greeted by a modern, VS Code-inspired welcome screen featuring:

#### Recent Files Section
- **Smart File History:** Recently opened documents with thumbnails and metadata
- **Quick Preview:** Hover over files to see document information
- **Pin Favorites:** Pin frequently used documents for easy access
- **Search History:** Find documents by name or content

#### Quick Actions Panel
- **📂 Open Document** (`Ctrl+O`) - Browse and open PDF files
- **📄 New Tab** (`Ctrl+T`) - Open a new document tab
- **⚙️ Settings** (`Ctrl+,`) - Access application preferences
- **❓ Help** (`F1`) - View help documentation and tutorials
- **🎨 Theme Toggle** - Switch between light and dark themes

#### Getting Started Tips
- **Drag & Drop:** Simply drag PDF files onto the window to open them
- **Multiple Tabs:** Open multiple documents in tabs for easy switching
- **Keyboard Navigation:** Use keyboard shortcuts for faster workflow

### First Time Setup Wizard

The application will guide you through initial configuration:

#### 1. Theme Selection
- **Light Theme:** Clean, professional appearance for office environments
- **Dark Theme:** Reduced eye strain for extended reading sessions
- **Auto Theme:** Automatically switches based on system preferences
- **Custom Themes:** Create personalized color schemes

#### 2. Default Preferences
- **Default Zoom Level:** Set preferred zoom (Fit Width, Fit Page, or custom percentage)
- **Reading Mode:** Choose single page, continuous, or facing pages
- **Annotation Tools:** Select default annotation colors and styles
- **Interface Layout:** Configure which panels to show by default

#### 3. Performance Settings
- **Memory Usage:** Optimize for your system's available RAM
- **Cache Settings:** Configure document caching for faster loading
- **Rendering Quality:** Balance between speed and visual quality
- **Hardware Acceleration:** Enable GPU acceleration if supported

### Quick Start Tutorial

#### Opening Your First Document
1. **Click "Open Document"** or press `Ctrl+O`
2. **Browse to your PDF file** using the modern file dialog
3. **Select the file** and click "Open"
4. **Explore the interface** using the guided tour (optional)

#### Basic Navigation
- **Zoom:** Use the mouse wheel or zoom controls in the toolbar
- **Pan:** Click and drag to move around the document
- **Page Navigation:** Use arrow keys or page controls
- **Fit Options:** Try "Fit Width" and "Fit Page" buttons

#### Your First Annotation
1. **Select the Highlight tool** from the annotation toolbar
2. **Click and drag** over text to highlight it
3. **Right-click** the highlight to add a note
4. **Save the document** to preserve your annotations

## 🖥️ Interface Overview

### Modern Ribbon Interface

Optimized PDF Viewer features a sophisticated Microsoft Office-style ribbon interface powered by ElaWidgetTools, providing a modern and intuitive user experience.

#### Main Interface Components

```
┌─────────────────────────────────────────────────────────────────┐
│ Title Bar                                    [_] [□] [×]         │
├─────────────────────────────────────────────────────────────────┤
│ File | Home | View | Annotate | Tools | Help                    │
├─────────────────────────────────────────────────────────────────┤
│ [Open] [Save] [Print] | [Zoom] [Fit] | [Highlight] [Note]       │
├─────────────────────────────────────────────────────────────────┤
│ ┌─────┐ │                                           │ ┌─────────┐ │
│ │     │ │                                           │ │ Outline │ │
│ │Thumb│ │            Document View                  │ │         │ │
│ │nails│ │                                           │ │ Search  │ │
│ │     │ │                                           │ │         │ │
│ └─────┘ │                                           │ │ Annot.  │ │
│         │                                           │ └─────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ Status Bar: Page 1 of 10 | 100% | Ready                        │
└─────────────────────────────────────────────────────────────────┘
```

#### Ribbon Sections

##### 📁 File Section
- **Open** (`Ctrl+O`) - Open PDF documents with advanced file browser
- **Recent** - Smart recent files with thumbnails and quick preview
- **Save** (`Ctrl+S`) - Save document with annotations
- **Save As** (`Ctrl+Shift+S`) - Export to different formats
- **Print** (`Ctrl+P`) - Advanced printing with preview
- **Export** - Export pages as images or other formats
- **Close** (`Ctrl+W`) - Close current document
- **Exit** (`Alt+F4`) - Exit application

##### 🏠 Home Section
- **New Tab** (`Ctrl+T`) - Open new document tab
- **Clipboard** - Copy/paste annotations and text
- **Find** (`Ctrl+F`) - Advanced search with filters
- **Go To** (`Ctrl+G`) - Navigate to specific page
- **Bookmarks** - Manage document bookmarks

##### 👁️ View Section
- **Zoom Controls**
  - Zoom In (`Ctrl++`) / Zoom Out (`Ctrl+-`)
  - Fit Width (`Ctrl+1`) - Fit page width to window
  - Fit Page (`Ctrl+0`) - Fit entire page to window
  - Actual Size (`Ctrl+2`) - 100% zoom level
  - Custom Zoom - Enter specific percentage
- **Layout Options**
  - Single Page - View one page at a time
  - Continuous - Scroll through pages continuously
  - Facing Pages - Two-page spread view
  - Continuous Facing - Continuous two-page view
- **Rotation**
  - Rotate Left (`Ctrl+Shift+R`) - Rotate 90° counter-clockwise
  - Rotate Right (`Ctrl+R`) - Rotate 90° clockwise
- **Display Options**
  - Full Screen (`F11`) - Immersive reading mode
  - Reading Mode - Distraction-free view
  - Night Mode - Dark theme for low-light reading

##### ✏️ Annotate Section
- **Selection Tools**
  - Text Selection - Select and copy text
  - Area Selection - Select rectangular regions
- **Markup Tools**
  - Highlight (`H`) - Highlight text with customizable colors
  - Underline (`U`) - Underline important text
  - Strikethrough (`S`) - Strike through text
  - Squiggly - Wavy underline for emphasis
- **Drawing Tools**
  - Pen Tool (`P`) - Freehand drawing
  - Line Tool (`L`) - Draw straight lines
  - Arrow Tool (`A`) - Draw arrows and callouts
  - Rectangle Tool (`R`) - Draw rectangles
  - Ellipse Tool (`E`) - Draw circles and ellipses
- **Note Tools**
  - Sticky Note (`N`) - Add popup notes
  - Text Box (`T`) - Add text annotations
  - Callout - Add callout bubbles
- **Stamp Tools**
  - Approved/Rejected stamps
  - Custom stamps
  - Date/time stamps

##### 🔧 Tools Section
- **Document Info** - View PDF metadata and properties
- **Security** - Manage document permissions and passwords
- **Preferences** (`Ctrl+,`) - Application settings
- **Performance Monitor** - View memory usage and performance
- **Plugin Manager** - Manage extensions and plugins
- **Export**: Export pages or entire document (Ctrl+E)
- **Document Info**: View document properties (Ctrl+I)

#### View Section

- **Zoom Controls**: Zoom in/out, fit to window, actual size
- **Layout**: Single page, continuous, facing pages
- **Rotation**: Rotate pages clockwise/counter-clockwise
- **Full Screen**: Toggle full screen mode (F11)

#### Tools Section

- **Annotations**: Access annotation tools
- **Search**: Find text in document (Ctrl+F)
- **Bookmarks**: Manage document bookmarks
- **Thumbnails**: Toggle thumbnail panel

#### Settings Section

- **Preferences**: Open settings dialog
- **Theme**: Quick theme switching
- **Help**: Access help and about information

### Panels and Docks

#### Thumbnail Panel

- Shows page thumbnails for quick navigation
- Click any thumbnail to jump to that page
- Drag to reorder pages (if editing is enabled)

#### Outline Panel

- Displays document outline/bookmarks
- Click entries to navigate to specific sections
- Expandable tree structure for nested bookmarks

#### Search Results Panel

- Shows search results with context
- Click results to navigate to matches
- Highlights search terms in document

### Status Bar

- **Document Info**: Current page, total pages, zoom level
- **Progress Bar**: Shows loading/operation progress
- **Theme Indicator**: Current theme mode

## Opening and Managing Documents

### Opening Documents

1. **File Menu**: Use File → Open or Ctrl+O
2. **Welcome Screen**: Click "Open Document" or select from recent files
3. **Drag and Drop**: Drag PDF files directly onto the application window

### Tabbed Interface

- **Multiple Documents**: Open multiple PDFs in separate tabs
- **Tab Management**:
  - Right-click tabs for context menu
  - Close tabs with Ctrl+W or middle-click
  - Switch tabs with Ctrl+Tab

### Document Properties

Access document information via File → Document Info (Ctrl+I):

- File path and size
- Page count and dimensions
- PDF version and security settings
- Creation and modification dates
- Author and title metadata

## Navigation and Viewing

### Page Navigation

- **Arrow Keys**: Navigate pages with Up/Down arrows
- **Page Numbers**: Enter specific page numbers in the navigation bar
- **Thumbnails**: Click thumbnail panel entries
- **Bookmarks**: Use outline panel for section navigation

### Zoom Controls

- **Zoom In/Out**: Ctrl++ / Ctrl+- or mouse wheel
- **Fit to Window**: Ctrl+0 - fits page to window size
- **Actual Size**: Ctrl+1 - shows page at 100% size
- **Custom Zoom**: Enter specific zoom percentages

### View Modes

- **Single Page**: View one page at a time
- **Continuous**: Scroll through pages continuously
- **Facing Pages**: View two pages side by side (like a book)
- **Full Screen**: F11 for distraction-free reading

### Page Rotation

- **Rotate Clockwise**: Ctrl+R
- **Rotate Counter-clockwise**: Ctrl+Shift+R
- **Reset Rotation**: Return to original orientation

## Annotations and Markup

### Annotation Tools

Access via the Tools section or annotation toolbar:

#### Text Annotations

- **Highlight**: Mark important text passages
- **Underline**: Underline text for emphasis
- **Strikethrough**: Cross out text
- **Text Notes**: Add popup text comments

#### Drawing Tools

- **Pen**: Freehand drawing and sketching
- **Shapes**: Rectangles, circles, arrows
- **Lines**: Straight lines and connectors

#### Stamps and Signatures

- **Stamps**: Pre-defined stamps (Approved, Reviewed, etc.)
- **Custom Stamps**: Create your own stamp images
- **Digital Signatures**: Sign documents digitally

### Managing Annotations

- **Edit**: Double-click annotations to modify
- **Delete**: Select and press Delete key
- **Properties**: Right-click for color, opacity, and style options
- **Export**: Save annotations separately or flatten into PDF

## Search and Find

### Text Search

1. **Open Search**: Ctrl+F or Tools → Search
2. **Enter Terms**: Type search text in the search box
3. **Navigate Results**: Use Previous/Next buttons or F3/Shift+F3
4. **Search Options**:
   - Case sensitive search
   - Whole words only
   - Regular expressions

### Advanced Search

- **Search in Annotations**: Include annotation text in search
- **Search Scope**: Current document or all open documents
- **Search History**: Recent search terms are remembered

## Customization and Settings

### Theme Customization

Access via Settings → Interface:

#### Theme Modes

- **Light Theme**: Traditional light interface
- **Dark Theme**: Modern dark interface for low-light environments
- **Auto Theme**: Follows system theme settings

#### Advanced Theme Options

- **Accent Colors**: Customize the primary accent color
- **Transparency**: Adjust window transparency (0-20%)
- **Visual Effects**: Enable/disable blur and shadow effects
- **Animations**: Control smooth transitions and animations

### Interface Settings

- **Default Zoom**: Set preferred zoom level for new documents
- **Panel Visibility**: Choose which panels to show by default
- **Toolbar Customization**: Show/hide specific toolbar sections
- **Status Bar**: Configure status bar information display

### Performance Settings

- **Cache Size**: Adjust memory usage for page caching (50-1000 MB)
- **Preload Range**: Number of pages to preload (1-10 pages)
- **Memory Monitoring**: Display memory usage in status bar
- **Hardware Acceleration**: Enable GPU acceleration if available

### Export Settings

- **Default Format**: PNG, JPEG, or TIFF for page exports
- **Image Quality**: Compression quality for exported images (1-100%)
- **Resolution**: DPI for exported images

## Keyboard Shortcuts

### File Operations

- **Ctrl+O**: Open document
- **Ctrl+W**: Close current tab
- **Ctrl+T**: New tab
- **Ctrl+P**: Print document
- **Ctrl+E**: Export document
- **Ctrl+I**: Document information
- **Ctrl+Q**: Quit application

### Navigation

- **Page Up/Down**: Navigate pages
- **Home/End**: First/last page
- **Ctrl+G**: Go to specific page
- **F5**: Refresh/reload document

### View Controls

- **Ctrl++**: Zoom in
- **Ctrl+-**: Zoom out
- **Ctrl+0**: Fit to window
- **Ctrl+1**: Actual size
- **F11**: Full screen mode
- **Ctrl+R**: Rotate clockwise
- **Ctrl+Shift+R**: Rotate counter-clockwise

### Search and Find

- **Ctrl+F**: Open search
- **F3**: Find next
- **Shift+F3**: Find previous
- **Escape**: Close search

### Interface

- **Ctrl+,**: Open settings
- **F1**: Help
- **Ctrl+Shift+T**: Toggle theme
- **Ctrl+B**: Toggle bookmarks panel
- **Ctrl+Shift+O**: Toggle outline panel

## Troubleshooting

### Common Issues

#### Document Won't Open

- **Check File Format**: Ensure the file is a valid PDF
- **File Permissions**: Verify you have read access to the file
- **Corrupted File**: Try opening the file in another PDF viewer
- **Memory Issues**: Close other applications to free up memory

#### Performance Issues

- **Reduce Cache Size**: Lower the cache size in settings
- **Disable Effects**: Turn off visual effects and animations
- **Close Unused Tabs**: Limit the number of open documents
- **Update Graphics Drivers**: Ensure graphics drivers are current

#### Display Problems

- **Zoom Issues**: Reset zoom to 100% (Ctrl+1)
- **Rendering Problems**: Try toggling hardware acceleration
- **Theme Issues**: Switch between light and dark themes
- **Font Problems**: Check system font installation

#### Annotation Issues

- **Can't Edit**: Ensure the PDF allows annotations
- **Missing Annotations**: Check if annotations are hidden
- **Export Problems**: Try flattening annotations before export

### Getting Help

- **Built-in Help**: Press F1 for context-sensitive help
- **User Manual**: This document contains comprehensive information
- **Log Files**: Check application logs for error details
- **Support**: Contact support with log files and error descriptions

### System Requirements

- **Operating System**: Windows 10/11, macOS 10.15+, or Linux
- **Memory**: Minimum 4GB RAM, 8GB recommended
- **Storage**: 100MB for application, additional space for cache
- **Graphics**: DirectX 11 compatible graphics card recommended

### Logging and Diagnostics

- **Log Location**: Application logs are stored in the user's AppData folder
- **Log Levels**: Debug, Info, Warning, Error, Critical
- **Performance Monitoring**: Built-in performance timers for operations
- **Error Reporting**: Comprehensive error handling with user notifications

---

*PDF Viewer v1.0 - Modern PDF viewing with Microsoft Office-style interface*
