#include "RibbonInterface.h"
#include "MainWindow.h"
#include <QPainter>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QStackedWidget>
#include <QButtonGroup>
#include <QPropertyAnimation>
#include <QGraphicsDropShadowEffect>
#include <QTimer>
#include <QResizeEvent>

RibbonInterface::RibbonInterface(MainWindow* parent)
    : QWidget(parent)
    , m_mainWindow(parent)
    , m_mainLayout(nullptr)
    , m_tabLayout(nullptr)
    , m_contentStack(nullptr)
    , m_sectionButtonGroup(nullptr)
    , m_quickAccessLayout(nullptr)
    , m_currentSection("Home")
    , m_hasDocument(false)
    , m_currentPage(1)
    , m_totalPages(1)
    , m_zoomFactor(1.0)
    , m_compactMode(false)
{
    setupUI();
    createSectionTabs();
    createQuickAccessToolbar();
    createRibbonGroups();
    
    // Set initial section
    setCurrentSection("Home");
}

RibbonInterface::~RibbonInterface()
{
    // Cleanup ribbon groups
    for (auto& groupList : m_ribbonGroups) {
        qDeleteAll(groupList);
    }
}

void RibbonInterface::setupUI()
{
    setFixedHeight(RIBBON_HEIGHT);

    // Enhanced ribbon styling with modern Office-like appearance
    setStyleSheet(
        "RibbonInterface {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 #ffffff, stop:0.5 #f8f9fa, stop:1 #e9ecef);"
        "    border-bottom: 2px solid #0078d4;"
        "}"
        "QWidget#RibbonTabWidget {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 #ffffff, stop:1 #f1f3f4);"
        "    border-bottom: 1px solid #d1d5db;"
        "}"
    );

    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);

    // Create enhanced tab widget for section tabs
    QWidget* tabWidget = new QWidget();
    tabWidget->setObjectName("RibbonTabWidget");
    tabWidget->setFixedHeight(TAB_HEIGHT + 5); // Slightly taller for better appearance
    m_tabLayout = new QHBoxLayout(tabWidget);
    m_tabLayout->setContentsMargins(15, 2, 15, 0);
    m_tabLayout->setSpacing(1);

    // Create content stack with enhanced styling
    m_contentStack = new QStackedWidget();
    m_contentStack->setStyleSheet(
        "QStackedWidget {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 #fafbfc, stop:1 #f0f2f5);"
        "    border: none;"
        "}"
    );

    m_mainLayout->addWidget(tabWidget);
    m_mainLayout->addWidget(m_contentStack);

    // Create button group for section tabs with enhanced behavior
    m_sectionButtonGroup = new QButtonGroup(this);
    m_sectionButtonGroup->setExclusive(true);
    connect(m_sectionButtonGroup, QOverload<QAbstractButton*>::of(&QButtonGroup::buttonClicked),
            this, &RibbonInterface::onSectionButtonClicked);
}

void RibbonInterface::createSectionTabs()
{
    QStringList sections = {"Home", "File", "View", "Tools", "Annotate", "Review"};
    
    for (const QString& section : sections) {
        ElaPushButton* button = new ElaPushButton(section, this);
        button->setCheckable(true);
        button->setFixedHeight(TAB_HEIGHT - 4);
        button->setProperty("section", section);
        
        // Enhanced Office-style tab button styling
        button->setStyleSheet(
            "ElaPushButton {"
            "    background: transparent;"
            "    border: none;"
            "    border-radius: 0px;"
            "    padding: 6px 16px 4px 16px;"
            "    font-size: 13px;"
            "    font-weight: 400;"
            "    color: #323130;"
            "    margin: 0px 1px;"
            "}"
            "ElaPushButton:hover:!checked {"
            "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "        stop:0 rgba(0, 120, 212, 0.05), stop:1 rgba(0, 120, 212, 0.1));"
            "    color: #0078d4;"
            "    border-bottom: 2px solid rgba(0, 120, 212, 0.3);"
            "}"
            "ElaPushButton:checked {"
            "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "        stop:0 #ffffff, stop:1 #fafbfc);"
            "    color: #0078d4;"
            "    border-bottom: 3px solid #0078d4;"
            "    font-weight: 500;"
            "}"
            "ElaPushButton:checked:hover {"
            "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "        stop:0 #ffffff, stop:1 #f8f9fa);"
            "}"
            "ElaPushButton:pressed {"
            "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "        stop:0 rgba(0, 120, 212, 0.1), stop:1 rgba(0, 120, 212, 0.15));"
            "    color: #106ebe;"
            "}"
        );
        
        m_sectionButtons[section] = button;
        m_sectionButtonGroup->addButton(button);
        m_tabLayout->addWidget(button);
    }
    
    m_tabLayout->addStretch();
}

void RibbonInterface::createQuickAccessToolbar()
{
    // Quick access toolbar in the title bar area
    m_quickAccessLayout = new QHBoxLayout();
    m_quickAccessLayout->setSpacing(2);
    
    // Add common quick access buttons
    QStringList quickActions = {"Open", "Save", "Print", "Undo", "Redo"};
    QList<ElaIconType::IconName> quickIcons = {
        ElaIconType::FolderOpen, ElaIconType::FloppyDisk, ElaIconType::Print,
        ElaIconType::ArrowRotateLeft, ElaIconType::ArrowRotateRight
    };
    
    for (int i = 0; i < quickActions.size(); ++i) {
        ElaPushButton* button = new ElaPushButton(this);
        button->setIcon(ElaIcon::getInstance()->getElaIcon(quickIcons[i], 16));
        button->setFixedSize(24, 24);
        button->setToolTip(quickActions[i]);
        button->setProperty("action", quickActions[i]);
        
        button->setStyleSheet(
            "ElaPushButton {"
            "    background: transparent;"
            "    border: 1px solid transparent;"
            "    border-radius: 3px;"
            "}"
            "ElaPushButton:hover {"
            "    background: rgba(0, 0, 0, 0.1);"
            "    border: 1px solid rgba(0, 0, 0, 0.2);"
            "}"
        );
        
        connect(button, &ElaPushButton::clicked, this, &RibbonInterface::onQuickAccessClicked);
        
        m_quickAccessButtons.append(button);
        m_quickAccessLayout->addWidget(button);
    }
}

void RibbonInterface::createRibbonGroups()
{
    createHomeSection();
    createFileSection();
    createViewSection();
    createToolsSection();
    createAnnotateSection();
    createReviewSection();
}

void RibbonInterface::createHomeSection()
{
    QWidget* homeWidget = new QWidget();
    QHBoxLayout* homeLayout = new QHBoxLayout(homeWidget);
    homeLayout->setContentsMargins(10, 5, 10, 5);
    homeLayout->setSpacing(GROUP_SPACING);
    
    // File group
    RibbonGroup* fileGroup = new RibbonGroup(tr("File"), homeWidget);
    fileGroup->addLargeButton(tr("Open"), ElaIconType::FolderOpen, tr("Open PDF document (Ctrl+O)"));
    fileGroup->addSmallButton(tr("Recent"), ElaIconType::Clock, tr("Recent documents"));
    fileGroup->addSmallButton(tr("Close"), ElaIconType::Xmark, tr("Close document (Ctrl+W)"));
    
    // View group
    RibbonGroup* viewGroup = new RibbonGroup(tr("View"), homeWidget);
    viewGroup->addLargeButton(tr("Zoom In"), ElaIconType::MagnifyingGlassPlus, tr("Zoom in (Ctrl++)"));
    viewGroup->addSmallButton(tr("Zoom Out"), ElaIconType::MagnifyingGlassMinus, tr("Zoom out (Ctrl+-)"));
    viewGroup->addSmallButton(tr("Fit Width"), ElaIconType::ArrowsMaximize, tr("Fit to width (Ctrl+0)"));
    
    // Navigate group
    RibbonGroup* navGroup = new RibbonGroup(tr("Navigate"), homeWidget);
    navGroup->addLargeButton(tr("Search"), ElaIconType::MagnifyingGlass, tr("Find text (Ctrl+F)"));
    navGroup->addSmallButton(tr("Previous"), ElaIconType::ChevronLeft, tr("Previous page"));
    navGroup->addSmallButton(tr("Next"), ElaIconType::ChevronRight, tr("Next page"));
    
    homeLayout->addWidget(fileGroup);
    homeLayout->addWidget(viewGroup);
    homeLayout->addWidget(navGroup);
    homeLayout->addStretch();
    
    m_sectionWidgets["Home"] = homeWidget;
    m_contentStack->addWidget(homeWidget);
}

void RibbonInterface::createFileSection()
{
    QWidget* fileWidget = new QWidget();
    QHBoxLayout* fileLayout = new QHBoxLayout(fileWidget);
    fileLayout->setContentsMargins(10, 5, 10, 5);
    fileLayout->setSpacing(GROUP_SPACING);
    
    // Open group
    RibbonGroup* openGroup = new RibbonGroup(tr("Open"), fileWidget);
    openGroup->addLargeButton(tr("Open"), ElaIconType::FolderOpen, tr("Open PDF document"));
    openGroup->addSmallButton(tr("Browse"), ElaIconType::MagnifyingGlass, tr("Browse for files"));
    openGroup->addSmallButton(tr("Recent"), ElaIconType::Clock, tr("Recent documents"));
    
    // Save group
    RibbonGroup* saveGroup = new RibbonGroup(tr("Export"), fileWidget);
    saveGroup->addLargeButton(tr("Export"), ElaIconType::FileExport, tr("Export document"));
    saveGroup->addSmallButton(tr("PDF"), ElaIconType::FilePdf, tr("Export as PDF"));
    saveGroup->addSmallButton(tr("Image"), ElaIconType::FileImage, tr("Export as image"));
    
    // Print group
    RibbonGroup* printGroup = new RibbonGroup(tr("Print"), fileWidget);
    printGroup->addLargeButton(tr("Print"), ElaIconType::Print, tr("Print document (Ctrl+P)"));
    printGroup->addSmallButton(tr("Preview"), ElaIconType::Eye, tr("Print preview"));
    printGroup->addSmallButton(tr("Setup"), ElaIconType::Gear, tr("Print setup"));
    
    fileLayout->addWidget(openGroup);
    fileLayout->addWidget(saveGroup);
    fileLayout->addWidget(printGroup);
    fileLayout->addStretch();
    
    m_sectionWidgets["File"] = fileWidget;
    m_contentStack->addWidget(fileWidget);
}

void RibbonInterface::createViewSection()
{
    QWidget* viewWidget = new QWidget();
    QHBoxLayout* viewLayout = new QHBoxLayout(viewWidget);
    viewLayout->setContentsMargins(10, 5, 10, 5);
    viewLayout->setSpacing(GROUP_SPACING);
    
    // Zoom group
    RibbonGroup* zoomGroup = new RibbonGroup(tr("Zoom"), viewWidget);
    zoomGroup->addLargeButton(tr("Fit Page"), ElaIconType::ArrowsMaximize, tr("Fit page to window"));
    zoomGroup->addSmallButton(tr("Zoom In"), ElaIconType::MagnifyingGlassPlus, tr("Zoom in"));
    zoomGroup->addSmallButton(tr("Zoom Out"), ElaIconType::MagnifyingGlassMinus, tr("Zoom out"));
    
    // Layout group
    RibbonGroup* layoutGroup = new RibbonGroup(tr("Layout"), viewWidget);
    layoutGroup->addLargeButton(tr("Single Page"), ElaIconType::File, tr("Single page view"));
    layoutGroup->addSmallButton(tr("Continuous"), ElaIconType::Bars, tr("Continuous view"));
    layoutGroup->addSmallButton(tr("Facing"), ElaIconType::BookOpen, tr("Facing pages"));
    
    // Panels group
    RibbonGroup* panelsGroup = new RibbonGroup(tr("Panels"), viewWidget);
    panelsGroup->addLargeButton(tr("Thumbnails"), ElaIconType::TableCells, tr("Show thumbnails"));
    panelsGroup->addSmallButton(tr("Outline"), ElaIconType::ListUl, tr("Document outline"));
    panelsGroup->addSmallButton(tr("Search"), ElaIconType::MagnifyingGlass, tr("Search results"));
    
    viewLayout->addWidget(zoomGroup);
    viewLayout->addWidget(layoutGroup);
    viewLayout->addWidget(panelsGroup);
    viewLayout->addStretch();
    
    m_sectionWidgets["View"] = viewWidget;
    m_contentStack->addWidget(viewWidget);
}

void RibbonInterface::createToolsSection()
{
    QWidget* toolsWidget = new QWidget();
    QHBoxLayout* toolsLayout = new QHBoxLayout(toolsWidget);
    toolsLayout->setContentsMargins(10, 5, 10, 5);
    toolsLayout->setSpacing(GROUP_SPACING);
    
    // Search group
    RibbonGroup* searchGroup = new RibbonGroup(tr("Search"), toolsWidget);
    searchGroup->addLargeButton(tr("Find"), ElaIconType::MagnifyingGlass, tr("Find text"));
    searchGroup->addSmallButton(tr("Find Next"), ElaIconType::ChevronDown, tr("Find next"));
    searchGroup->addSmallButton(tr("Find Previous"), ElaIconType::ChevronUp, tr("Find previous"));
    
    // Rotate group
    RibbonGroup* rotateGroup = new RibbonGroup(tr("Rotate"), toolsWidget);
    rotateGroup->addLargeButton(tr("Rotate Right"), ElaIconType::ArrowRotateRight, tr("Rotate clockwise"));
    rotateGroup->addSmallButton(tr("Rotate Left"), ElaIconType::ArrowRotateLeft, tr("Rotate counter-clockwise"));
    rotateGroup->addSmallButton(tr("Reset"), ElaIconType::ArrowsRotate, tr("Reset rotation"));
    
    // Utilities group
    RibbonGroup* utilsGroup = new RibbonGroup(tr("Utilities"), toolsWidget);
    utilsGroup->addLargeButton(tr("Properties"), ElaIconType::CircleInfo, tr("Document properties"));
    utilsGroup->addSmallButton(tr("Memory"), ElaIconType::ChartLine, tr("Memory usage"));
    utilsGroup->addSmallButton(tr("Settings"), ElaIconType::Gear, tr("Application settings"));
    
    toolsLayout->addWidget(searchGroup);
    toolsLayout->addWidget(rotateGroup);
    toolsLayout->addWidget(utilsGroup);
    toolsLayout->addStretch();
    
    m_sectionWidgets["Tools"] = toolsWidget;
    m_contentStack->addWidget(toolsWidget);
}

void RibbonInterface::createAnnotateSection()
{
    QWidget* annotateWidget = new QWidget();
    QHBoxLayout* annotateLayout = new QHBoxLayout(annotateWidget);
    annotateLayout->setContentsMargins(10, 5, 10, 5);
    annotateLayout->setSpacing(GROUP_SPACING);
    
    // Text group
    RibbonGroup* textGroup = new RibbonGroup(tr("Text"), annotateWidget);
    textGroup->addLargeButton(tr("Highlight"), ElaIconType::Highlighter, tr("Highlight text"));
    textGroup->addSmallButton(tr("Underline"), ElaIconType::Underline, tr("Underline text"));
    textGroup->addSmallButton(tr("Strikeout"), ElaIconType::Strikethrough, tr("Strike through text"));
    
    // Draw group
    RibbonGroup* drawGroup = new RibbonGroup(tr("Draw"), annotateWidget);
    drawGroup->addLargeButton(tr("Pen"), ElaIconType::Pen, tr("Freehand drawing"));
    drawGroup->addSmallButton(tr("Line"), ElaIconType::Minus, tr("Draw line"));
    drawGroup->addSmallButton(tr("Rectangle"), ElaIconType::Square, tr("Draw rectangle"));
    
    // Notes group
    RibbonGroup* notesGroup = new RibbonGroup(tr("Notes"), annotateWidget);
    notesGroup->addLargeButton(tr("Note"), ElaIconType::Note, tr("Add text note"));
    notesGroup->addSmallButton(tr("Stamp"), ElaIconType::Stamp, tr("Add stamp"));
    notesGroup->addSmallButton(tr("Signature"), ElaIconType::Signature, tr("Digital signature"));
    
    annotateLayout->addWidget(textGroup);
    annotateLayout->addWidget(drawGroup);
    annotateLayout->addWidget(notesGroup);
    annotateLayout->addStretch();
    
    m_sectionWidgets["Annotate"] = annotateWidget;
    m_contentStack->addWidget(annotateWidget);
}

void RibbonInterface::createReviewSection()
{
    QWidget* reviewWidget = new QWidget();
    QHBoxLayout* reviewLayout = new QHBoxLayout(reviewWidget);
    reviewLayout->setContentsMargins(10, 5, 10, 5);
    reviewLayout->setSpacing(GROUP_SPACING);
    
    // Comments group
    RibbonGroup* commentsGroup = new RibbonGroup(tr("Comments"), reviewWidget);
    commentsGroup->addLargeButton(tr("New Comment"), ElaIconType::Comment, tr("Add comment"));
    commentsGroup->addSmallButton(tr("Reply"), ElaIconType::Reply, tr("Reply to comment"));
    commentsGroup->addSmallButton(tr("Delete"), ElaIconType::Trash, tr("Delete comment"));
    
    // Review group
    RibbonGroup* reviewGroup = new RibbonGroup(tr("Review"), reviewWidget);
    reviewGroup->addLargeButton(tr("Approve"), ElaIconType::Check, tr("Approve document"));
    reviewGroup->addSmallButton(tr("Reject"), ElaIconType::Xmark, tr("Reject document"));
    reviewGroup->addSmallButton(tr("Complete"), ElaIconType::CheckDouble, tr("Mark complete"));
    
    reviewLayout->addWidget(commentsGroup);
    reviewLayout->addWidget(reviewGroup);
    reviewLayout->addStretch();
    
    m_sectionWidgets["Review"] = reviewWidget;
    m_contentStack->addWidget(reviewWidget);
}

void RibbonInterface::setCurrentSection(const QString& sectionName)
{
    if (m_currentSection == sectionName) return;
    
    m_currentSection = sectionName;
    
    // Update button states
    if (m_sectionButtons.contains(sectionName)) {
        m_sectionButtons[sectionName]->setChecked(true);
    }
    
    // Update content stack
    if (m_sectionWidgets.contains(sectionName)) {
        m_contentStack->setCurrentWidget(m_sectionWidgets[sectionName]);
    }
    
    emit sectionChanged(sectionName);
}

void RibbonInterface::onSectionButtonClicked()
{
    ElaPushButton* button = qobject_cast<ElaPushButton*>(sender());
    if (button) {
        QString section = button->property("section").toString();
        setCurrentSection(section);
    }
}

void RibbonInterface::onQuickAccessClicked()
{
    ElaPushButton* button = qobject_cast<ElaPushButton*>(sender());
    if (button) {
        QString action = button->property("action").toString();
        emit actionTriggered(action);
    }
}

void RibbonInterface::updateDocumentActions(bool hasDocument)
{
    m_hasDocument = hasDocument;
    // Update button states based on document availability
    // This would be implemented to enable/disable relevant buttons
}

void RibbonInterface::paintEvent(QPaintEvent* event)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Draw ribbon background
    QLinearGradient gradient(0, 0, 0, height());
    gradient.setColorAt(0, QColor(248, 249, 250));
    gradient.setColorAt(1, QColor(233, 236, 239));
    
    painter.fillRect(rect(), gradient);
    
    // Draw bottom border
    painter.setPen(QColor(222, 226, 230));
    painter.drawLine(0, height() - 1, width(), height() - 1);
    
    QWidget::paintEvent(event);
}

// RibbonGroup implementation
RibbonGroup::RibbonGroup(const QString& title, QWidget* parent)
    : QWidget(parent)
    , m_title(title)
{
    setupUI();
}

void RibbonGroup::setupUI()
{
    setFixedWidth(120);
    
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(5, 5, 5, 5);
    m_mainLayout->setSpacing(2);
    
    // Button area
    QWidget* buttonWidget = new QWidget();
    m_buttonLayout = new QHBoxLayout(buttonWidget);
    m_buttonLayout->setContentsMargins(0, 0, 0, 0);
    m_buttonLayout->setSpacing(2);
    
    // Title label
    m_titleLabel = new ElaText(m_title, this);
    m_titleLabel->setAlignment(Qt::AlignCenter);
    m_titleLabel->setTextPixelSize(10);
    m_titleLabel->setStyleSheet("color: #6c757d; font-weight: 500;");
    
    m_mainLayout->addWidget(buttonWidget);
    m_mainLayout->addWidget(m_titleLabel);
}

ElaPushButton* RibbonGroup::addLargeButton(const QString& text, ElaIconType::IconName icon, const QString& tooltip)
{
    ElaPushButton* button = new ElaPushButton(text, this);
    button->setIcon(ElaIcon::getInstance()->getElaIcon(icon, 32));
    button->setFixedSize(48, 64);
    button->setToolTip(tooltip);

    // Enhanced styling for large buttons with Office-like appearance
    button->setStyleSheet(
        "ElaPushButton {"
        "    background: transparent;"
        "    border: 1px solid transparent;"
        "    border-radius: 4px;"
        "    text-align: center;"
        "    font-size: 11px;"
        "    font-weight: 400;"
        "    color: #323130;"
        "    padding: 2px;"
        "}"
        "ElaPushButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 rgba(0, 120, 212, 0.08), stop:1 rgba(0, 120, 212, 0.12));"
        "    border: 1px solid rgba(0, 120, 212, 0.4);"
        "    color: #0078d4;"
        "}"
        "ElaPushButton:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 rgba(0, 120, 212, 0.15), stop:1 rgba(0, 120, 212, 0.25));"
        "    border: 1px solid rgba(0, 120, 212, 0.6);"
        "    color: #106ebe;"
        "}"
        "ElaPushButton:disabled {"
        "    color: #a19f9d;"
        "    background: transparent;"
        "    border: 1px solid transparent;"
        "}"
    );

    m_buttons.append(button);
    m_buttonLayout->addWidget(button);

    return button;
}

ElaPushButton* RibbonGroup::addSmallButton(const QString& text, ElaIconType::IconName icon, const QString& tooltip)
{
    ElaPushButton* button = new ElaPushButton(text, this);
    button->setIcon(ElaIcon::getInstance()->getElaIcon(icon, 16));
    button->setFixedSize(32, 32);
    button->setToolTip(tooltip);

    // Enhanced styling for small buttons with Office-like appearance
    button->setStyleSheet(
        "ElaPushButton {"
        "    background: transparent;"
        "    border: 1px solid transparent;"
        "    border-radius: 3px;"
        "    font-size: 10px;"
        "    font-weight: 400;"
        "    color: #323130;"
        "    padding: 1px;"
        "}"
        "ElaPushButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 rgba(0, 120, 212, 0.08), stop:1 rgba(0, 120, 212, 0.12));"
        "    border: 1px solid rgba(0, 120, 212, 0.4);"
        "    color: #0078d4;"
        "}"
        "ElaPushButton:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 rgba(0, 120, 212, 0.15), stop:1 rgba(0, 120, 212, 0.25));"
        "    border: 1px solid rgba(0, 120, 212, 0.6);"
        "    color: #106ebe;"
        "}"
        "ElaPushButton:disabled {"
        "    color: #a19f9d;"
        "    background: transparent;"
        "    border: 1px solid transparent;"
        "}"
    );

    m_buttons.append(button);
    m_buttonLayout->addWidget(button);

    return button;
}

void RibbonGroup::paintEvent(QPaintEvent* event)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Draw group border
    painter.setPen(QColor(222, 226, 230));
    painter.drawRoundedRect(rect().adjusted(1, 1, -1, -1), 4, 4);
    
    QWidget::paintEvent(event);
}

// Responsive design methods
void RibbonInterface::setCompactMode(bool compact)
{
    if (m_compactMode == compact) return;

    m_compactMode = compact;

    // Hide/show sections based on compact mode
    if (compact) {
        // In compact mode, hide less essential sections
        hideSection("Review");
        hideSection("Tools");

        // Make remaining sections more compact
        setFixedHeight(RIBBON_HEIGHT - 20);
    } else {
        // In normal mode, show all sections
        showSection("Review");
        showSection("Tools");

        // Restore normal height
        setFixedHeight(RIBBON_HEIGHT);
    }

    update();
}

bool RibbonInterface::isCompactMode() const
{
    return m_compactMode;
}

void RibbonInterface::showSection(const QString& sectionName)
{
    if (m_sectionButtons.contains(sectionName)) {
        m_sectionButtons[sectionName]->show();

        // Show corresponding content widget
        if (m_sectionWidgets.contains(sectionName)) {
            m_sectionWidgets[sectionName]->show();
        }

        // Show corresponding ribbon groups
        if (m_ribbonGroups.contains(sectionName)) {
            for (RibbonGroupData* groupData : m_ribbonGroups[sectionName]) {
                if (groupData && groupData->widget) {
                    groupData->widget->show();
                }
            }
        }

        updateSectionVisibility();
    }
}

void RibbonInterface::hideSection(const QString& sectionName)
{
    if (m_sectionButtons.contains(sectionName)) {
        m_sectionButtons[sectionName]->hide();

        // Hide corresponding content widget
        if (m_sectionWidgets.contains(sectionName)) {
            m_sectionWidgets[sectionName]->hide();
        }

        // Hide corresponding ribbon groups
        if (m_ribbonGroups.contains(sectionName)) {
            for (RibbonGroupData* groupData : m_ribbonGroups[sectionName]) {
                if (groupData && groupData->widget) {
                    groupData->widget->hide();
                }
            }
        }

        // If hiding current section, switch to Home
        if (m_currentSection == sectionName) {
            setCurrentSection("Home");
        }

        updateSectionVisibility();
    }
}

void RibbonInterface::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);

    // Handle responsive design
    if (width() < 800 && !m_compactMode) {
        setCompactMode(true);
    } else if (width() >= 800 && m_compactMode) {
        setCompactMode(false);
    }
}

void RibbonInterface::updateSectionVisibility()
{
    // Update the visibility of sections based on current state
    for (auto it = m_sectionButtons.begin(); it != m_sectionButtons.end(); ++it) {
        const QString& sectionName = it.key();
        ElaPushButton* button = it.value();

        if (button && button->isVisible()) {
            // Update button appearance if needed
            // Note: ElaPushButton doesn't have setChecked, so we update style instead
            if (sectionName == m_currentSection) {
                button->setProperty("selected", true);
            } else {
                button->setProperty("selected", false);
            }
            button->style()->unpolish(button);
            button->style()->polish(button);
        }
    }

    // Update layout if needed
    if (m_tabLayout) {
        m_tabLayout->update();
    }
}

#include "RibbonInterface.moc"
