#include <QtTest/QtTest>
#include <QJsonObject>
#include <QJsonDocument>
#include <QPainter>
#include <QPixmap>
#include <QPointF>
#include <QRectF>

#include "Annotation.h"

class TestAnnotation : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Base Annotation tests
    void testAnnotationCreation();
    void testAnnotationProperties();
    void testAnnotationSerialization();
    void testAnnotationId();

    // HighlightAnnotation tests
    void testHighlightCreation();
    void testHighlightQuads();
    void testHighlightBoundingRect();
    void testHighlightContains();
    void testHighlightSerialization();

    // NoteAnnotation tests
    void testNoteCreation();
    void testNotePosition();
    void testNoteSerialization();

    // ShapeAnnotation tests
    void testShapeCreation();
    void testShapeTypes();
    void testShapeGeometry();
    void testShapeSerialization();

    // TextAnnotation tests
    void testTextCreation();
    void testTextContent();
    void testTextSerialization();

    // DrawingAnnotation tests
    void testDrawingCreation();
    void testDrawingPaths();
    void testDrawingSerialization();

private:
    void verifyBaseAnnotationProperties(Annotation* annotation);
};

void TestAnnotation::initTestCase()
{
    // Setup for all tests
}

void TestAnnotation::cleanupTestCase()
{
    // Cleanup after all tests
}

void TestAnnotation::init()
{
    // Setup before each test
}

void TestAnnotation::cleanup()
{
    // Cleanup after each test
}

void TestAnnotation::testAnnotationCreation()
{
    auto highlight = std::make_unique<HighlightAnnotation>();
    QVERIFY(highlight != nullptr);
    QCOMPARE(highlight->getType(), AnnotationType::Highlight);
    QVERIFY(!highlight->getId().isEmpty());
    QCOMPARE(highlight->getPageNumber(), -1); // Default page
}

void TestAnnotation::testAnnotationProperties()
{
    auto note = std::make_unique<NoteAnnotation>();
    
    // Test setting properties
    note->setPageNumber(5);
    note->setColor(QColor(255, 0, 0));
    note->setOpacity(0.8);
    note->setAuthor("Test Author");
    note->setContent("Test content");
    
    // Verify properties
    QCOMPARE(note->getPageNumber(), 5);
    QCOMPARE(note->getColor(), QColor(255, 0, 0));
    QCOMPARE(note->getOpacity(), 0.8);
    QCOMPARE(note->getAuthor(), QString("Test Author"));
    QCOMPARE(note->getContent(), QString("Test content"));
}

void TestAnnotation::testAnnotationSerialization()
{
    auto highlight = std::make_unique<HighlightAnnotation>();
    highlight->setPageNumber(2);
    highlight->setColor(QColor(255, 255, 0));
    highlight->setOpacity(0.5);
    highlight->setAuthor("Test User");
    highlight->setContent("Test highlight");
    
    // Add some quads
    QList<QRectF> quads;
    quads.append(QRectF(10, 20, 100, 15));
    quads.append(QRectF(10, 35, 80, 15));
    highlight->setQuads(quads);
    
    // Serialize to JSON
    QJsonObject json = highlight->toJson();
    
    // Verify JSON structure
    QCOMPARE(json["type"].toInt(), static_cast<int>(AnnotationType::Highlight));
    QCOMPARE(json["pageNumber"].toInt(), 2);
    QCOMPARE(json["color"].toString(), QString("#ffff00"));
    QCOMPARE(json["opacity"].toDouble(), 0.5);
    QCOMPARE(json["author"].toString(), QString("Test User"));
    QCOMPARE(json["content"].toString(), QString("Test highlight"));
    
    // Create new annotation from JSON
    auto newHighlight = std::make_unique<HighlightAnnotation>();
    newHighlight->fromJson(json);
    
    // Verify deserialization
    QCOMPARE(newHighlight->getPageNumber(), 2);
    QCOMPARE(newHighlight->getColor(), QColor(255, 255, 0));
    QCOMPARE(newHighlight->getOpacity(), 0.5);
    QCOMPARE(newHighlight->getAuthor(), QString("Test User"));
    QCOMPARE(newHighlight->getContent(), QString("Test highlight"));
    QCOMPARE(newHighlight->getQuads().size(), 2);
}

void TestAnnotation::testAnnotationId()
{
    auto note1 = std::make_unique<NoteAnnotation>();
    auto note2 = std::make_unique<NoteAnnotation>();
    
    // IDs should be unique
    QVERIFY(note1->getId() != note2->getId());
    QVERIFY(!note1->getId().isEmpty());
    QVERIFY(!note2->getId().isEmpty());
    
    // ID should be persistent
    QString originalId = note1->getId();
    note1->setContent("Modified content");
    QCOMPARE(note1->getId(), originalId);
}

void TestAnnotation::testHighlightCreation()
{
    auto highlight = std::make_unique<HighlightAnnotation>();
    QCOMPARE(highlight->getType(), AnnotationType::Highlight);
    QVERIFY(highlight->getQuads().isEmpty());
}

void TestAnnotation::testHighlightQuads()
{
    auto highlight = std::make_unique<HighlightAnnotation>();
    
    // Add quads
    QRectF quad1(10, 20, 100, 15);
    QRectF quad2(10, 35, 80, 15);
    
    highlight->addQuad(quad1);
    highlight->addQuad(quad2);
    
    QCOMPARE(highlight->getQuads().size(), 2);
    QCOMPARE(highlight->getQuads()[0], quad1);
    QCOMPARE(highlight->getQuads()[1], quad2);
    
    // Set quads directly
    QList<QRectF> newQuads;
    newQuads.append(QRectF(50, 60, 120, 18));
    highlight->setQuads(newQuads);
    
    QCOMPARE(highlight->getQuads().size(), 1);
    QCOMPARE(highlight->getQuads()[0], QRectF(50, 60, 120, 18));
}

void TestAnnotation::testHighlightBoundingRect()
{
    auto highlight = std::make_unique<HighlightAnnotation>();
    
    // Empty highlight should have null bounding rect
    QVERIFY(highlight->getBoundingRect().isNull());
    
    // Add quads and test bounding rect
    highlight->addQuad(QRectF(10, 20, 100, 15));
    highlight->addQuad(QRectF(50, 40, 80, 15));
    
    QRectF boundingRect = highlight->getBoundingRect();
    QCOMPARE(boundingRect.left(), 10.0);
    QCOMPARE(boundingRect.top(), 20.0);
    QCOMPARE(boundingRect.right(), 130.0); // 50 + 80
    QCOMPARE(boundingRect.bottom(), 55.0); // 40 + 15
}

void TestAnnotation::testHighlightContains()
{
    auto highlight = std::make_unique<HighlightAnnotation>();
    highlight->addQuad(QRectF(10, 20, 100, 15));
    highlight->addQuad(QRectF(10, 40, 80, 15));
    
    // Points inside quads
    QVERIFY(highlight->contains(QPointF(50, 25)));
    QVERIFY(highlight->contains(QPointF(30, 45)));
    
    // Points outside quads
    QVERIFY(!highlight->contains(QPointF(5, 25)));
    QVERIFY(!highlight->contains(QPointF(50, 60)));
}

void TestAnnotation::testHighlightSerialization()
{
    auto highlight = std::make_unique<HighlightAnnotation>();
    highlight->setPageNumber(1);
    highlight->addQuad(QRectF(10, 20, 100, 15));
    highlight->addQuad(QRectF(10, 35, 80, 15));
    
    QJsonObject json = highlight->toJson();
    QVERIFY(json.contains("quads"));
    
    auto newHighlight = std::make_unique<HighlightAnnotation>();
    newHighlight->fromJson(json);
    
    QCOMPARE(newHighlight->getQuads().size(), 2);
    QCOMPARE(newHighlight->getQuads()[0], QRectF(10, 20, 100, 15));
    QCOMPARE(newHighlight->getQuads()[1], QRectF(10, 35, 80, 15));
}

void TestAnnotation::testNoteCreation()
{
    auto note = std::make_unique<NoteAnnotation>();
    QCOMPARE(note->getType(), AnnotationType::Note);
    QCOMPARE(note->getPosition(), QPointF(0, 0));
}

void TestAnnotation::testNotePosition()
{
    auto note = std::make_unique<NoteAnnotation>();

    QPointF position(100, 200);
    note->setPosition(position);

    QCOMPARE(note->getPosition(), position);
    QCOMPARE(note->getBoundingRect().topLeft(), position);
}

void TestAnnotation::testNoteSerialization()
{
    auto note = std::make_unique<NoteAnnotation>();
    note->setPosition(QPointF(150, 250));
    note->setContent("Test note content");

    QJsonObject json = note->toJson();
    QVERIFY(json.contains("x"));
    QVERIFY(json.contains("y"));
    
    auto newNote = std::make_unique<NoteAnnotation>();
    newNote->fromJson(json);
    
    QCOMPARE(newNote->getPosition(), QPointF(150, 250));
    QCOMPARE(newNote->getContent(), QString("Test note content"));
}

void TestAnnotation::testShapeCreation()
{
    auto rect = std::make_unique<ShapeAnnotation>(ShapeAnnotation::ShapeType::Rectangle);
    QCOMPARE(rect->getType(), AnnotationType::Rectangle);
    QCOMPARE(rect->getShapeType(), ShapeAnnotation::ShapeType::Rectangle);

    auto circle = std::make_unique<ShapeAnnotation>(ShapeAnnotation::ShapeType::Circle);
    QCOMPARE(circle->getType(), AnnotationType::Circle);
    QCOMPARE(circle->getShapeType(), ShapeAnnotation::ShapeType::Circle);
}

void TestAnnotation::testShapeTypes()
{
    auto rect = std::make_unique<ShapeAnnotation>(ShapeAnnotation::ShapeType::Rectangle);
    auto circle = std::make_unique<ShapeAnnotation>(ShapeAnnotation::ShapeType::Circle);
    auto arrow = std::make_unique<ShapeAnnotation>(ShapeAnnotation::ShapeType::Arrow);
    
    QCOMPARE(rect->getShapeType(), ShapeAnnotation::ShapeType::Rectangle);
    QCOMPARE(circle->getShapeType(), ShapeAnnotation::ShapeType::Circle);
    QCOMPARE(arrow->getShapeType(), ShapeAnnotation::ShapeType::Arrow);
}

void TestAnnotation::testShapeGeometry()
{
    auto shape = std::make_unique<ShapeAnnotation>(ShapeAnnotation::ShapeType::Rectangle);
    
    QRectF rect(50, 100, 200, 150);
    shape->setRect(rect);
    
    QCOMPARE(shape->getRect(), rect);
    QCOMPARE(shape->getBoundingRect(), rect);
}

void TestAnnotation::testShapeSerialization()
{
    auto shape = std::make_unique<ShapeAnnotation>(ShapeAnnotation::ShapeType::Circle);
    shape->setRect(QRectF(25, 50, 100, 100));
    
    QJsonObject json = shape->toJson();
    QVERIFY(json.contains("shapeType"));
    QVERIFY(json.contains("x"));
    QVERIFY(json.contains("y"));
    QVERIFY(json.contains("width"));
    QVERIFY(json.contains("height"));
    
    auto newShape = std::make_unique<ShapeAnnotation>(ShapeAnnotation::ShapeType::Rectangle);
    newShape->fromJson(json);
    
    QCOMPARE(newShape->getShapeType(), ShapeAnnotation::ShapeType::Circle);
    QCOMPARE(newShape->getRect(), QRectF(25, 50, 100, 100));
}

void TestAnnotation::testTextCreation()
{
    auto text = std::make_unique<TextAnnotation>();
    QCOMPARE(text->getType(), AnnotationType::Text);
    QVERIFY(text->getText().isEmpty());
}

void TestAnnotation::testTextContent()
{
    auto text = std::make_unique<TextAnnotation>();
    
    QString content = "This is test text content";
    text->setText(content);
    
    QCOMPARE(text->getText(), content);
}

void TestAnnotation::testTextSerialization()
{
    auto text = std::make_unique<TextAnnotation>();
    text->setText("Test text annotation");
    text->setRect(QRectF(10, 20, 200, 50));
    
    QJsonObject json = text->toJson();
    QVERIFY(json.contains("text"));
    QVERIFY(json.contains("x"));
    QVERIFY(json.contains("y"));
    QVERIFY(json.contains("width"));
    QVERIFY(json.contains("height"));
    
    auto newText = std::make_unique<TextAnnotation>();
    newText->fromJson(json);
    
    QCOMPARE(newText->getText(), QString("Test text annotation"));
    QCOMPARE(newText->getRect(), QRectF(10, 20, 200, 50));
}

void TestAnnotation::testDrawingCreation()
{
    auto drawing = std::make_unique<DrawingAnnotation>();
    QCOMPARE(drawing->getType(), AnnotationType::Drawing);
    QVERIFY(drawing->getPath().isEmpty());
}

void TestAnnotation::testDrawingPaths()
{
    auto drawing = std::make_unique<DrawingAnnotation>();

    QPainterPath path;
    path.moveTo(10, 20);
    path.lineTo(50, 60);
    path.lineTo(90, 30);

    drawing->setPath(path);

    QCOMPARE(drawing->getPath(), path);
    QVERIFY(!drawing->getPath().isEmpty());
}

void TestAnnotation::testDrawingSerialization()
{
    auto drawing = std::make_unique<DrawingAnnotation>();

    QPainterPath path;
    path.moveTo(10, 20);
    path.lineTo(50, 60);
    drawing->setPath(path);

    QJsonObject json = drawing->toJson();
    QVERIFY(json.contains("path"));

    auto newDrawing = std::make_unique<DrawingAnnotation>();
    newDrawing->fromJson(json);

    QVERIFY(!newDrawing->getPath().isEmpty());
}

void TestAnnotation::verifyBaseAnnotationProperties(Annotation* annotation)
{
    QVERIFY(annotation != nullptr);
    QVERIFY(!annotation->getId().isEmpty());
    QVERIFY(annotation->getCreationDate().isValid());
    QVERIFY(annotation->getModificationDate().isValid());
}

QTEST_MAIN(TestAnnotation)
#include "test_annotation.moc"
