#include "BookmarkDialog.h"
#include <QInputDialog>
#include <QMessageBox>
#include <QFileInfo>
#include <QDateTime>
#include <QSplitter>
#include <QGroupBox>
#include <QGridLayout>

BookmarkDialog::BookmarkDialog(QSettings* settings, QWidget *parent)
    : QDialog(parent)
    , m_settings(settings)
    , m_currentPage(0)
    , m_currentZoom(1.0)
{
    setWindowTitle(tr("Bookmark Manager"));
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);
    resize(500, 400);
    
    setupUi();
    loadBookmarks();
    updateButtonStates();
}

void BookmarkDialog::setupUi()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(8);
    m_mainLayout->setContentsMargins(12, 12, 12, 12);

    // Title
    QLabel* titleLabel = new QLabel(tr("Bookmarks"), this);
    titleLabel->setStyleSheet("QLabel { font-size: 14px; font-weight: bold; color: #333; }");
    m_mainLayout->addWidget(titleLabel);

    // Bookmarks list
    m_bookmarksList = new QListWidget(this);
    m_bookmarksList->setAlternatingRowColors(true);
    m_bookmarksList->setSelectionMode(QAbstractItemView::SingleSelection);
    m_bookmarksList->setStyleSheet(
        "QListWidget { "
        "    border: 1px solid #d0d0d0; "
        "    border-radius: 4px; "
        "    background: white; "
        "    font-size: 11px; "
        "} "
        "QListWidget::item { "
        "    padding: 8px; "
        "    border-bottom: 1px solid #f0f0f0; "
        "} "
        "QListWidget::item:selected { "
        "    background: #e3f2fd; "
        "    color: #1976d2; "
        "} "
        "QListWidget::item:hover { "
        "    background: #f5f5f5; "
        "}"
    );
    
    connect(m_bookmarksList, &QListWidget::itemDoubleClicked, this, &BookmarkDialog::onBookmarkDoubleClicked);
    connect(m_bookmarksList, &QListWidget::itemSelectionChanged, this, &BookmarkDialog::onSelectionChanged);
    
    m_mainLayout->addWidget(m_bookmarksList);

    // Button layout
    m_buttonLayout = new QHBoxLayout();
    m_buttonLayout->setSpacing(8);

    m_addButton = new QPushButton(tr("Add"), this);
    m_addButton->setToolTip(tr("Add bookmark for current page"));
    m_addButton->setStyleSheet(
        "QPushButton { "
        "    padding: 6px 12px; "
        "    border: 1px solid #28a745; "
        "    border-radius: 4px; "
        "    background: #28a745; "
        "    color: white; "
        "    font-weight: bold; "
        "} "
        "QPushButton:hover { "
        "    background: #218838; "
        "    border: 1px solid #218838; "
        "} "
        "QPushButton:pressed { "
        "    background: #1e7e34; "
        "}"
    );
    connect(m_addButton, &QPushButton::clicked, this, &BookmarkDialog::onAddBookmark);

    m_editButton = new QPushButton(tr("Edit"), this);
    m_editButton->setToolTip(tr("Edit selected bookmark"));
    m_editButton->setStyleSheet(
        "QPushButton { "
        "    padding: 6px 12px; "
        "    border: 1px solid #ffc107; "
        "    border-radius: 4px; "
        "    background: #ffc107; "
        "    color: #212529; "
        "    font-weight: bold; "
        "} "
        "QPushButton:hover:enabled { "
        "    background: #e0a800; "
        "    border: 1px solid #e0a800; "
        "} "
        "QPushButton:disabled { "
        "    background: #f8f9fa; "
        "    border: 1px solid #dee2e6; "
        "    color: #6c757d; "
        "}"
    );
    connect(m_editButton, &QPushButton::clicked, this, &BookmarkDialog::onEditBookmark);

    m_deleteButton = new QPushButton(tr("Delete"), this);
    m_deleteButton->setToolTip(tr("Delete selected bookmark"));
    m_deleteButton->setStyleSheet(
        "QPushButton { "
        "    padding: 6px 12px; "
        "    border: 1px solid #dc3545; "
        "    border-radius: 4px; "
        "    background: #dc3545; "
        "    color: white; "
        "    font-weight: bold; "
        "} "
        "QPushButton:hover:enabled { "
        "    background: #c82333; "
        "    border: 1px solid #c82333; "
        "} "
        "QPushButton:disabled { "
        "    background: #f8f9fa; "
        "    border: 1px solid #dee2e6; "
        "    color: #6c757d; "
        "}"
    );
    connect(m_deleteButton, &QPushButton::clicked, this, &BookmarkDialog::onDeleteBookmark);

    m_goToButton = new QPushButton(tr("Go To"), this);
    m_goToButton->setToolTip(tr("Navigate to selected bookmark"));
    m_goToButton->setStyleSheet(
        "QPushButton { "
        "    padding: 6px 12px; "
        "    border: 1px solid #007bff; "
        "    border-radius: 4px; "
        "    background: #007bff; "
        "    color: white; "
        "    font-weight: bold; "
        "} "
        "QPushButton:hover:enabled { "
        "    background: #0056b3; "
        "    border: 1px solid #0056b3; "
        "} "
        "QPushButton:disabled { "
        "    background: #f8f9fa; "
        "    border: 1px solid #dee2e6; "
        "    color: #6c757d; "
        "}"
    );
    connect(m_goToButton, &QPushButton::clicked, this, &BookmarkDialog::onGoToBookmark);

    m_closeButton = new QPushButton(tr("Close"), this);
    m_closeButton->setDefault(true);
    m_closeButton->setStyleSheet(
        "QPushButton { "
        "    padding: 6px 12px; "
        "    border: 1px solid #6c757d; "
        "    border-radius: 4px; "
        "    background: #6c757d; "
        "    color: white; "
        "} "
        "QPushButton:hover { "
        "    background: #545b62; "
        "    border: 1px solid #545b62; "
        "}"
    );
    connect(m_closeButton, &QPushButton::clicked, this, &QDialog::accept);

    m_buttonLayout->addWidget(m_addButton);
    m_buttonLayout->addWidget(m_editButton);
    m_buttonLayout->addWidget(m_deleteButton);
    m_buttonLayout->addStretch();
    m_buttonLayout->addWidget(m_goToButton);
    m_buttonLayout->addWidget(m_closeButton);

    m_mainLayout->addLayout(m_buttonLayout);
}

void BookmarkDialog::setCurrentDocument(const QString& filePath, int currentPage, double currentZoom)
{
    m_currentFilePath = filePath;
    m_currentPage = currentPage;
    m_currentZoom = currentZoom;
    
    // Enable/disable add button based on whether we have a document
    m_addButton->setEnabled(!filePath.isEmpty());
}

void BookmarkDialog::refreshBookmarks()
{
    loadBookmarks();
    updateButtonStates();
}

void BookmarkDialog::loadBookmarks()
{
    m_bookmarks.clear();
    m_bookmarksList->clear();

    if (!m_settings) return;

    // Load all bookmarks from settings
    m_settings->beginGroup("bookmarks");
    QStringList fileGroups = m_settings->childGroups();
    
    for (const QString& fileGroup : fileGroups) {
        m_settings->beginGroup(fileGroup);
        QStringList bookmarkNames = m_settings->allKeys();
        
        for (const QString& bookmarkName : bookmarkNames) {
            QString bookmarkValue = m_settings->value(bookmarkName).toString();
            QStringList parts = bookmarkValue.split('|');
            
            if (parts.size() >= 3) {
                Bookmark bookmark;
                bookmark.name = bookmarkName;
                bookmark.filePath = parts[0];
                bookmark.pageNumber = parts[1].toInt();
                bookmark.zoomFactor = parts[2].toDouble();
                bookmark.fileName = QFileInfo(bookmark.filePath).fileName();
                
                // Try to get creation date if available
                if (parts.size() >= 4) {
                    bookmark.created = QDateTime::fromString(parts[3], Qt::ISODate);
                } else {
                    bookmark.created = QDateTime::currentDateTime();
                }
                
                m_bookmarks.append(bookmark);
                
                // Create list item
                QListWidgetItem* item = new QListWidgetItem();
                QString displayText = QString("%1 - %2 (Page %3)")
                    .arg(bookmark.name)
                    .arg(bookmark.fileName)
                    .arg(bookmark.pageNumber + 1);
                item->setText(displayText);
                item->setData(Qt::UserRole, m_bookmarks.size() - 1); // Store index
                item->setToolTip(QString("File: %1\nPage: %2\nZoom: %3%\nCreated: %4")
                    .arg(bookmark.filePath)
                    .arg(bookmark.pageNumber + 1)
                    .arg(qRound(bookmark.zoomFactor * 100))
                    .arg(bookmark.created.toString()));
                
                m_bookmarksList->addItem(item);
            }
        }
        
        m_settings->endGroup();
    }
    
    m_settings->endGroup();
}

void BookmarkDialog::onAddBookmark()
{
    if (m_currentFilePath.isEmpty()) return;

    bool ok;
    QString name = QInputDialog::getText(this, tr("Add Bookmark"),
                                       tr("Bookmark name:"), QLineEdit::Normal,
                                       tr("Page %1").arg(m_currentPage + 1), &ok);
    if (!ok || name.isEmpty()) return;

    // Check if bookmark name already exists for this file
    QString fileName = QFileInfo(m_currentFilePath).fileName();
    for (const Bookmark& existing : m_bookmarks) {
        if (existing.fileName == fileName && existing.name == name) {
            QMessageBox::warning(this, tr("Duplicate Bookmark"),
                tr("A bookmark with this name already exists for this document."));
            return;
        }
    }

    Bookmark bookmark;
    bookmark.name = name;
    bookmark.filePath = m_currentFilePath;
    bookmark.pageNumber = m_currentPage;
    bookmark.zoomFactor = m_currentZoom;
    bookmark.fileName = fileName;
    bookmark.created = QDateTime::currentDateTime();

    saveBookmark(bookmark);
    loadBookmarks();
    updateButtonStates();
}

void BookmarkDialog::onEditBookmark()
{
    QListWidgetItem* currentItem = m_bookmarksList->currentItem();
    if (!currentItem) return;

    int index = currentItem->data(Qt::UserRole).toInt();
    if (index < 0 || index >= m_bookmarks.size()) return;

    Bookmark& bookmark = m_bookmarks[index];

    bool ok;
    QString newName = QInputDialog::getText(this, tr("Edit Bookmark"),
                                          tr("Bookmark name:"), QLineEdit::Normal,
                                          bookmark.name, &ok);
    if (!ok || newName.isEmpty() || newName == bookmark.name) return;

    // Check if new name already exists for this file
    for (int i = 0; i < m_bookmarks.size(); ++i) {
        if (i != index && m_bookmarks[i].fileName == bookmark.fileName && m_bookmarks[i].name == newName) {
            QMessageBox::warning(this, tr("Duplicate Bookmark"),
                tr("A bookmark with this name already exists for this document."));
            return;
        }
    }

    // Delete old bookmark and save new one
    deleteBookmark(getBookmarkKey(bookmark));
    bookmark.name = newName;
    saveBookmark(bookmark);

    loadBookmarks();
    updateButtonStates();
}

void BookmarkDialog::onDeleteBookmark()
{
    QListWidgetItem* currentItem = m_bookmarksList->currentItem();
    if (!currentItem) return;

    int index = currentItem->data(Qt::UserRole).toInt();
    if (index < 0 || index >= m_bookmarks.size()) return;

    const Bookmark& bookmark = m_bookmarks[index];

    int ret = QMessageBox::question(this, tr("Delete Bookmark"),
                                  tr("Are you sure you want to delete the bookmark '%1'?").arg(bookmark.name),
                                  QMessageBox::Yes | QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        deleteBookmark(getBookmarkKey(bookmark));
        loadBookmarks();
        updateButtonStates();
    }
}

void BookmarkDialog::onGoToBookmark()
{
    QListWidgetItem* currentItem = m_bookmarksList->currentItem();
    if (!currentItem) return;

    int index = currentItem->data(Qt::UserRole).toInt();
    if (index < 0 || index >= m_bookmarks.size()) return;

    const Bookmark& bookmark = m_bookmarks[index];
    emit bookmarkSelected(bookmark.filePath, bookmark.pageNumber, bookmark.zoomFactor);
    accept();
}

void BookmarkDialog::onBookmarkDoubleClicked(QListWidgetItem* item)
{
    Q_UNUSED(item);
    onGoToBookmark();
}

void BookmarkDialog::onSelectionChanged()
{
    updateButtonStates();
}

void BookmarkDialog::saveBookmark(const Bookmark& bookmark)
{
    if (!m_settings) return;

    QString key = getBookmarkKey(bookmark);
    QString value = QString("%1|%2|%3|%4")
        .arg(bookmark.filePath)
        .arg(bookmark.pageNumber)
        .arg(bookmark.zoomFactor)
        .arg(bookmark.created.toString(Qt::ISODate));

    m_settings->setValue(key, value);
}

void BookmarkDialog::deleteBookmark(const QString& key)
{
    if (!m_settings) return;
    m_settings->remove(key);
}

QString BookmarkDialog::getBookmarkKey(const Bookmark& bookmark) const
{
    return QString("bookmarks/%1/%2").arg(bookmark.fileName).arg(bookmark.name);
}

void BookmarkDialog::updateButtonStates()
{
    bool hasSelection = m_bookmarksList->currentItem() != nullptr;
    m_editButton->setEnabled(hasSelection);
    m_deleteButton->setEnabled(hasSelection);
    m_goToButton->setEnabled(hasSelection);
}
