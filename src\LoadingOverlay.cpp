#include "LoadingOverlay.h"
#include <QPainter>
#include <QPainterPath>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGraphicsDropShadowEffect>

LoadingOverlay::LoadingOverlay(QWidget *parent)
    : QWidget(parent)
    , m_message<PERSON>abel(nullptr)
    , m_progressBar(nullptr)
    , m_contentWidget(nullptr)
    , m_fadeAnimation(nullptr)
    , m_rotationAnimation(nullptr)
    , m_animationTimer(nullptr)
    , m_isVisible(false)
    , m_rotationAngle(0)
    , m_currentMessage(tr("Loading..."))
{
    setupUI();
    hide();
}

LoadingOverlay::~LoadingOverlay()
{
    if (m_animationTimer) {
        m_animationTimer->stop();
    }
}

void LoadingOverlay::setupUI()
{
    // Set up the overlay widget
    setWindowFlags(Qt::FramelessWindowHint);
    setAttribute(Qt::WA_TranslucentBackground);
    setAttribute(Qt::WA_NoSystemBackground);

    // Create content widget with enhanced styling
    m_contentWidget = new QWidget(this);
    m_contentWidget->setFixedSize(240, 140);
    m_contentWidget->setStyleSheet(
        "QWidget { "
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 rgba(255, 255, 255, 250), stop:1 rgba(248, 249, 250, 245)); "
        "    border-radius: 16px; "
        "    border: 1px solid rgba(0, 120, 212, 0.2); "
        "}"
    );
    
    // Add drop shadow effect
    QGraphicsDropShadowEffect* shadowEffect = new QGraphicsDropShadowEffect(this);
    shadowEffect->setBlurRadius(20);
    shadowEffect->setColor(QColor(0, 0, 0, 60));
    shadowEffect->setOffset(0, 4);
    m_contentWidget->setGraphicsEffect(shadowEffect);
    
    // Create layout
    QVBoxLayout* layout = new QVBoxLayout(m_contentWidget);
    layout->setContentsMargins(CONTENT_PADDING, CONTENT_PADDING, CONTENT_PADDING, CONTENT_PADDING);
    layout->setSpacing(15);
    
    // Message label with enhanced styling
    m_messageLabel = new ElaText(m_currentMessage, this);
    m_messageLabel->setAlignment(Qt::AlignCenter);
    m_messageLabel->setTextPixelSize(15);
    m_messageLabel->setWordWrap(true);
    m_messageLabel->setStyleSheet(
        "color: #1e1e1e; "
        "font-weight: 500; "
        "font-family: 'Segoe UI', system-ui, sans-serif; "
        "margin: 5px 0px;"
    );
    layout->addWidget(m_messageLabel);

    // Enhanced progress bar
    m_progressBar = new ElaProgress(this);
    m_progressBar->setRange(0, 100);
    m_progressBar->setValue(0);
    m_progressBar->setTextVisible(false);
    m_progressBar->setFixedHeight(8);
    m_progressBar->setStyleSheet(
        "ElaProgress { "
        "    background: rgba(0, 120, 212, 0.1); "
        "    border-radius: 4px; "
        "    border: none; "
        "} "
        "ElaProgress::chunk { "
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, "
        "        stop:0 #0078d4, stop:1 #106ebe); "
        "    border-radius: 4px; "
        "}"
    );
    layout->addWidget(m_progressBar);
    
    // Set up enhanced animations
    m_fadeAnimation = new QPropertyAnimation(this, "windowOpacity", this);
    m_fadeAnimation->setDuration(400);
    m_fadeAnimation->setEasingCurve(QEasingCurve::OutCubic);

    // Set up smooth rotation animation timer (60 FPS for smoother animation)
    m_animationTimer = new QTimer(this);
    connect(m_animationTimer, &QTimer::timeout, this, &LoadingOverlay::updateAnimation);

    // Add scale animation for content widget
    m_rotationAnimation = new QPropertyAnimation(m_contentWidget, "geometry", this);
    m_rotationAnimation->setDuration(400);
    m_rotationAnimation->setEasingCurve(QEasingCurve::OutBack);
    
    updatePosition();
}

void LoadingOverlay::showLoading(const QString& message)
{
    if (m_isVisible) return;

    m_isVisible = true;
    m_currentMessage = message;
    m_messageLabel->setText(message);

    // Reset progress
    m_progressBar->setValue(0);

    // Position the overlay
    updatePosition();

    // Start with scaled-down content widget for bounce-in effect
    QRect finalGeometry = m_contentWidget->geometry();
    QRect startGeometry = finalGeometry;
    startGeometry.setSize(finalGeometry.size() * 0.8);
    startGeometry.moveCenter(finalGeometry.center());
    m_contentWidget->setGeometry(startGeometry);

    // Show with fade in animation
    setWindowOpacity(0.0);
    show();
    raise();

    // Animate fade in
    m_fadeAnimation->setStartValue(0.0);
    m_fadeAnimation->setEndValue(1.0);
    m_fadeAnimation->start();

    // Animate scale up with bounce effect
    m_rotationAnimation->setStartValue(startGeometry);
    m_rotationAnimation->setEndValue(finalGeometry);
    m_rotationAnimation->start();

    // Start smooth rotation animation (60 FPS)
    m_animationTimer->start(16);
}

void LoadingOverlay::hideLoading()
{
    if (!m_isVisible) return;

    m_isVisible = false;

    // Stop animations
    m_animationTimer->stop();

    // Animate scale down with fade out
    QRect currentGeometry = m_contentWidget->geometry();
    QRect endGeometry = currentGeometry;
    endGeometry.setSize(currentGeometry.size() * 0.9);
    endGeometry.moveCenter(currentGeometry.center());

    // Fade out
    m_fadeAnimation->setStartValue(windowOpacity());
    m_fadeAnimation->setEndValue(0.0);

    // Scale down animation
    m_rotationAnimation->setStartValue(currentGeometry);
    m_rotationAnimation->setEndValue(endGeometry);
    m_rotationAnimation->setEasingCurve(QEasingCurve::InBack);

    // Connect animations to hide when finished
    connect(m_fadeAnimation, &QPropertyAnimation::finished, this, [this]() {
        hide();
        m_fadeAnimation->disconnect();
        m_rotationAnimation->disconnect();
        // Reset easing curve for next show
        m_rotationAnimation->setEasingCurve(QEasingCurve::OutBack);
    });

    m_fadeAnimation->start();
    m_rotationAnimation->start();
}

void LoadingOverlay::setProgress(int value)
{
    m_progressBar->setValue(qBound(0, value, 100));
}

void LoadingOverlay::setMessage(const QString& message)
{
    m_currentMessage = message;
    m_messageLabel->setText(message);
}

void LoadingOverlay::updateAnimation()
{
    // Smoother rotation with variable speed for more dynamic effect
    m_rotationAngle = (m_rotationAngle + 8) % 360;
    update();
}

void LoadingOverlay::updatePosition()
{
    if (!parent()) return;
    
    QWidget* parentWidget = qobject_cast<QWidget*>(parent());
    if (!parentWidget) return;
    
    // Center the overlay on the parent widget
    QRect parentRect = parentWidget->rect();
    QPoint center = parentRect.center();
    
    QRect overlayRect = m_contentWidget->rect();
    overlayRect.moveCenter(center);
    
    setGeometry(parentRect);
    m_contentWidget->move(overlayRect.topLeft());
}

void LoadingOverlay::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Draw semi-transparent background
    painter.fillRect(rect(), QColor(0, 0, 0, 80));
    
    // Draw enhanced spinning indicator
    if (m_isVisible && m_animationTimer->isActive()) {
        QRect spinnerRect(0, 0, SPINNER_SIZE + 4, SPINNER_SIZE + 4);
        spinnerRect.moveCenter(m_contentWidget->geometry().center() - QPoint(0, 25));

        painter.save();
        painter.translate(spinnerRect.center());
        painter.rotate(m_rotationAngle);

        // Draw gradient spinner with multiple arcs for depth effect
        QConicalGradient gradient(0, 0, 0);
        gradient.setColorAt(0.0, QColor(0, 120, 212, 255));
        gradient.setColorAt(0.7, QColor(0, 120, 212, 100));
        gradient.setColorAt(1.0, QColor(0, 120, 212, 0));

        QPen pen;
        pen.setWidth(4);
        pen.setCapStyle(Qt::RoundCap);
        pen.setBrush(QBrush(gradient));
        painter.setPen(pen);
        painter.setBrush(Qt::NoBrush);

        // Draw main arc
        QRect arcRect(-SPINNER_SIZE/2 + 2, -SPINNER_SIZE/2 + 2, SPINNER_SIZE - 4, SPINNER_SIZE - 4);
        painter.drawArc(arcRect, 0, 300 * 16); // 300 degrees for better visual effect

        // Draw secondary arc for depth
        painter.save();
        painter.rotate(180);
        QPen secondaryPen(QColor(0, 120, 212, 60), 2);
        secondaryPen.setCapStyle(Qt::RoundCap);
        painter.setPen(secondaryPen);
        painter.drawArc(arcRect.adjusted(2, 2, -2, -2), 0, 120 * 16);
        painter.restore();

        painter.restore();
    }
}

void LoadingOverlay::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    updatePosition();
}
