#ifndef ONBOARDINGTOUR_H
#define ONBOARDINGTOUR_H

#include "ElaIntegration.h"
#include <QPropertyAnimation>
#include <QVBoxLayout>
#include <QHBoxLayout>

enum class TourPosition {
    Top,
    Bottom,
    Left,
    Right,
    Center
};

struct TourStep {
    QString title;
    QString description;
    QWidget* targetWidget;
    QString targetObjectName;
    QPoint customPosition;
    bool useCustomPosition;
    QString buttonText;
    TourPosition position;

    TourStep(const QString& t, const QString& desc, QWidget* target = nullptr)
        : title(t), description(desc), targetWidget(target), useCustomPosition(false),
          buttonText("Next"), position(TourPosition::Bottom) {}

    TourStep(const QString& t, const QString& desc, const QString& objectName)
        : title(t), description(desc), targetWidget(nullptr), targetObjectName(objectName),
          useCustomPosition(false), buttonText("Next"), position(TourPosition::Bottom) {}

    TourStep(const QString& t, const QString& desc, const QPoint& pos)
        : title(t), description(desc), targetWidget(nullptr), customPosition(pos),
          useCustomPosition(true), buttonText("Next"), position(TourPosition::Center) {}
};

class OnboardingTour : public QWidget
{
    Q_OBJECT

public:
    explicit OnboardingTour(QWidget *parent = nullptr);
    ~OnboardingTour();

    // Tour management
    void addStep(const TourStep& step);
    void startTour();
    void stopTour();
    void nextStep();
    void previousStep();
    void skipTour();
    
    // Configuration
    void setWelcomeMessage(const QString& title, const QString& message);
    void setCompletionMessage(const QString& title, const QString& message);

signals:
    void tourStarted();
    void tourCompleted();
    void tourSkipped();
    void stepChanged(int currentStep, int totalSteps);

protected:
    void paintEvent(QPaintEvent *event) override;
    void showEvent(QShowEvent *event) override;
    void hideEvent(QHideEvent *event) override;
    bool eventFilter(QObject *obj, QEvent *event) override;

private slots:
    void onNextClicked();
    void onPreviousClicked();
    void onSkipClicked();
    void animateToStep();

private:
    void setupUI();
    void showCurrentStep();
    void hideCurrentStep();
    void updateStepContent();
    void positionTooltip();
    void highlightTarget();
    void removeHighlight();
    void updateTheme();
    
    // UI Components
    QWidget* m_tooltipWidget;
    QVBoxLayout* m_tooltipLayout;
    ElaText* m_titleLabel;
    ElaText* m_descriptionLabel;
    ElaText* m_stepCounterLabel;
    QHBoxLayout* m_buttonLayout;
    ElaPushButton* m_previousButton;
    ElaPushButton* m_nextButton;
    ElaPushButton* m_skipButton;
    
    // Overlay for highlighting
    QWidget* m_overlayWidget;
    QWidget* m_highlightWidget;
    
    // Animation
    QPropertyAnimation* m_fadeAnimation;
    QPropertyAnimation* m_moveAnimation;
    
    // Tour data
    QList<TourStep> m_steps;
    int m_currentStep;
    QString m_welcomeTitle;
    QString m_welcomeMessage;
    QString m_completionTitle;
    QString m_completionMessage;
    
    // State
    ElaThemeType::ThemeMode m_themeMode;
    bool m_isActive;
    
    // Styling constants
    static const int TOOLTIP_RADIUS = 12;
    static const int TOOLTIP_PADDING = 20;
    static const int HIGHLIGHT_MARGIN = 8;
    static const int ANIMATION_DURATION = 300;
};

#endif // ONBOARDINGTOUR_H
