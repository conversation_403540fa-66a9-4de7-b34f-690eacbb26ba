// PdfController.cpp
#include "PdfController.h"
#include "AnnotationManager.h"
#include <poppler-qt6.h>
#include <QtConcurrent>
#include <QDebug>
#include <QAtomicInt>
#include <QPainter>
#include <QRegularExpression>
#include <QGuiApplication>
#include <QScreen>


PdfController::PdfController(QObject *parent) : QObject(parent), m_activeRenderTasks(0)
{
    // Limit cache sizes to avoid using excessive memory
    m_pageCache.setMaxCost(400 * 1024 * 1024); // 400 MB for hi-res
    m_previewCache.setMaxCost(50 * 1024 * 1024); // 50 MB for lo-res

    // Configure main thread pool for high-priority rendering
    int idealThreads = QThread::idealThreadCount();
    m_threadPool.setMaxThreadCount(qMax(2, idealThreads - 1)); // Reserve one thread for UI

    // Configure preview thread pool with lower priority
    m_previewThreadPool.setMaxThreadCount(qMax(1, idealThreads / 2)); // Use fewer threads for previews
    m_previewThreadPool.setThreadPriority(QThread::LowPriority);
}

PdfController::~PdfController()
{
    // Ensure all tasks finish before destruction
    m_threadPool.waitForDone(5000); // Wait up to 5 seconds
    m_previewThreadPool.waitForDone(3000); // Wait up to 3 seconds for previews
}

std::optional<int> PdfController::pageCount() const
{
    if (m_pdfDocument) {
        return m_pdfDocument->numPages();
    }
    return std::nullopt;
}

QSizeF PdfController::getPageSize(int pageNum) const
{
    if (!m_pdfDocument || pageNum < 0 || pageNum >= m_pdfDocument->numPages()) {
        return QSizeF();
    }

    std::unique_ptr<Poppler::Page> page(m_pdfDocument->page(pageNum));
    if (!page) {
        return QSizeF();
    }

    // Get page size in points (1/72 inch)
    return page->pageSizeF();
}

void PdfController::loadDocument(const QString& filePath)
{
    auto future = QtConcurrent::run(&m_threadPool, [this, filePath]() {
        // Emit loading progress
        emit loadingProgress(10, tr("Clearing cache..."));

        // Thread-safe cache clearing
        {
            QMutexLocker cacheLocker(&m_cacheMutex);
            m_pageCache.clear();
            m_previewCache.clear();
        }

        // Thread-safe document replacement
        {
            QMutexLocker docLocker(&m_documentMutex);
            m_pdfDocument.reset(); // Release old document
        }

        emit loadingProgress(30, tr("Loading PDF document..."));

        auto newDocument = Poppler::Document::load(filePath);

        if (!newDocument || newDocument->isLocked()) {
            emit documentLoaded(false, "Failed to load or PDF is password-protected.");
            return;
        }

        emit loadingProgress(60, tr("Configuring rendering settings..."));

        // Configure document-level rendering settings for better quality
        newDocument->setRenderBackend(Poppler::Document::SplashBackend);
        newDocument->setRenderHint(Poppler::Document::Antialiasing, true);
        newDocument->setRenderHint(Poppler::Document::TextAntialiasing, true);
        newDocument->setRenderHint(Poppler::Document::TextHinting, true);
        newDocument->setRenderHint(Poppler::Document::TextSlightHinting, true);

        emit loadingProgress(80, tr("Finalizing document..."));

        // Thread-safe document assignment
        {
            QMutexLocker docLocker(&m_documentMutex);
            m_pdfDocument = std::move(newDocument);
        }

        emit loadingProgress(100, tr("Document loaded successfully"));
        emit documentLoaded(true, "");

        // After loading, start generating the low-res previews in the background
        generatePreviewCache();
    });
    Q_UNUSED(future); // Suppress nodiscard warning
}

void PdfController::closeDocument()
{
    // Clear cache first
    clearCache();

    // Thread-safe document reset
    {
        QMutexLocker docLocker(&m_documentMutex);
        if (m_pdfDocument) {
            m_pdfDocument.reset();
        }
    }

    // Emit signal to indicate document is closed
    emit documentLoaded(false, "Document closed");
}

void PdfController::generatePreviewCache()
{
    int numPages;
    {
        QMutexLocker docLocker(&m_documentMutex);
        if (!m_pdfDocument) return;
        numPages = m_pdfDocument->numPages();
    }

    // C++11: Atomic integer for safe progress tracking from multiple threads
    auto progress = std::make_shared<QAtomicInt>(0);

    // Use separate thread pool for preview generation to avoid blocking main rendering
    for (int i = 0; i < numPages; ++i) {
        auto future = QtConcurrent::run(&m_previewThreadPool, [this, i, numPages, progress]() {
            renderPageTask(i, 1.0, true, 0); // Render a preview (zoom doesn't matter, no rotation)
            const int currentProgress = progress->fetchAndAddOrdered(1) + 1;
            emit previewCacheProgress(currentProgress, numPages);
        });
        Q_UNUSED(future); // Suppress nodiscard warning
    }
}

void PdfController::requestPage(int pageNum, double zoomFactor, int rotation)
{
    // Generate cache key that includes zoom factor and rotation
    const QString cacheKey = generateCacheKey(pageNum, zoomFactor, rotation);

    // 1. Check hi-res cache first. If found, we're done.
    if (auto* cachedPixmap = m_pageCache.object(cacheKey)) {
        emit pageReady(pageNum, *cachedPixmap, true);
        return;
    }

    // 2. Check lo-res cache. If found, show it immediately (only if no rotation)
    if (rotation == 0) {
        if (auto* previewPixmap = m_previewCache.object(pageNum)) {
            emit pageReady(pageNum, *previewPixmap, false); // It's a preview
        }
    }

    // 3. In all cases (except hi-res hit), request a new hi-res render.
    renderPageTask(pageNum, zoomFactor, false, rotation);

    // 4. Preload adjacent pages for better performance
    preloadAdjacentPages(pageNum, zoomFactor, rotation);
}

void PdfController::requestPreview(int pageNum)
{
    // Check lo-res cache. If found, show it immediately.
    if (auto* previewPixmap = m_previewCache.object(pageNum)) {
        emit pageReady(pageNum, *previewPixmap, false); // It's a preview
        return;
    }

    // If not in cache, render a preview
    renderPageTask(pageNum, 1.0, true, 0);
}

void PdfController::renderPageTask(int pageNum, double zoomFactor, bool isPreview, int rotation)
{
    const QString cacheKey = generateCacheKey(pageNum, zoomFactor, rotation);

    // Don't re-render if a hi-res version is already in the cache
    if (!isPreview && m_pageCache.contains(cacheKey)) {
        return;
    }

    auto future = QtConcurrent::run(&m_threadPool, [this, pageNum, zoomFactor, isPreview, rotation, cacheKey]() {
        // Track active render tasks
        m_activeRenderTasks.fetchAndAddOrdered(1);

        std::unique_ptr<Poppler::Page> page;
        {
            QMutexLocker docLocker(&m_documentMutex);
            if (!m_pdfDocument) {
                m_activeRenderTasks.fetchAndSubOrdered(1);
                return;
            }
            page = std::unique_ptr<Poppler::Page>(m_pdfDocument->page(pageNum));
        }

        if (!page) {
            m_activeRenderTasks.fetchAndSubOrdered(1);
            return;
        }

        // Calculate DPI with adaptive quality based on zoom level
        double dpi;
        if (isPreview) {
            dpi = m_previewDpi;
        } else {
            // Use adaptive DPI calculation for better performance
            dpi = getAdaptiveDpi(zoomFactor);
            // Clamp DPI to reasonable limits to prevent excessive memory usage
            dpi = qBound(m_defaultDpi * 0.1, dpi, m_maxDpi);
        }

        // Get rotation enum
        Poppler::Page::Rotation rotationEnum = static_cast<Poppler::Page::Rotation>(getRotationEnum(rotation));

        // Optimize rendering settings based on zoom level
        optimizeRenderingSettings(page.get(), zoomFactor);

        // Render the page with adaptive quality settings
        QImage qtImage = page->renderToImage(dpi, dpi, -1, -1, -1, -1, rotationEnum);
        if (qtImage.isNull()) return;

        // Ensure the image format is optimal for display and apply post-processing for better quality
        if (qtImage.format() != QImage::Format_ARGB32_Premultiplied) {
            qtImage = qtImage.convertToFormat(QImage::Format_ARGB32_Premultiplied);
        }

        // Apply additional image processing for better quality if needed
        if (!isPreview && zoomFactor > 1.5) {
            // For high zoom levels, apply slight sharpening to improve text clarity
            qtImage = applySharpeningFilter(qtImage);
        }

        QPixmap pixmap = QPixmap::fromImage(qtImage);

        // Set device pixel ratio for high-DPI displays
        if (!isPreview) {
            // Get device pixel ratio from the primary screen
            QScreen* screen = QGuiApplication::primaryScreen();
            if (screen) {
                pixmap.setDevicePixelRatio(screen->devicePixelRatio());
            }
        }

        // Thread-safe cache operations
        {
            QMutexLocker cacheLocker(&m_cacheMutex);
            if (isPreview) {
                m_previewCache.insert(pageNum, new QPixmap(pixmap), pixmap.toImage().sizeInBytes());
            } else {
                m_pageCache.insert(cacheKey, new QPixmap(pixmap), pixmap.toImage().sizeInBytes());
            }
        }

        // Only emit pageReady for hi-res images from this task
        if (!isPreview) {
            emit pageReady(pageNum, pixmap, true);
        }

        // Emit memory usage update and check for cleanup
        const qint64 currentUsage = getCacheMemoryUsage();
        emit memoryUsageChanged(currentUsage, getCachedPagesCount());

        // Auto-cleanup if memory usage is too high
        if (currentUsage > m_maxCacheSize) {
            performSmartCleanup();
        }

        // Decrement active render tasks counter
        m_activeRenderTasks.fetchAndSubOrdered(1);
    });
    Q_UNUSED(future); // Suppress nodiscard warning
}

QString PdfController::generateCacheKey(int pageNum, double zoomFactor, int rotation) const
{
    // Round zoom factor to avoid excessive cache entries for tiny differences
    const double roundedZoom = qRound(zoomFactor * 100.0) / 100.0;
    return QString("%1_%2_%3").arg(pageNum).arg(roundedZoom).arg(rotation);
}

double PdfController::getEffectiveDpi(double zoomFactor, double devicePixelRatio) const
{
    // If no device pixel ratio provided, get it from the primary screen
    if (devicePixelRatio <= 0.0) {
        QScreen* screen = QGuiApplication::primaryScreen();
        if (screen) {
            devicePixelRatio = screen->devicePixelRatio();
        } else {
            devicePixelRatio = 1.0;
        }
    }

    // Improved DPI calculation for better rendering quality
    // Base DPI should be higher for crisp text rendering
    const double baseDpi = 200.0; // Increased from 150 for better quality

    // For high-DPI displays, we need to be more conservative to avoid excessive memory usage
    // while still maintaining quality
    double effectiveDpi;
    if (devicePixelRatio > 1.0) {
        // On high-DPI displays, use a more conservative approach
        // The system already handles some scaling, so we don't need to multiply by full device pixel ratio
        effectiveDpi = zoomFactor * baseDpi * (1.0 + (devicePixelRatio - 1.0) * 0.5);
    } else {
        // On standard displays, use the full calculation
        effectiveDpi = zoomFactor * baseDpi * devicePixelRatio;
    }

    // Ensure we don't exceed reasonable limits
    return qBound(baseDpi * 0.25, effectiveDpi, m_maxDpi);
}

QImage PdfController::renderPageToImage(int pageNum, double dpi, int rotation) const
{
    if (!m_pdfDocument || pageNum < 0 || pageNum >= m_pdfDocument->numPages()) {
        return QImage();
    }

    auto page = m_pdfDocument->page(pageNum);
    if (!page) {
        return QImage();
    }

    // Convert rotation to Poppler enum
    Poppler::Page::Rotation rotationEnum = Poppler::Page::Rotate0;
    switch (rotation) {
        case 90: rotationEnum = Poppler::Page::Rotate90; break;
        case 180: rotationEnum = Poppler::Page::Rotate180; break;
        case 270: rotationEnum = Poppler::Page::Rotate270; break;
        default: rotationEnum = Poppler::Page::Rotate0; break;
    }

    // Render the page to image
    QImage image = page->renderToImage(dpi, dpi, -1, -1, -1, -1, rotationEnum);

    return image;
}

int PdfController::getRotationEnum(int degrees) const
{
    switch (degrees) {
        case 90:  return static_cast<int>(Poppler::Page::Rotate90);
        case 180: return static_cast<int>(Poppler::Page::Rotate180);
        case 270: return static_cast<int>(Poppler::Page::Rotate270);
        default:  return static_cast<int>(Poppler::Page::Rotate0);
    }
}

qint64 PdfController::getCacheMemoryUsage() const
{
    QMutexLocker cacheLocker(&m_cacheMutex);
    qint64 totalSize = 0;

    // Calculate high-res cache size
    QList<QString> keys = m_pageCache.keys();
    for (const QString& key : keys) {
        if (auto* pixmap = m_pageCache.object(key)) {
            totalSize += pixmap->toImage().sizeInBytes();
        }
    }

    // Calculate preview cache size
    QList<int> previewKeys = m_previewCache.keys();
    for (int key : previewKeys) {
        if (auto* pixmap = m_previewCache.object(key)) {
            totalSize += pixmap->toImage().sizeInBytes();
        }
    }

    return totalSize;
}

int PdfController::getCachedPagesCount() const
{
    QMutexLocker cacheLocker(&m_cacheMutex);
    return m_pageCache.size() + m_previewCache.size();
}

int PdfController::getActiveRenderTasks() const
{
    return m_activeRenderTasks.loadAcquire();
}

QString PdfController::getPageText(int pageNum) const
{
    if (!m_pdfDocument || pageNum < 0 || pageNum >= m_pdfDocument->numPages()) {
        return QString();
    }

    std::unique_ptr<Poppler::Page> page(m_pdfDocument->page(pageNum));
    if (!page) {
        return QString();
    }

    return page->text(QRectF());
}

QList<SearchResult> PdfController::searchText(const QString& searchTerm, bool caseSensitive, bool wholeWords) const
{
    QList<SearchResult> results;

    if (!m_pdfDocument || searchTerm.isEmpty()) {
        return results;
    }

    const int totalPages = m_pdfDocument->numPages();

    for (int pageNum = 0; pageNum < totalPages; ++pageNum) {
        std::unique_ptr<Poppler::Page> page(m_pdfDocument->page(pageNum));
        if (!page) continue;

        // Get page text and search manually for better control
        QString pageText = page->text(QRectF());
        if (pageText.isEmpty()) continue;

        Qt::CaseSensitivity sensitivity = caseSensitive ? Qt::CaseSensitive : Qt::CaseInsensitive;

        // Find all occurrences of the search term
        int startIndex = 0;
        while (true) {
            int foundIndex = pageText.indexOf(searchTerm, startIndex, sensitivity);
            if (foundIndex == -1) break;

            // Check for whole word match if required
            if (wholeWords && !isWholeWordMatch(pageText, foundIndex, searchTerm.length())) {
                startIndex = foundIndex + 1;
                continue;
            }

            SearchResult result;
            result.pageNumber = pageNum;
            result.startIndex = foundIndex;
            result.length = searchTerm.length();

            // Create a better bounding box estimate
            QSizeF pageSize = page->pageSizeF();
            // Estimate position based on text position (rough approximation)
            double relativePosition = static_cast<double>(foundIndex) / pageText.length();
            double estimatedY = relativePosition * pageSize.height();
            result.boundingBox = QRectF(0, estimatedY, pageSize.width(), pageSize.height() / 30.0);

            // Extract context (50 characters before and after)
            int contextStart = qMax(0, foundIndex - 50);
            int contextEnd = qMin(pageText.length(), foundIndex + searchTerm.length() + 50);
            result.context = pageText.mid(contextStart, contextEnd - contextStart);

            results.append(result);
            startIndex = foundIndex + 1;
        }
    }

    return results;
}

QList<OutlineItem> PdfController::getDocumentOutline() const
{
    QList<OutlineItem> outline;

    if (!m_pdfDocument) {
        return outline;
    }

    // For now, create a simple outline based on page numbers
    // In a full implementation, we would parse the actual PDF outline/bookmarks
    const int totalPages = m_pdfDocument->numPages();

    // Create a simple outline with page groups
    if (totalPages > 10) {
        // Group pages in sets of 10
        for (int i = 0; i < totalPages; i += 10) {
            OutlineItem item;
            int endPage = qMin(i + 9, totalPages - 1);
            item.title = tr("Pages %1-%2").arg(i + 1).arg(endPage + 1);
            item.pageNumber = i;
            item.level = 0;
            item.isOpen = false;

            // Add individual pages as children
            for (int j = i; j <= endPage && j < totalPages; ++j) {
                OutlineItem pageItem;
                pageItem.title = tr("Page %1").arg(j + 1);
                pageItem.pageNumber = j;
                pageItem.level = 1;
                pageItem.isOpen = false;
                item.children.append(pageItem);
            }

            outline.append(item);
        }
    } else {
        // For small documents, just list all pages
        for (int i = 0; i < totalPages; ++i) {
            OutlineItem item;
            item.title = tr("Page %1").arg(i + 1);
            item.pageNumber = i;
            item.level = 0;
            item.isOpen = false;
            outline.append(item);
        }
    }

    return outline;
}

void PdfController::performSearch(const QString& searchTerm, bool caseSensitive, bool wholeWords)
{
    auto future = QtConcurrent::run(&m_threadPool, [this, searchTerm, caseSensitive, wholeWords]() {
        QList<SearchResult> results = searchText(searchTerm, caseSensitive, wholeWords);
        emit searchCompleted(results);
    });
    Q_UNUSED(future);
}

void PdfController::requestPageWithHighlights(int pageNum, double zoomFactor, const QList<QRectF>& highlights, int rotation)
{
    renderPageWithHighlightsTask(pageNum, zoomFactor, highlights, rotation);
}

void PdfController::requestPageWithAnnotations(int pageNum, double zoomFactor, int rotation)
{
    renderPageWithAnnotationsTask(pageNum, zoomFactor, rotation);
}

void PdfController::renderPageWithHighlightsTask(int pageNum, double zoomFactor, const QList<QRectF>& highlights, int rotation)
{
    auto future = QtConcurrent::run(&m_threadPool, [this, pageNum, zoomFactor, highlights, rotation]() {
        if (!m_pdfDocument) return;

        std::unique_ptr<Poppler::Page> page(m_pdfDocument->page(pageNum));
        if (!page) return;

        // Calculate DPI using effective DPI calculation
        double dpi = getEffectiveDpi(zoomFactor);
        dpi = qBound(m_defaultDpi * 0.1, dpi, m_maxDpi);

        // Get rotation enum
        Poppler::Page::Rotation rotationEnum = static_cast<Poppler::Page::Rotation>(getRotationEnum(rotation));

        // Render the page
        QImage qtImage = page->renderToImage(dpi, dpi, -1, -1, -1, -1, rotationEnum);
        if (qtImage.isNull()) return;

        QPixmap pixmap = QPixmap::fromImage(qtImage);

        // Add highlights to the pixmap
        if (!highlights.isEmpty()) {
            pixmap = addHighlightsToPixmap(pixmap, highlights, dpi);
        }

        emit pageReady(pageNum, pixmap, true);
    });
    Q_UNUSED(future);
}

QPixmap PdfController::addHighlightsToPixmap(const QPixmap& originalPixmap, const QList<QRectF>& highlights, double dpi) const
{
    QPixmap highlightedPixmap = originalPixmap;
    QPainter painter(&highlightedPixmap);

    // Set up highlight brush
    QColor highlightColor(255, 255, 0, 100); // Semi-transparent yellow
    painter.setBrush(QBrush(highlightColor));
    painter.setPen(Qt::NoPen);

    // Convert PDF coordinates to pixel coordinates
    const double pointsToPixels = dpi / 72.0;

    for (const QRectF& highlight : highlights) {
        QRectF pixelRect = QRectF(
            highlight.x() * pointsToPixels,
            highlight.y() * pointsToPixels,
            highlight.width() * pointsToPixels,
            highlight.height() * pointsToPixels
        );

        painter.drawRect(pixelRect);
    }

    return highlightedPixmap;
}

void PdfController::renderPageWithAnnotationsTask(int pageNum, double zoomFactor, int rotation)
{
    auto future = QtConcurrent::run(&m_threadPool, [this, pageNum, zoomFactor, rotation]() {
        if (!m_pdfDocument) return;

        std::unique_ptr<Poppler::Page> page(m_pdfDocument->page(pageNum));
        if (!page) return;

        // Calculate DPI using effective DPI calculation
        double dpi = getEffectiveDpi(zoomFactor);
        dpi = qBound(m_defaultDpi * 0.1, dpi, m_maxDpi);

        // Get rotation enum
        Poppler::Page::Rotation rotationEnum = static_cast<Poppler::Page::Rotation>(getRotationEnum(rotation));

        // Render the page
        QImage qtImage = page->renderToImage(dpi, dpi, -1, -1, -1, -1, rotationEnum);
        if (qtImage.isNull()) return;

        QPixmap pixmap = QPixmap::fromImage(qtImage);

        // Add annotations if annotation manager is available
        if (m_annotationManager) {
            QPainter painter(&pixmap);
            m_annotationManager->renderAnnotations(&painter, pageNum, dpi, zoomFactor);
        }

        emit pageWithAnnotationsReady(pageNum, pixmap);
    });
    Q_UNUSED(future);
}

void PdfController::preloadAdjacentPages(int currentPage, double zoomFactor, int rotation)
{
    if (!m_pdfDocument) return;

    const int totalPages = m_pdfDocument->numPages();

    // Preload pages within range
    for (int offset = 1; offset <= m_preloadRange; ++offset) {
        // Preload next pages
        int nextPage = currentPage + offset;
        if (nextPage < totalPages) {
            const QString nextKey = generateCacheKey(nextPage, zoomFactor, rotation);
            if (!m_pageCache.contains(nextKey)) {
                // Use lower priority for preloading
                auto future = QtConcurrent::run(&m_threadPool, [this, nextPage, zoomFactor, rotation]() {
                    renderPageTask(nextPage, zoomFactor, false, rotation);
                });
                Q_UNUSED(future);
            }
        }

        // Preload previous pages
        int prevPage = currentPage - offset;
        if (prevPage >= 0) {
            const QString prevKey = generateCacheKey(prevPage, zoomFactor, rotation);
            if (!m_pageCache.contains(prevKey)) {
                // Use lower priority for preloading
                auto future = QtConcurrent::run(&m_threadPool, [this, prevPage, zoomFactor, rotation]() {
                    renderPageTask(prevPage, zoomFactor, false, rotation);
                });
                Q_UNUSED(future);
            }
        }
    }
}

void PdfController::clearCache()
{
    QMutexLocker cacheLocker(&m_cacheMutex);
    m_pageCache.clear();
    m_previewCache.clear();
    emit memoryUsageChanged(0, 0);
}

void PdfController::stopAllOperations()
{
    // Clear cache first to stop any cache-related operations
    clearCache();

    // Wait for all ongoing operations to complete with timeout
    if (!m_threadPool.waitForDone(3000)) {
        qWarning() << "Main thread pool did not finish within timeout, forcing termination";
        // Note: QThreadPool doesn't have a force stop, but this warning helps with debugging
    }

    if (!m_previewThreadPool.waitForDone(2000)) {
        qWarning() << "Preview thread pool did not finish within timeout, forcing termination";
    }

    // Reset document safely
    {
        QMutexLocker docLocker(&m_documentMutex);
        if (m_pdfDocument) {
            m_pdfDocument.reset();
        }
    }
}

void PdfController::performSmartCleanup()
{
    // Enhanced smart cleanup with priority-based cache management
    const qint64 targetSize = m_maxCacheSize * 0.7; // Target 70% of max size
    qint64 currentSize = getCacheMemoryUsage();

    if (currentSize <= targetSize) {
        return; // No cleanup needed
    }

    // Phase 1: Clear preview cache more aggressively (easier to regenerate)
    QList<int> previewKeys = m_previewCache.keys();
    int previewsToRemove = qMin(previewKeys.size() * 3 / 4, previewKeys.size()); // Remove 75%

    for (int i = 0; i < previewsToRemove; ++i) {
        m_previewCache.remove(previewKeys[i]);
    }

    currentSize = getCacheMemoryUsage();
    if (currentSize <= targetSize) {
        emit memoryUsageChanged(currentSize, getCachedPagesCount());
        return;
    }

    // Phase 2: Smart high-res cache cleanup based on zoom levels and recency
    QList<QString> pageKeys = m_pageCache.keys();
    QList<QPair<QString, double>> keyPriorities;

    // Calculate priorities for each cached page
    for (const QString& key : pageKeys) {
        QStringList parts = key.split('_');
        if (parts.size() >= 2) {
            double zoomFactor = parts[1].toDouble();
            double priority = calculateCachePriority(key, zoomFactor);
            keyPriorities.append(qMakePair(key, priority));
        }
    }

    // Sort by priority (lower priority = remove first)
    std::sort(keyPriorities.begin(), keyPriorities.end(),
              [](const QPair<QString, double>& a, const QPair<QString, double>& b) {
                  return a.second < b.second;
              });

    // Remove low-priority pages until we reach target size
    int pagesToRemove = qMin(keyPriorities.size() / 3, keyPriorities.size()); // Remove up to 33%
    for (int i = 0; i < pagesToRemove && getCacheMemoryUsage() > targetSize; ++i) {
        m_pageCache.remove(keyPriorities[i].first);
    }

    emit memoryUsageChanged(getCacheMemoryUsage(), getCachedPagesCount());
}

QImage PdfController::applySharpeningFilter(const QImage& image) const
{
    if (image.isNull() || image.width() < 3 || image.height() < 3) {
        return image;
    }

    // Create a copy of the image to work with
    QImage result = image.copy();

    // Simple unsharp mask filter for text sharpening
    // This is a lightweight sharpening that works well for text
    const int width = image.width();
    const int height = image.height();

    // Sharpening kernel (3x3)
    const double kernel[3][3] = {
        { 0, -0.25,  0},
        {-0.25, 2.0, -0.25},
        { 0, -0.25,  0}
    };

    for (int y = 1; y < height - 1; ++y) {
        for (int x = 1; x < width - 1; ++x) {
            double r = 0, g = 0, b = 0;

            // Apply convolution kernel
            for (int ky = -1; ky <= 1; ++ky) {
                for (int kx = -1; kx <= 1; ++kx) {
                    QRgb pixel = image.pixel(x + kx, y + ky);
                    double weight = kernel[ky + 1][kx + 1];
                    r += qRed(pixel) * weight;
                    g += qGreen(pixel) * weight;
                    b += qBlue(pixel) * weight;
                }
            }

            // Clamp values and set pixel
            r = qBound(0.0, r, 255.0);
            g = qBound(0.0, g, 255.0);
            b = qBound(0.0, b, 255.0);

            QRgb originalPixel = image.pixel(x, y);
            result.setPixel(x, y, qRgba(static_cast<int>(r), static_cast<int>(g), static_cast<int>(b), qAlpha(originalPixel)));
        }
    }

    return result;
}

bool PdfController::isWholeWordMatch(const QString& text, int position, int length) const
{
    // Check if the found text is a whole word (surrounded by word boundaries)
    bool startBoundary = (position == 0) || !text[position - 1].isLetterOrNumber();
    bool endBoundary = (position + length >= text.length()) || !text[position + length].isLetterOrNumber();

    return startBoundary && endBoundary;
}

// Performance optimization methods
double PdfController::getAdaptiveDpi(double zoomFactor) const
{
    // Adaptive DPI calculation for better performance vs quality balance
    QScreen* screen = QGuiApplication::primaryScreen();
    double devicePixelRatio = screen ? screen->devicePixelRatio() : 1.0;

    // Base DPI calculation with performance considerations
    double baseDpi = m_defaultDpi;

    // For very low zoom levels, use lower DPI to save memory
    if (zoomFactor < 0.5) {
        baseDpi = m_defaultDpi * 0.7;
    }
    // For high zoom levels, increase DPI but with diminishing returns
    else if (zoomFactor > 2.0) {
        baseDpi = m_defaultDpi * (1.0 + (zoomFactor - 1.0) * 0.6);
    }
    // For normal zoom levels, use standard calculation
    else {
        baseDpi = m_defaultDpi * zoomFactor;
    }

    // Adjust for high-DPI displays with performance considerations
    if (devicePixelRatio > 1.0) {
        baseDpi *= (1.0 + (devicePixelRatio - 1.0) * 0.5);
    }

    return qBound(m_defaultDpi * 0.3, baseDpi, m_maxDpi);
}

void PdfController::optimizeRenderingSettings(Poppler::Page* page, double zoomFactor) const
{
    if (!page || !m_pdfDocument) return;

    // Optimize rendering hints based on zoom level for better performance
    // Note: Render hints are set on the document, not individual pages
    if (zoomFactor < 1.0) {
        // For zoomed-out views, prioritize speed over quality
        m_pdfDocument->setRenderHint(Poppler::Document::Antialiasing, false);
        m_pdfDocument->setRenderHint(Poppler::Document::TextAntialiasing, true); // Keep text readable
        m_pdfDocument->setRenderHint(Poppler::Document::TextHinting, false);
    } else if (zoomFactor > 2.0) {
        // For high zoom levels, prioritize quality
        m_pdfDocument->setRenderHint(Poppler::Document::Antialiasing, true);
        m_pdfDocument->setRenderHint(Poppler::Document::TextAntialiasing, true);
        m_pdfDocument->setRenderHint(Poppler::Document::TextHinting, true);
        m_pdfDocument->setRenderHint(Poppler::Document::TextSlightHinting, true);
    } else {
        // For normal zoom levels, balanced settings
        m_pdfDocument->setRenderHint(Poppler::Document::Antialiasing, true);
        m_pdfDocument->setRenderHint(Poppler::Document::TextAntialiasing, true);
        m_pdfDocument->setRenderHint(Poppler::Document::TextHinting, true);
    }
}

bool PdfController::shouldUseProgressiveRendering(double zoomFactor) const
{
    // Use progressive rendering for high zoom levels or large documents
    return zoomFactor > 1.5 || (m_pdfDocument && m_pdfDocument->numPages() > 100);
}

double PdfController::calculateCachePriority(const QString& cacheKey, double zoomFactor) const
{
    // Calculate cache priority based on multiple factors
    // Higher priority = keep longer in cache

    QStringList parts = cacheKey.split('_');
    if (parts.size() < 1) return 0.0;

    int pageNum = parts[0].toInt();
    double priority = 1.0;

    // Factor 1: Zoom level priority (normal zoom levels are more important)
    if (zoomFactor >= 0.8 && zoomFactor <= 2.0) {
        priority += 2.0; // Common zoom levels
    } else if (zoomFactor > 2.0) {
        priority += 1.0; // High zoom levels
    } else {
        priority += 0.5; // Very low zoom levels
    }

    // Factor 2: Page position priority (first few pages and middle pages are more important)
    if (!m_pdfDocument) return priority;

    int totalPages = m_pdfDocument->numPages();
    if (pageNum < 5) {
        priority += 1.5; // First few pages
    } else if (pageNum < totalPages / 4) {
        priority += 1.0; // First quarter
    } else if (pageNum > totalPages * 3 / 4) {
        priority += 0.5; // Last quarter
    } else {
        priority += 1.2; // Middle pages
    }

    // Factor 3: Memory size consideration (lower DPI = higher priority for keeping)
    double dpi = getAdaptiveDpi(zoomFactor);
    if (dpi < m_defaultDpi) {
        priority += 0.5; // Smaller images are cheaper to keep
    }

    return priority;
}
