#ifndef CRASHLOGPACKAGER_H
#define CRASHLOGPACKAGER_H

#include <QObject>
#include <QString>
#include <QStringList>
#include <QDateTime>
#include <QDir>
#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonArray>
#include <QFileInfo>
#include <QProcess>
#include <QTemporaryDir>
#include <QMutex>
#include <QMutexLocker>

struct PackageInfo {
    QString packagePath;
    QString packageName;
    QDateTime createdAt;
    qint64 totalSize;
    int crashReportCount;
    int debugFileCount;
    QString compressionType;
    QString checksum;
    
    PackageInfo() 
        : createdAt(QDateTime::currentDateTime())
        , totalSize(0)
        , crashReportCount(0)
        , debugFileCount(0)
        , compressionType("none") {}
};

class CrashLogPackager : public QObject
{
    Q_OBJECT

public:
    explicit CrashLogPackager(QObject *parent = nullptr);
    ~CrashLogPackager();

    // Main packaging functions
    QString createPackage(const QString& crashLogDirectory, const QString& outputPath = QString());
    QString createQuickPackage(const QString& crashLogDirectory);
    
    // Package configuration
    void setIncludeSystemInfo(bool include);
    void setIncludeDebugSymbols(bool include);
    void setIncludeApplicationBinary(bool include);
    void setCompressionEnabled(bool enabled);
    void setCompressionLevel(int level); // 1-9, where 9 is maximum compression
    
    // Package analysis
    PackageInfo analyzePackage(const QString& packagePath);
    QStringList listPackageContents(const QString& packagePath);
    
    // Utility functions
    QString calculateChecksum(const QString& filePath);
    qint64 getDirectorySize(const QString& dirPath);
    QStringList findCrashReports(const QString& directory);
    QStringList findDebugFiles(const QString& directory);
    
    // Package validation
    bool validatePackage(const QString& packagePath);
    QString getLastError() const;

signals:
    void packageCreated(const QString& packagePath, const PackageInfo& info);
    void packageProgress(int percentage, const QString& currentFile);
    void packageError(const QString& error);

private slots:
    void onCompressionProgress();

private:
    // Internal packaging methods
    bool createPackageStructure(const QString& tempDir);
    bool copyFiles(const QStringList& sourceFiles, const QString& destDir);
    bool compressPackage(const QString& sourceDir, const QString& outputPath);
    bool createManifest(const QString& packageDir, const PackageInfo& info);
    
    // System information gathering
    QString gatherSystemDiagnostics();
    QString gatherApplicationInfo();
    QStringList findRelevantDebugFiles();
    
    // Compression utilities
    bool compressWithZip(const QString& sourceDir, const QString& outputPath);
    bool compressWithTar(const QString& sourceDir, const QString& outputPath);
    
    // Configuration
    bool m_includeSystemInfo;
    bool m_includeDebugSymbols;
    bool m_includeApplicationBinary;
    bool m_compressionEnabled;
    int m_compressionLevel;
    
    // State
    QString m_lastError;
    QMutex m_mutex;
    
    // Constants
    static const QString MANIFEST_FILENAME;
    static const QString PACKAGE_VERSION;
    static const int DEFAULT_COMPRESSION_LEVEL = 6;
};

#endif // CRASHLOGPACKAGER_H
