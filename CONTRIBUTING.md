# Contributing to Optimized PDF Viewer

Thank you for your interest in contributing to the Optimized PDF Viewer project! This document provides guidelines and information for contributors.

## Code of Conduct

By participating in this project, you agree to abide by our code of conduct:
- Be respectful and inclusive
- Focus on constructive feedback
- Help create a welcoming environment for all contributors
- Report any unacceptable behavior to the project maintainers

## How to Contribute

### Reporting Bugs

Before creating bug reports, please check the existing issues to avoid duplicates. When creating a bug report, include:

- **Clear title and description**
- **Steps to reproduce** the issue
- **Expected vs actual behavior**
- **Environment details** (OS, Qt version, compiler)
- **Screenshots or logs** if applicable

### Suggesting Features

Feature suggestions are welcome! Please:
- Check existing feature requests first
- Provide a clear use case and rationale
- Consider the scope and complexity
- Be open to discussion and feedback

### Development Workflow

1. **Fork the repository**
2. **Create a feature branch** from `main`
   ```bash
   git checkout -b feature/your-feature-name
   ```
3. **Make your changes** following our coding standards
4. **Add tests** for new functionality
5. **Run the test suite** to ensure nothing breaks
6. **Commit your changes** with clear messages
7. **Push to your fork** and create a pull request

### Pull Request Guidelines

- **Clear title and description** of changes
- **Reference related issues** using keywords (fixes #123)
- **Include tests** for new features or bug fixes
- **Update documentation** if needed
- **Keep changes focused** - one feature/fix per PR
- **Ensure CI passes** before requesting review

## Development Setup

### Prerequisites
- Qt6 development environment
- CMake 3.16+
- C++17 compatible compiler
- Poppler with Qt6 bindings

### Building for Development
```bash
git clone <your-fork-url>
cd qt-pdf-render
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Debug
make -j$(nproc)
```

### Running Tests
```bash
# From build directory
ctest

# Or using scripts
cd ..
./scripts/run_tests.sh
```

## Coding Standards

### C++ Style Guidelines
- Follow Qt coding conventions
- Use meaningful variable and function names
- Include header guards in all header files
- Document public APIs with Doxygen comments
- Prefer const correctness
- Use RAII principles

### Code Formatting
- Use the provided `.clang-format` configuration
- 4 spaces for indentation (no tabs)
- Maximum line length: 100 characters
- Opening braces on same line for functions/classes

### Example Code Style
```cpp
class ExampleClass : public QObject
{
    Q_OBJECT

public:
    explicit ExampleClass(QObject *parent = nullptr);
    
    /**
     * @brief Brief description of the method
     * @param parameter Description of parameter
     * @return Description of return value
     */
    bool doSomething(const QString &parameter);

private slots:
    void onSomethingChanged();

private:
    QString m_memberVariable;
    QPointer<QWidget> m_widget;
};
```

### Commit Message Format
```
type(scope): brief description

Detailed explanation if needed.

Fixes #123
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

## Testing Guidelines

### Unit Tests
- Write tests for all new functionality
- Use Qt Test framework
- Test both success and failure cases
- Mock external dependencies when possible

### Integration Tests
- Test complete workflows
- Verify UI interactions
- Test with real PDF files

### Test Organization
```cpp
class TestClassName : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void testMethodName();
    void testMethodName_data();
    void cleanupTestCase();
};
```

## Documentation

### Code Documentation
- Document all public APIs
- Use Doxygen format for comments
- Include usage examples for complex functions
- Keep documentation up-to-date with code changes

### User Documentation
- Update README.md for user-facing changes
- Add screenshots for UI changes
- Update build instructions if dependencies change

## Release Process

### Version Numbering
We follow Semantic Versioning (SemVer):
- MAJOR.MINOR.PATCH
- MAJOR: Breaking changes
- MINOR: New features (backward compatible)
- PATCH: Bug fixes

### Release Checklist
- [ ] Update version numbers
- [ ] Update CHANGELOG.md
- [ ] Run full test suite
- [ ] Update documentation
- [ ] Create release notes
- [ ] Tag release in Git

## Getting Help

### Communication Channels
- GitHub Issues for bugs and features
- GitHub Discussions for questions
- Code reviews for technical discussions

### Resources
- [Qt Documentation](https://doc.qt.io/)
- [Poppler Documentation](https://poppler.freedesktop.org/)
- [CMake Documentation](https://cmake.org/documentation/)

## Recognition

Contributors will be recognized in:
- CONTRIBUTORS.md file
- Release notes
- Project documentation

Thank you for contributing to make this project better!
