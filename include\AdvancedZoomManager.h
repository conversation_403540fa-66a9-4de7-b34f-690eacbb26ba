/**
 * @file AdvancedZoomManager.h
 * @brief Advanced zoom management for PDF documents
 * <AUTHOR> Viewer Team
 * @date 2024-12-01
 * @version 1.0.0
 *
 * This file contains the AdvancedZoomManager class which provides enhanced
 * zoom functionality including smart zoom, magnifying glass, and content-aware
 * zoom features.
 */

#ifndef ADVANCEDZOOMMANAGER_H
#define ADVANCEDZOOMMANAGER_H

#include <QObject>
#include <QWidget>
#include <QLabel>
#include <QScrollArea>
#include <QTimer>
#include <QPropertyAnimation>
#include <QEasingCurve>
#include <QRectF>
#include <QPointF>
#include <QSizeF>
#include <memory>

class PdfController;
class DocumentTab;

/**
 * @brief Zoom mode enumeration
 */
enum class ZoomMode {
    Manual,         // User-controlled zoom
    FitToWindow,    // Fit entire page to window
    FitToWidth,     // Fit page width to window
    FitToHeight,    // Fit page height to window
    SmartFit,       // Intelligent fitting based on content
    ActualSize      // 100% zoom (actual size)
};

/**
 * @brief Smart zoom type enumeration
 */
enum class SmartZoomType {
    ContentAware,   // Zoom based on content density
    TextOptimal,    // Optimal zoom for text reading
    ImageOptimal,   // Optimal zoom for image viewing
    Adaptive        // Adaptive zoom based on content type
};

/**
 * @brief Advanced zoom manager for enhanced PDF viewing
 *
 * The AdvancedZoomManager provides sophisticated zoom functionality including:
 * - Smart zoom that adapts to content
 * - Enhanced magnifying glass with preview
 * - Smooth zoom animations
 * - Content-aware zoom suggestions
 * - Zoom history and presets
 *
 * @details
 * Key features include:
 * - **Smart Zoom**: Automatically determines optimal zoom based on content
 * - **Magnifying Glass**: Interactive magnifier with real-time preview
 * - **Smooth Animations**: Animated zoom transitions for better UX
 * - **Zoom Presets**: Quick access to common zoom levels
 * - **Content Analysis**: Analyzes page content for optimal zoom suggestions
 * - **Zoom History**: Navigate through previous zoom levels
 */
class AdvancedZoomManager : public QObject
{
    Q_OBJECT

public:
    explicit AdvancedZoomManager(QObject* parent = nullptr);
    ~AdvancedZoomManager();

    // Configuration
    void setDocumentTab(DocumentTab* tab);
    void setPdfController(PdfController* controller);
    void setPageLabel(QLabel* pageLabel);
    void setScrollArea(QScrollArea* scrollArea);

    // Zoom operations
    void setZoomFactor(double factor, bool animated = false);
    double getZoomFactor() const;
    void setZoomMode(ZoomMode mode);
    ZoomMode getZoomMode() const;

    // Smart zoom functionality
    void performSmartZoom(SmartZoomType type = SmartZoomType::Adaptive);
    double calculateOptimalZoom(SmartZoomType type) const;
    void suggestZoomLevel();

    // Magnifying glass
    void enableMagnifier(bool enabled);
    bool isMagnifierEnabled() const;
    void setMagnifierZoom(double factor);
    void setMagnifierSize(const QSize& size);

    // Zoom to specific areas
    void zoomToRect(const QRectF& rect, bool animated = true);
    void zoomToPoint(const QPointF& point, double factor, bool animated = true);
    void zoomToSelection(const QRectF& selection);

    // Zoom presets and history
    void addZoomPreset(double factor, const QString& name);
    void applyZoomPreset(const QString& name);
    QStringList getZoomPresets() const;
    void clearZoomHistory();
    bool canZoomBack() const;
    bool canZoomForward() const;

    // Animation settings
    void setAnimationDuration(int milliseconds);
    void setAnimationEasing(QEasingCurve::Type easing);

public slots:
    void zoomIn(bool animated = true);
    void zoomOut(bool animated = true);
    void zoomToFit();
    void zoomToWidth();
    void zoomToHeight();
    void zoomToActualSize();
    void zoomBack();
    void zoomForward();

signals:
    void zoomChanged(double factor);
    void zoomModeChanged(ZoomMode mode);
    void smartZoomSuggestion(double suggestedZoom, const QString& reason);
    void magnifierPositionChanged(const QPointF& position);

protected:
    bool eventFilter(QObject* object, QEvent* event) override;

private slots:
    void onAnimationFinished();
    void onMagnifierTimer();
    void updateMagnifier();

private:
    // Content analysis
    double analyzeContentDensity(int pageNumber) const;
    double analyzeTextDensity(int pageNumber) const;
    double analyzeImageDensity(int pageNumber) const;
    QRectF findContentBounds(int pageNumber) const;

    // Zoom calculations
    double calculateFitToWindowZoom() const;
    double calculateFitToWidthZoom() const;
    double calculateFitToHeightZoom() const;
    double calculateSmartFitZoom() const;

    // Animation helpers
    void animateZoomTo(double targetZoom);
    void updateZoomWithAnimation(double factor);

    // Magnifier helpers
    void createMagnifierWidget();
    void showMagnifier(const QPointF& position);
    void hideMagnifier();
    void updateMagnifierContent(const QPointF& position);

    // Member variables
    DocumentTab* m_documentTab;
    PdfController* m_pdfController;
    QLabel* m_pageLabel;
    QScrollArea* m_scrollArea;

    // Zoom state
    double m_currentZoom;
    ZoomMode m_zoomMode;
    QList<double> m_zoomHistory;
    int m_zoomHistoryIndex;
    QMap<QString, double> m_zoomPresets;

    // Animation
    QPropertyAnimation* m_zoomAnimation;
    int m_animationDuration;
    QEasingCurve::Type m_animationEasing;

    // Magnifier
    bool m_magnifierEnabled;
    QWidget* m_magnifierWidget;
    QLabel* m_magnifierLabel;
    double m_magnifierZoom;
    QSize m_magnifierSize;
    QTimer* m_magnifierTimer;
    QPointF m_magnifierPosition;

    // Constants
    static constexpr double MIN_ZOOM = 0.1;
    static constexpr double MAX_ZOOM = 10.0;
    static constexpr double DEFAULT_MAGNIFIER_ZOOM = 2.0;
    static constexpr int DEFAULT_ANIMATION_DURATION = 300;
    static constexpr int MAGNIFIER_UPDATE_INTERVAL = 50;
};

#endif // ADVANCEDZOOMMANAGER_H
