#include "FullScreenOverlay.h"
#include <QApplication>
#include <QStyle>
#include <QMouseEvent>

FullScreenOverlay::FullScreenOverlay(QWidget *parent)
    : QWidget(parent)
    , m_isVisible(false)
    , m_isAnimating(false)
{
    setupUi();
    
    // Set up auto-hide timer
    m_autoHideTimer = new QTimer(this);
    m_autoHideTimer->setSingleShot(true);
    m_autoHideTimer->setInterval(3000); // 3 seconds
    connect(m_autoHideTimer, &QTimer::timeout, this, &FullScreenOverlay::onAutoHideTimer);
    
    // Set up fade animation
    m_opacityEffect = new QGraphicsOpacityEffect(this);
    m_controlsWidget->setGraphicsEffect(m_opacityEffect);
    
    m_fadeAnimation = new QPropertyAnimation(m_opacityEffect, "opacity", this);
    m_fadeAnimation->setDuration(300);
    connect(m_fadeAnimation, &QPropertyAnimation::finished, this, [this]() {
        m_isAnimating = false;
        if (m_opacityEffect->opacity() == 0.0) {
            onFadeOutFinished();
        } else {
            onFadeInFinished();
        }
    });
    
    // Initially hidden
    m_controlsWidget->hide();
    setMouseTracking(true);
}

void FullScreenOverlay::setupUi()
{
    setObjectName("FullScreenOverlay");
    setAttribute(Qt::WA_TransparentForMouseEvents, false);
    
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);
    
    // Add stretch to push controls to bottom
    m_mainLayout->addStretch();
    
    // Create controls widget
    m_controlsWidget = new QWidget();
    m_controlsWidget->setObjectName("FullScreenControls");
    m_controlsWidget->setStyleSheet(
        "#FullScreenControls {"
        "    background-color: rgba(0, 0, 0, 180);"
        "    border-radius: 8px;"
        "    padding: 8px;"
        "}"
        "QPushButton {"
        "    background-color: rgba(255, 255, 255, 200);"
        "    border: 1px solid rgba(255, 255, 255, 100);"
        "    border-radius: 4px;"
        "    padding: 8px 12px;"
        "    color: black;"
        "    font-weight: bold;"
        "    min-width: 60px;"
        "}"
        "QPushButton:hover {"
        "    background-color: rgba(255, 255, 255, 255);"
        "    border: 1px solid rgba(255, 255, 255, 200);"
        "}"
        "QPushButton:pressed {"
        "    background-color: rgba(200, 200, 200, 255);"
        "}"
        "QLabel {"
        "    color: white;"
        "    font-weight: bold;"
        "    padding: 8px;"
        "}"
    );
    
    m_controlsLayout = new QHBoxLayout(m_controlsWidget);
    m_controlsLayout->setSpacing(8);
    
    // Navigation controls
    m_firstPageButton = new QPushButton(tr("First"));
    m_firstPageButton->setToolTip(tr("Go to first page"));
    connect(m_firstPageButton, &QPushButton::clicked, this, &FullScreenOverlay::firstPageRequested);
    
    m_prevPageButton = new QPushButton(tr("Previous"));
    m_prevPageButton->setToolTip(tr("Go to previous page"));
    connect(m_prevPageButton, &QPushButton::clicked, this, &FullScreenOverlay::previousPageRequested);
    
    m_pageInfoLabel = new QLabel(tr("Page 1 of 1"));
    m_pageInfoLabel->setAlignment(Qt::AlignCenter);
    m_pageInfoLabel->setMinimumWidth(100);
    
    m_nextPageButton = new QPushButton(tr("Next"));
    m_nextPageButton->setToolTip(tr("Go to next page"));
    connect(m_nextPageButton, &QPushButton::clicked, this, &FullScreenOverlay::nextPageRequested);
    
    m_lastPageButton = new QPushButton(tr("Last"));
    m_lastPageButton->setToolTip(tr("Go to last page"));
    connect(m_lastPageButton, &QPushButton::clicked, this, &FullScreenOverlay::lastPageRequested);
    
    // Zoom controls
    m_zoomOutButton = new QPushButton(tr("Zoom Out"));
    m_zoomOutButton->setToolTip(tr("Zoom out"));
    connect(m_zoomOutButton, &QPushButton::clicked, this, &FullScreenOverlay::zoomOutRequested);
    
    m_zoomLabel = new QLabel(tr("100%"));
    m_zoomLabel->setAlignment(Qt::AlignCenter);
    m_zoomLabel->setMinimumWidth(60);
    
    m_zoomInButton = new QPushButton(tr("Zoom In"));
    m_zoomInButton->setToolTip(tr("Zoom in"));
    connect(m_zoomInButton, &QPushButton::clicked, this, &FullScreenOverlay::zoomInRequested);
    
    m_fitToWindowButton = new QPushButton(tr("Fit Window"));
    m_fitToWindowButton->setToolTip(tr("Fit to window"));
    connect(m_fitToWindowButton, &QPushButton::clicked, this, &FullScreenOverlay::fitToWindowRequested);
    
    m_fitToWidthButton = new QPushButton(tr("Fit Width"));
    m_fitToWidthButton->setToolTip(tr("Fit to width"));
    connect(m_fitToWidthButton, &QPushButton::clicked, this, &FullScreenOverlay::fitToWidthRequested);
    
    // Exit control
    m_exitButton = new QPushButton(tr("Exit Full Screen"));
    m_exitButton->setToolTip(tr("Exit full screen mode"));
    m_exitButton->setStyleSheet(
        "QPushButton {"
        "    background-color: rgba(255, 100, 100, 200);"
        "    color: white;"
        "}"
        "QPushButton:hover {"
        "    background-color: rgba(255, 150, 150, 255);"
        "}"
    );
    connect(m_exitButton, &QPushButton::clicked, this, &FullScreenOverlay::exitFullScreenRequested);
    
    // Add controls to layout
    m_controlsLayout->addWidget(m_firstPageButton);
    m_controlsLayout->addWidget(m_prevPageButton);
    m_controlsLayout->addWidget(m_pageInfoLabel);
    m_controlsLayout->addWidget(m_nextPageButton);
    m_controlsLayout->addWidget(m_lastPageButton);
    m_controlsLayout->addStretch();
    m_controlsLayout->addWidget(m_zoomOutButton);
    m_controlsLayout->addWidget(m_zoomLabel);
    m_controlsLayout->addWidget(m_zoomInButton);
    m_controlsLayout->addWidget(m_fitToWindowButton);
    m_controlsLayout->addWidget(m_fitToWidthButton);
    m_controlsLayout->addStretch();
    m_controlsLayout->addWidget(m_exitButton);
    
    m_mainLayout->addWidget(m_controlsWidget, 0, Qt::AlignCenter);
    m_mainLayout->addSpacing(20); // Bottom margin
}

void FullScreenOverlay::setPageInfo(int currentPage, int totalPages)
{
    m_pageInfoLabel->setText(tr("Page %1 of %2").arg(currentPage + 1).arg(totalPages));
    
    // Enable/disable navigation buttons
    m_firstPageButton->setEnabled(currentPage > 0);
    m_prevPageButton->setEnabled(currentPage > 0);
    m_nextPageButton->setEnabled(currentPage < totalPages - 1);
    m_lastPageButton->setEnabled(currentPage < totalPages - 1);
}

void FullScreenOverlay::setZoomLevel(double zoomFactor)
{
    m_zoomLabel->setText(tr("%1%").arg(qRound(zoomFactor * 100)));
}

void FullScreenOverlay::showOverlay()
{
    if (m_isVisible || m_isAnimating) return;
    
    m_isVisible = true;
    m_controlsWidget->show();
    fadeIn();
    startAutoHideTimer();
}

void FullScreenOverlay::hideOverlay()
{
    if (!m_isVisible || m_isAnimating) return;
    
    stopAutoHideTimer();
    fadeOut();
}

void FullScreenOverlay::toggleOverlay()
{
    if (m_isVisible) {
        hideOverlay();
    } else {
        showOverlay();
    }
}

void FullScreenOverlay::enterEvent(QEnterEvent* event)
{
    Q_UNUSED(event);
    if (!m_isVisible) {
        showOverlay();
    } else {
        startAutoHideTimer();
    }
}

void FullScreenOverlay::leaveEvent(QEvent* event)
{
    Q_UNUSED(event);
    // Don't auto-hide when leaving the overlay area
    // User might be moving to the document area
}

void FullScreenOverlay::mouseMoveEvent(QMouseEvent* event)
{
    Q_UNUSED(event);
    if (!m_isVisible) {
        showOverlay();
    } else {
        startAutoHideTimer();
    }
}

void FullScreenOverlay::onAutoHideTimer()
{
    hideOverlay();
}

void FullScreenOverlay::onFadeInFinished()
{
    // Fade in complete
}

void FullScreenOverlay::onFadeOutFinished()
{
    m_isVisible = false;
    m_controlsWidget->hide();
}

void FullScreenOverlay::startAutoHideTimer()
{
    m_autoHideTimer->start();
}

void FullScreenOverlay::stopAutoHideTimer()
{
    m_autoHideTimer->stop();
}

void FullScreenOverlay::fadeIn()
{
    if (m_isAnimating) return;
    
    m_isAnimating = true;
    m_fadeAnimation->setStartValue(0.0);
    m_fadeAnimation->setEndValue(1.0);
    m_fadeAnimation->start();
}

void FullScreenOverlay::fadeOut()
{
    if (m_isAnimating) return;
    
    m_isAnimating = true;
    m_fadeAnimation->setStartValue(1.0);
    m_fadeAnimation->setEndValue(0.0);
    m_fadeAnimation->start();
}
