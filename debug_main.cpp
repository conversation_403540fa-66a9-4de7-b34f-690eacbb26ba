// debug_main.cpp - 诊断版本
#include "Logger.h"
#include "ErrorHandler.h"
#include "ElaIntegration.h"
#include <QApplication>
#include <QMainWindow>
#include <QLabel>
#include <QTimer>
#include <QDebug>
#include <iostream>

// 简单的测试窗口类
class SimpleTestWindow : public QMainWindow
{
    Q_OBJECT
public:
    explicit SimpleTestWindow(QWidget *parent = nullptr) : QMainWindow(parent)
    {
        setWindowTitle("Simple Test Window");
        setCentralWidget(new QLabel("Test Content", this));
        resize(400, 300);
    }
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 基本应用设置
    app.setApplicationName("Debug PDF Viewer");
    app.setApplicationVersion("1.0");
    app.setOrganizationName("PDF Viewer");
    
    std::cout << "=== Debug Main Started ===" << std::endl;
    
    // 初始化日志系统
    std::cout << "Initializing logger..." << std::endl;
    Logger* logger = Logger::instance();
    logger->setLogLevel(LogLevel::Debug);
    logger->info("Debug application starting up", "DebugMain");
    
    std::cout << "Initializing error handler..." << std::endl;
    ErrorHandler* errorHandler = ErrorHandler::instance();
    errorHandler->setShowUserNotifications(true);
    errorHandler->setupCrashHandler();
    logger->info("Error handler initialized", "DebugMain");
    
    // 测试 ElaIntegration
    std::cout << "Testing ElaIntegration..." << std::endl;
    logger->info("Testing ElaIntegration availability", "DebugMain");
    
    if (ElaIntegration::isElaWidgetsAvailable()) {
        logger->info("ElaWidgetTools is available", "DebugMain");
        std::cout << "ElaWidgetTools is available" << std::endl;
        
        try {
            std::cout << "Initializing ElaApplication..." << std::endl;
            ElaIntegration::initializeElaApplication();
            logger->info("ElaApplication initialized successfully", "DebugMain");
            
            std::cout << "Applying Ela theme..." << std::endl;
            ElaIntegration::applyElaTheme();
            logger->info("Ela theme applied successfully", "DebugMain");
        } catch (const std::exception& e) {
            std::cout << "ElaIntegration failed: " << e.what() << std::endl;
            logger->error(QString("ElaIntegration failed: %1").arg(e.what()), "DebugMain");
        } catch (...) {
            std::cout << "ElaIntegration failed with unknown error" << std::endl;
            logger->error("ElaIntegration failed with unknown error", "DebugMain");
        }
    } else {
        logger->info("ElaWidgetTools is not available", "DebugMain");
        std::cout << "ElaWidgetTools is not available" << std::endl;
    }
    
    // 测试简单窗口创建
    std::cout << "Creating simple test window..." << std::endl;
    logger->info("Creating simple test window", "DebugMain");
    
    try {
        SimpleTestWindow window;
        logger->info("Simple window created successfully", "DebugMain");
        
        std::cout << "Showing simple window..." << std::endl;
        window.show();
        logger->info("Simple window shown successfully", "DebugMain");
        
        std::cout << "=== Debug Main Completed Successfully ===" << std::endl;
        
        // 设置一个定时器来确保应用能够正常退出
        QTimer::singleShot(5000, &app, &QApplication::quit);
        
        return app.exec();
        
    } catch (const std::exception& e) {
        std::cout << "Simple window creation failed: " << e.what() << std::endl;
        logger->error(QString("Simple window creation failed: %1").arg(e.what()), "DebugMain");
        return -1;
    } catch (...) {
        std::cout << "Simple window creation failed with unknown error" << std::endl;
        logger->error("Simple window creation failed with unknown error", "DebugMain");
        return -1;
    }
}

#include "debug_main.moc"
