/**
 * @file TextSelectionOverlay.cpp
 * @brief Implementation of text selection overlay for PDF documents
 */

#include "TextSelectionOverlay.h"
#include "PdfController.h"
#include <QDebug>
#include <QKeyEvent>
#include <QPainterPath>
#include <QGuiApplication>

TextSelectionOverlay::TextSelectionOverlay(QWidget* parent)
    : QWidget(parent)
    , m_pageLabel(nullptr)
    , m_pdfController(nullptr)
    , m_currentPageNumber(0)
    , m_zoomFactor(1.0)
    , m_dpi(DEFAULT_DPI)
    , m_textSelectionEnabled(false)
    , m_isSelecting(false)
    , m_selectionStart(0, 0)
    , m_selectionEnd(0, 0)
    , m_selectionTimer(new QTimer(this))
    , m_clipboard(QGuiApplication::clipboard())
{
    setAttribute(Qt::WA_TransparentForMouseEvents, false);
    setAttribute(Qt::WA_NoSystemBackground, true);
    setFocusPolicy(Qt::StrongFocus);

    // Setup selection timer for smooth updates
    m_selectionTimer->setSingleShot(false);
    m_selectionTimer->setInterval(SELECTION_TIMER_INTERVAL);
    connect(m_selectionTimer, &QTimer::timeout, this, &TextSelectionOverlay::onSelectionTimer);
}

TextSelectionOverlay::~TextSelectionOverlay()
{
}

void TextSelectionOverlay::setPageLabel(QLabel* pageLabel)
{
    m_pageLabel = pageLabel;
    if (m_pageLabel) {
        setParent(qobject_cast<QWidget*>(m_pageLabel->parent()));
        resize(m_pageLabel->size());
        move(m_pageLabel->pos());
    }
}

void TextSelectionOverlay::setPdfController(PdfController* controller)
{
    m_pdfController = controller;
}

void TextSelectionOverlay::setCurrentPage(int pageNumber)
{
    if (m_currentPageNumber != pageNumber) {
        clearSelection();
        m_currentPageNumber = pageNumber;
    }
}

void TextSelectionOverlay::setZoomFactor(double zoomFactor)
{
    m_zoomFactor = zoomFactor;
    update(); // Redraw selection with new zoom
}

void TextSelectionOverlay::setDpi(double dpi)
{
    m_dpi = dpi;
    update(); // Redraw selection with new DPI
}

void TextSelectionOverlay::clearSelection()
{
    if (m_currentSelection.isValid()) {
        m_currentSelection = TextSelection();
        m_isSelecting = false;
        m_selectionTimer->stop();
        update();
        emit selectionCleared();
    }
}

bool TextSelectionOverlay::hasSelection() const
{
    return m_currentSelection.isValid();
}

QString TextSelectionOverlay::getSelectedText() const
{
    return m_currentSelection.text;
}

void TextSelectionOverlay::copySelectionToClipboard()
{
    if (hasSelection() && m_clipboard) {
        m_clipboard->setText(m_currentSelection.text);
    }
}

void TextSelectionOverlay::setTextSelectionEnabled(bool enabled)
{
    if (m_textSelectionEnabled != enabled) {
        m_textSelectionEnabled = enabled;
        if (!enabled) {
            clearSelection();
        }
        setAttribute(Qt::WA_TransparentForMouseEvents, !enabled);
    }
}

void TextSelectionOverlay::paintEvent(QPaintEvent* event)
{
    Q_UNUSED(event);
    
    if (!m_textSelectionEnabled || !hasSelection()) {
        return;
    }

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    drawSelection(&painter);
}

void TextSelectionOverlay::mousePressEvent(QMouseEvent* event)
{
    if (!m_textSelectionEnabled || event->button() != Qt::LeftButton) {
        QWidget::mousePressEvent(event);
        return;
    }

    QPointF position = event->position();
    startSelection(position);
    event->accept();
}

void TextSelectionOverlay::mouseMoveEvent(QMouseEvent* event)
{
    if (!m_textSelectionEnabled || !m_isSelecting) {
        QWidget::mouseMoveEvent(event);
        return;
    }

    QPointF position = event->position();
    updateSelection(position);
    event->accept();
}

void TextSelectionOverlay::mouseReleaseEvent(QMouseEvent* event)
{
    if (!m_textSelectionEnabled || event->button() != Qt::LeftButton || !m_isSelecting) {
        QWidget::mouseReleaseEvent(event);
        return;
    }

    finishSelection();
    event->accept();
}

void TextSelectionOverlay::keyPressEvent(QKeyEvent* event)
{
    if (event->matches(QKeySequence::Copy) && hasSelection()) {
        copySelectionToClipboard();
        event->accept();
        return;
    }
    
    if (event->key() == Qt::Key_Escape && hasSelection()) {
        clearSelection();
        event->accept();
        return;
    }

    QWidget::keyPressEvent(event);
}

void TextSelectionOverlay::onSelectionTimer()
{
    if (m_isSelecting) {
        update();
    }
}

QPointF TextSelectionOverlay::pixelToPdfCoordinates(const QPointF& pixelPoint) const
{
    if (!m_pageLabel) return pixelPoint;
    
    // Convert from widget coordinates to PDF coordinates (points)
    const double pointsToPixels = (m_dpi * m_zoomFactor) / 72.0;
    
    // Get the position relative to the page label
    QPointF labelPoint = pixelPoint;
    if (m_pageLabel->parent()) {
        labelPoint = m_pageLabel->mapFromParent(pixelPoint.toPoint());
    }
    
    // Convert to PDF coordinates
    return QPointF(labelPoint.x() / pointsToPixels, labelPoint.y() / pointsToPixels);
}

QPointF TextSelectionOverlay::pdfToPixelCoordinates(const QPointF& pdfPoint) const
{
    if (!m_pageLabel) return pdfPoint;
    
    // Convert from PDF coordinates (points) to widget coordinates
    const double pointsToPixels = (m_dpi * m_zoomFactor) / 72.0;
    
    QPointF pixelPoint(pdfPoint.x() * pointsToPixels, pdfPoint.y() * pointsToPixels);
    
    // Convert to parent coordinates if needed
    if (m_pageLabel->parent()) {
        pixelPoint = m_pageLabel->mapToParent(pixelPoint.toPoint());
    }
    
    return pixelPoint;
}

QRectF TextSelectionOverlay::pixelToPdfRect(const QRectF& pixelRect) const
{
    QPointF topLeft = pixelToPdfCoordinates(pixelRect.topLeft());
    QPointF bottomRight = pixelToPdfCoordinates(pixelRect.bottomRight());
    return QRectF(topLeft, bottomRight);
}

QRectF TextSelectionOverlay::pdfToPixelRect(const QRectF& pdfRect) const
{
    QPointF topLeft = pdfToPixelCoordinates(pdfRect.topLeft());
    QPointF bottomRight = pdfToPixelCoordinates(pdfRect.bottomRight());
    return QRectF(topLeft, bottomRight);
}

void TextSelectionOverlay::startSelection(const QPointF& point)
{
    clearSelection();
    m_isSelecting = true;
    m_selectionStart = point;
    m_selectionEnd = point;
    m_selectionTimer->start();
}

void TextSelectionOverlay::updateSelection(const QPointF& point)
{
    if (!m_isSelecting) return;
    
    m_selectionEnd = point;
    // Update will be triggered by timer for smooth performance
}

void TextSelectionOverlay::finishSelection()
{
    if (!m_isSelecting) return;

    m_isSelecting = false;
    m_selectionTimer->stop();

    // Extract text from the selected region
    extractSelectedText();

    if (hasSelection()) {
        emit selectionChanged(m_currentSelection.text);
    }

    update();
}

void TextSelectionOverlay::extractSelectedText()
{
    if (!m_pdfController || !m_pageLabel) return;

    // Create selection rectangle in PDF coordinates
    QPointF pdfStart = pixelToPdfCoordinates(m_selectionStart);
    QPointF pdfEnd = pixelToPdfCoordinates(m_selectionEnd);
    QRectF selectionRect = QRectF(pdfStart, pdfEnd).normalized();

    // Minimum selection size check
    if (selectionRect.width() < 5 || selectionRect.height() < 5) {
        return;
    }

    // Get page text and try to extract text from the selected region
    QString pageText = m_pdfController->getPageText(m_currentPageNumber);
    if (pageText.isEmpty()) {
        return;
    }

    // For now, we'll use a simple approach - get all text and estimate
    // which portion falls within the selection rectangle
    // This is a simplified implementation; a more sophisticated version
    // would use Poppler's text box extraction capabilities

    m_currentSelection.pageNumber = m_currentPageNumber;
    m_currentSelection.startPoint = pdfStart;
    m_currentSelection.endPoint = pdfEnd;
    m_currentSelection.rects.clear();
    m_currentSelection.rects.append(selectionRect);

    // For this implementation, we'll extract a portion of the page text
    // based on the relative position of the selection
    QSizeF pageSize = m_pdfController->getPageSize(m_currentPageNumber);
    if (!pageSize.isEmpty()) {
        double relativeStart = (selectionRect.top() / pageSize.height());
        double relativeEnd = (selectionRect.bottom() / pageSize.height());

        // Clamp values
        relativeStart = qBound(0.0, relativeStart, 1.0);
        relativeEnd = qBound(0.0, relativeEnd, 1.0);

        // Extract text portion
        int textStart = static_cast<int>(relativeStart * pageText.length());
        int textEnd = static_cast<int>(relativeEnd * pageText.length());

        if (textEnd > textStart) {
            QString selectedText = pageText.mid(textStart, textEnd - textStart);
            // Clean up the text - remove excessive whitespace
            selectedText = selectedText.simplified();
            m_currentSelection.text = selectedText;
        }
    }
}

QList<QRectF> TextSelectionOverlay::getTextRectsInRegion(const QRectF& region) const
{
    // This is a placeholder for more sophisticated text rectangle extraction
    // In a full implementation, this would use Poppler's text box functionality
    // to get precise text rectangles within the selection region
    QList<QRectF> rects;
    rects.append(region);
    return rects;
}

void TextSelectionOverlay::drawSelection(QPainter* painter)
{
    if (m_isSelecting) {
        // Draw current selection being made
        QRectF selectionRect = QRectF(m_selectionStart, m_selectionEnd).normalized();
        drawSelectionRect(painter, selectionRect);
    } else if (hasSelection()) {
        // Draw completed selection
        for (const QRectF& rect : m_currentSelection.rects) {
            QRectF pixelRect = pdfToPixelRect(rect);
            drawSelectionRect(painter, pixelRect);
        }
    }
}

void TextSelectionOverlay::drawSelectionRect(QPainter* painter, const QRectF& rect)
{
    // Selection highlight color (semi-transparent blue)
    QColor highlightColor(0, 120, 215, 80); // Windows-style selection color
    QColor borderColor(0, 120, 215, 150);

    // Fill the selection rectangle
    painter->fillRect(rect, highlightColor);

    // Draw border
    QPen borderPen(borderColor, 1.0);
    painter->setPen(borderPen);
    painter->drawRect(rect);
}
