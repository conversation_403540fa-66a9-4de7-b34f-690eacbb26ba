#ifndef BOOKMARKDIALOG_H
#define BOOKMARKDIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QListWidget>
#include <QPushButton>
#include <QLineEdit>
#include <QLabel>
#include <QSettings>
#include <QDateTime>

struct Bookmark {
    QString name;
    QString filePath;
    int pageNumber;
    double zoomFactor;
    QString fileName;
    QDateTime created;
};

class BookmarkDialog : public QDialog
{
    Q_OBJECT

public:
    explicit BookmarkDialog(QSettings* settings, QWidget *parent = nullptr);
    
    void setCurrentDocument(const QString& filePath, int currentPage, double currentZoom);
    void refreshBookmarks();

signals:
    void bookmarkSelected(const QString& filePath, int pageNumber, double zoomFactor);

private slots:
    void onAddBookmark();
    void onEditBookmark();
    void onDeleteBookmark();
    void onGoToBookmark();
    void onBookmarkDoubleClicked(QListWidgetItem* item);
    void onSelectionChanged();

private:
    void setupUi();
    void loadBookmarks();
    void saveBookmark(const Bookmark& bookmark);
    void deleteBookmark(const QString& key);
    QString getBookmarkKey(const Bookmark& bookmark) const;
    Bookmark getBookmarkFromKey(const QString& key) const;
    void updateButtonStates();

    // UI components
    QVBoxLayout* m_mainLayout;
    QListWidget* m_bookmarksList;
    QHBoxLayout* m_buttonLayout;
    QPushButton* m_addButton;
    QPushButton* m_editButton;
    QPushButton* m_deleteButton;
    QPushButton* m_goToButton;
    QPushButton* m_closeButton;
    
    // Data
    QSettings* m_settings;
    QString m_currentFilePath;
    int m_currentPage;
    double m_currentZoom;
    QList<Bookmark> m_bookmarks;
};

#endif // BOOKMARKDIALOG_H
