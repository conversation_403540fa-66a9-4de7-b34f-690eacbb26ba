#ifndef PERFORMANCEDASHBOARD_H
#define PERFORMANCEDASHBOARD_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QProgressBar>
#include <QListWidget>
#include <QTextEdit>
#include <QPushButton>
#include <QGroupBox>
#include <QTimer>
#include <QChart>
#include <QChartView>
#include <QLineSeries>
#include <QValueAxis>
#include <QDateTimeAxis>

#include "PerformanceMonitor.h"

QT_CHARTS_USE_NAMESPACE

/**
 * @brief Performance monitoring dashboard widget
 * 
 * This widget provides a comprehensive view of application performance
 * metrics, including real-time charts, statistics, and optimization
 * recommendations.
 */
class PerformanceDashboard : public QWidget
{
    Q_OBJECT

public:
    explicit PerformanceDashboard(PerformanceMonitor* monitor, QWidget* parent = nullptr);
    ~PerformanceDashboard();

    // Dashboard control
    void startRealTimeUpdates();
    void stopRealTimeUpdates();
    void refreshData();
    
    // Display options
    void setUpdateInterval(int milliseconds);
    void setChartTimeRange(int minutes);
    void showCategory(const QString& category);
    void hideCategory(const QString& category);

public slots:
    void onMetricsUpdated();
    void onPerformanceAlert(const QString& message, const QString& category);
    void onOptimizationRecommended(const QString& recommendation);

private slots:
    void updateRealTimeData();
    void exportMetrics();
    void clearMetrics();
    void optimizePerformance();
    void showDetailedStats();

private:
    void setupUI();
    void createOverviewSection();
    void createChartsSection();
    void createStatsSection();
    void createRecommendationsSection();
    void createControlsSection();
    
    void updateOverviewMetrics();
    void updateCharts();
    void updateStatistics();
    void updateRecommendations();
    
    void createMemoryChart();
    void createCpuChart();
    void createRenderingChart();
    void createCacheChart();
    
    void applyDashboardStyling();
    
    // Core components
    PerformanceMonitor* m_monitor;
    QTimer* m_updateTimer;
    int m_updateInterval;
    int m_chartTimeRange;
    
    // Layout
    QVBoxLayout* m_mainLayout;
    QHBoxLayout* m_topLayout;
    QHBoxLayout* m_bottomLayout;
    
    // Overview section
    QGroupBox* m_overviewGroup;
    QGridLayout* m_overviewLayout;
    QLabel* m_memoryLabel;
    QLabel* m_cpuLabel;
    QLabel* m_threadsLabel;
    QLabel* m_cacheHitRatioLabel;
    QProgressBar* m_memoryProgress;
    QProgressBar* m_cpuProgress;
    
    // Charts section
    QGroupBox* m_chartsGroup;
    QVBoxLayout* m_chartsLayout;
    QChartView* m_memoryChartView;
    QChartView* m_cpuChartView;
    QChartView* m_renderingChartView;
    QChartView* m_cacheChartView;
    
    QChart* m_memoryChart;
    QChart* m_cpuChart;
    QChart* m_renderingChart;
    QChart* m_cacheChart;
    
    QLineSeries* m_memorySeries;
    QLineSeries* m_cpuSeries;
    QLineSeries* m_renderTimeSeries;
    QLineSeries* m_cacheHitSeries;
    
    // Statistics section
    QGroupBox* m_statsGroup;
    QVBoxLayout* m_statsLayout;
    QTextEdit* m_statsText;
    
    // Recommendations section
    QGroupBox* m_recommendationsGroup;
    QVBoxLayout* m_recommendationsLayout;
    QListWidget* m_recommendationsList;
    QPushButton* m_optimizeButton;
    
    // Controls section
    QGroupBox* m_controlsGroup;
    QHBoxLayout* m_controlsLayout;
    QPushButton* m_exportButton;
    QPushButton* m_clearButton;
    QPushButton* m_refreshButton;
    QPushButton* m_detailsButton;
    
    // State
    QStringList m_visibleCategories;
    QDateTime m_lastUpdate;
};

/**
 * @brief Compact performance widget for status bar or toolbar
 */
class PerformanceWidget : public QWidget
{
    Q_OBJECT

public:
    explicit PerformanceWidget(PerformanceMonitor* monitor, QWidget* parent = nullptr);

    void setCompactMode(bool compact);
    void showMemoryOnly(bool memoryOnly);

private slots:
    void updateDisplay();
    void showDashboard();

private:
    void setupUI();
    void updateMemoryDisplay();
    void updateCpuDisplay();
    
    PerformanceMonitor* m_monitor;
    QTimer* m_updateTimer;
    
    QHBoxLayout* m_layout;
    QLabel* m_memoryLabel;
    QLabel* m_cpuLabel;
    QPushButton* m_dashboardButton;
    
    bool m_compactMode;
    bool m_memoryOnly;
    
    PerformanceDashboard* m_dashboard;
};

#endif // PERFORMANCEDASHBOARD_H
