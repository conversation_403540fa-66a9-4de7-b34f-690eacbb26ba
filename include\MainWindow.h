/**
 * @file MainWindow.h
 * @brief Main application window with modern ribbon interface
 * <AUTHOR> Viewer Team
 * @date 2024-12-01
 * @version 1.0.0
 *
 * This file contains the MainWindow class which provides the main
 * application interface using ElaWidgetTools for a modern, Microsoft
 * Office-style appearance with comprehensive PDF viewing capabilities.
 */

#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include "ElaIntegration.h"
#include "PdfController.h"
#include "DocumentTab.h"
#include "DocumentationViewer.h"
#include "AnnotationToolbar.h"
#include "WelcomeScreen.h"
#include "LoadingOverlay.h"
#include "ModernContextMenu.h"
#include "RichTooltip.h"
#include "OnboardingTour.h"
#include "RibbonInterface.h"
#include "SearchHistory.h"
#include "SearchWidget.h"

struct SearchResult; ///< Forward declaration for search functionality
struct CrashInfo; ///< Forward declaration for crash reporting

class QAction;
class QSplitter;
class QHBoxLayout;
class QWidget;
class QSettings;
class QPrinter;
class SettingsDialog;
class SearchResultsPanel;

// Forward declarations for Ela widgets (will fallback to Qt if not available)
class QListWidgetItem;
class QTreeWidgetItem;

/**
 * @brief Main application window class with modern ribbon interface
 *
 * The MainWindow class serves as the primary user interface for the Optimized
 * PDF Viewer application. It provides a sophisticated, Microsoft Office-style
 * ribbon interface built with ElaWidgetTools, supporting multiple document tabs,
 * comprehensive annotation tools, and extensive customization options.
 *
 * @details
 * Key features include:
 * - Modern ribbon-style toolbar with contextual tabs and commands
 * - Multiple document tabs with drag-and-drop support and tab management
 * - Integrated annotation tools with real-time preview and editing
 * - Customizable themes with light/dark mode and accent color support
 * - Performance monitoring with memory usage tracking and optimization
 * - Comprehensive error handling with crash reporting and recovery
 * - Accessibility support with keyboard navigation and screen reader compatibility
 * - Plugin architecture for extensibility and custom functionality
 *
 * The window automatically adapts its interface based on the current document
 * type and user preferences, providing contextual tools and optimized workflows
 * for different PDF viewing and editing scenarios.
 *
 * @example Basic usage:
 * @code
 * MainWindow window;
 * window.show();
 *
 * // Open a document
 * if (window.openDocument("example.pdf")) {
 *     qDebug() << "Document opened successfully";
 * }
 *
 * // Access current document tab
 * DocumentTab* currentTab = window.getCurrentDocumentTab();
 * if (currentTab) {
 *     currentTab->setZoomLevel(150); // Set zoom to 150%
 * }
 * @endcode
 *
 * @example Advanced usage with annotations:
 * @code
 * MainWindow window;
 * window.show();
 * window.openDocument("document.pdf");
 *
 * // Enable annotation mode
 * window.setAnnotationMode(true);
 *
 * // Add a highlight annotation
 * AnnotationManager* annotMgr = window.getAnnotationManager();
 * annotMgr->createHighlight(QRectF(100, 100, 200, 20), Qt::yellow);
 * @endcode
 *
 * @note This class is thread-safe for document operations but UI updates
 *       must be performed on the main thread.
 *
 * @warning Always call show() before attempting to open documents to ensure
 *          proper initialization of the rendering context.
 *
 * @see DocumentTab, AnnotationManager, RibbonInterface, ElaMainWindow
 * @since 1.0.0
 */
class MainWindow : public ElaMainWindow
{
    Q_OBJECT

public:
    /**
     * @brief Constructs a new MainWindow instance
     * @param parent Parent widget, typically nullptr for main windows
     *
     * Initializes the main window with default settings, sets up the modern
     * ribbon interface, prepares the welcome screen, and configures all
     * necessary components for PDF viewing and annotation.
     *
     * The constructor performs the following initialization steps:
     * - Sets up the ElaWidgetTools integration for modern UI
     * - Initializes the ribbon interface with all tool sections
     * - Configures document tab management and drag-and-drop support
     * - Sets up annotation tools and property panels
     * - Loads user preferences and applies saved themes
     * - Initializes performance monitoring and error handling
     *
     * @note The window is not shown by default; call show() explicitly.
     */
    explicit MainWindow(QWidget *parent = nullptr);

    /**
     * @brief Destructor for MainWindow
     *
     * Performs cleanup operations including:
     * - Saving current user preferences and window state
     * - Closing all open documents and releasing resources
     * - Cleaning up annotation data and temporary files
     * - Shutting down background threads and timers
     * - Releasing graphics resources and memory caches
     */
    ~MainWindow();

    // Public API methods

    /**
     * @brief Opens a PDF document in a new tab
     * @param filePath Absolute path to the PDF file to open
     * @return true if the document was opened successfully, false otherwise
     *
     * Opens the specified PDF document in a new tab. If the document is
     * already open in another tab, switches to that tab instead of creating
     * a duplicate. The method performs validation of the file path and format
     * before attempting to load the document.
     *
     * @note This method is thread-safe and can be called from any thread.
     *       The actual UI updates will be performed on the main thread.
     *
     * @warning Large PDF files may take significant time to load. Consider
     *          using openDocumentAsync() for better user experience.
     *
     * @see openDocumentAsync(), closeDocument(), getCurrentDocumentTab()
     * @since 1.0.0
     */
    bool openDocument(const QString& filePath);

    /**
     * @brief Asynchronously opens a PDF document
     * @param filePath Path to the PDF file to open
     *
     * Opens the specified PDF document asynchronously, showing a loading
     * overlay while the document is being processed. This method returns
     * immediately and emits documentOpened() signal when complete.
     *
     * @see documentOpened(), documentLoadFailed()
     * @since 1.0.0
     */
    void openDocumentAsync(const QString& filePath);

    /**
     * @brief Closes the currently active document tab
     * @return true if a document was closed, false if no document was open
     *
     * Closes the currently active document tab after prompting the user
     * to save any unsaved annotations. If this is the last tab, shows
     * the welcome screen.
     *
     * @see closeAllDocuments(), openDocument()
     * @since 1.0.0
     */
    bool closeCurrentDocument();

    /**
     * @brief Gets the currently active document tab
     * @return Pointer to the current DocumentTab, or nullptr if no document is open
     *
     * Returns a pointer to the currently active document tab, which can be
     * used to access document-specific functionality like zoom, annotations,
     * and navigation.
     *
     * @note The returned pointer is valid only while the tab remains open.
     *       Do not store this pointer for extended periods.
     *
     * @see getDocumentTabCount(), getDocumentTabAt()
     * @since 1.0.0
     */
    DocumentTab* getCurrentDocumentTab() const;

    /**
     * @brief Gets the annotation manager for the current document
     * @return Pointer to AnnotationManager, or nullptr if no document is open
     *
     * Provides access to the annotation manager for the currently active
     * document, allowing programmatic creation and manipulation of annotations.
     *
     * @see getCurrentDocumentTab(), AnnotationManager
     * @since 1.0.0
     */
    AnnotationManager* getAnnotationManager() const;

    /**
     * @brief Sets the application theme mode
     * @param mode Theme mode (Light, Dark, or Auto)
     *
     * Changes the application theme and updates all UI elements accordingly.
     * The Auto mode follows the system theme preference.
     *
     * @see ElaThemeType::ThemeMode
     * @since 1.0.0
     */
    void setThemeMode(ElaThemeType::ThemeMode mode);

    /**
     * @brief Enables or disables annotation mode
     * @param enabled true to enable annotation mode, false to disable
     *
     * When annotation mode is enabled, the annotation toolbar becomes
     * visible and annotation tools are activated. When disabled, returns
     * to normal viewing mode.
     *
     * @see isAnnotationModeEnabled(), getAnnotationManager()
     * @since 1.0.0
     */
    void setAnnotationMode(bool enabled);

    /**
     * @brief Checks if annotation mode is currently enabled
     * @return true if annotation mode is enabled, false otherwise
     *
     * @see setAnnotationMode()
     * @since 1.0.0
     */
    bool isAnnotationModeEnabled() const;

signals:
    /**
     * @brief Emitted when a document is successfully opened
     * @param filePath Path to the opened document
     * @param tab Pointer to the document tab containing the document
     *
     * This signal is emitted after a document has been successfully loaded
     * and displayed in a tab. Connect to this signal to perform actions
     * when documents are opened, such as updating recent files or logging.
     *
     * @note The tab pointer is guaranteed to be valid when this signal is emitted.
     *
     * @see documentLoadFailed(), documentClosed()
     */
    void documentOpened(const QString& filePath, DocumentTab* tab);

    /**
     * @brief Emitted when document loading fails
     * @param filePath Path to the document that failed to load
     * @param errorMessage Detailed error message describing the failure
     *
     * This signal is emitted when a document cannot be loaded due to
     * file format issues, corruption, permissions, or other errors.
     *
     * @see documentOpened()
     */
    void documentLoadFailed(const QString& filePath, const QString& errorMessage);

    /**
     * @brief Emitted when a document tab is closed
     * @param filePath Path to the closed document
     *
     * This signal is emitted after a document tab has been closed and
     * all associated resources have been cleaned up.
     *
     * @see documentOpened()
     */
    void documentClosed(const QString& filePath);

    /**
     * @brief Emitted when the application theme changes
     * @param mode New theme mode
     *
     * This signal is emitted when the user changes the application theme
     * or when the system theme changes in Auto mode.
     *
     * @see setThemeMode()
     */
    void themeChanged(ElaThemeType::ThemeMode mode);

    /**
     * @brief Emitted when annotation mode is toggled
     * @param enabled true if annotation mode was enabled, false if disabled
     *
     * This signal is emitted when annotation mode is enabled or disabled,
     * either programmatically or through user interaction.
     *
     * @see setAnnotationMode()
     */
    void annotationModeChanged(bool enabled);

public slots:
    /**
     * @brief Shows the application settings dialog
     *
     * Displays the comprehensive settings dialog where users can configure
     * application preferences, themes, performance options, and more.
     *
     * @see SettingsDialog
     * @since 1.0.0
     */
    void showSettingsDialog();

    /**
     * @brief Shows the about dialog
     *
     * Displays information about the application including version,
     * build information, and credits.
     *
     * @since 1.0.0
     */
    void showAboutDialog();

    /**
     * @brief Toggles full-screen mode
     *
     * Switches between windowed and full-screen viewing modes.
     * In full-screen mode, all UI elements except the document
     * view are hidden for distraction-free reading.
     *
     * @see enterFullScreen(), exitFullScreen()
     * @since 1.0.0
     */
    void toggleFullScreen();

protected:
    void resizeEvent(QResizeEvent* event) override;
    void closeEvent(QCloseEvent* event) override;
    bool eventFilter(QObject* object, QEvent* event) override;
    void mousePressEvent(QMouseEvent* event) override;
    void mouseMoveEvent(QMouseEvent* event) override;
    void mouseReleaseEvent(QMouseEvent* event) override;
    void dragEnterEvent(QDragEnterEvent* event) override;
    void dragMoveEvent(QDragMoveEvent* event) override;
    void dragLeaveEvent(QDragLeaveEvent* event) override;
    void dropEvent(QDropEvent* event) override;

private slots:
    void openPdf();
    void openPdfFile(const QString& filePath);
    void nextPage();
    void previousPage();
    void zoomIn();
    void zoomOut();
    void jumpToPage(int page);
    void fitToWindow();
    void fitToWidth();
    void firstPage();
    void lastPage();
    void zoom100();
    void toggleThumbnails();
    void onThumbnailClicked(QListWidgetItem* item);
    void toggleOutline();
    void onOutlineItemClicked(QTreeWidgetItem* item, int column);
    void onOutlineContextMenu(const QPoint& pos);
    void toggleSearchResults();
    void enterFullScreen();
    void exitFullScreen();
    void toggleSelectionZoom();
    void toggleTextSelection();
    void copySelectedText();
    void clearTextSelection();
    void performSmartZoom();
    void toggleAdvancedMagnifier();
    void toggleMagnifier();
    void showCustomZoomDialog();
    void onZoomComboChanged(const QString& text);
    void onZoomSliderChanged(int value);
    void newTab();
    void closeTab(int index);
    void closeCurrentTab();
    void onTabChanged(int index);
    void showSearchBar();
    void hideSearchBar();
    void searchText();
    void findNext();
    void findPrevious();
    void addBookmark();
    void goToBookmark();
    void openRecentFile();
    void clearRecentFiles();
    void rotateClockwise();
    void rotateCounterClockwise();
    void rotate180();
    void setSinglePageMode();
    void setContinuousPageMode();
    void setFacingPageMode();
    void resetRotation();
    void clearCache();
    void showMemoryUsage();
    void exportCurrentPage();
    void exportAllPages();
    void exportPageRange();
    void printDocument();
    void printCurrentPage();
    void showPrintPreview();
    void showSettings();
    void showHelp();
    void showDocumentInfo();

    // Annotation actions
    void undoAnnotation();
    void redoAnnotation();
    void updateUndoRedoActions();
    void searchAnnotations();
    void copyAnnotations();
    void pasteAnnotations();

    // Slots to receive signals from the controller
    void onDocumentLoaded(bool success, const QString& errorString);
    void onPageReady(int pageNum, const QPixmap& pixmap, bool isHighQuality);
    void onPreviewProgressUpdated(int pagesRendered, int totalPages);
    void onMemoryUsageChanged(qint64 bytesUsed, int cachedPages);
    void onSearchCompleted(const QList<SearchResult>& results);
    void onSearchProgress(int pagesSearched, int totalPages);
    void onOutlineReady(const QList<OutlineItem>& outline);
    void onSearchResultSelected(int pageNumber, const QRectF& rect);
    void onSearchRequested(const QString& term, bool caseSensitive, bool wholeWords);
    void onSearchPanelClosed();
    void onEnhancedSearchRequested(const QString& term, bool caseSensitive, bool wholeWords);
    void onEnhancedSearchCleared();

    // Welcome screen slots
    void onWelcomeOpenFileRequested();
    void onWelcomeNewDocumentRequested();
    void onWelcomeRecentFileSelected(const QString& filePath);
    void onWelcomeSettingsRequested();
    void onWelcomeAboutRequested();
    void onWelcomeHelpRequested();

private:
    void createUi();
    void createActions();
    void createMenus();
    void createToolBar();
    void createAnnotationToolbar();
    void createStatusBar();

    void requestCurrentPage();
    void updateUiState();
    void applyFitMode();
    void createThumbnailsPanel();
    void updateThumbnails();
    void createOutlinePanel();
    void updateOutline();
    void createSearchResultsPanel();
    void createSearchBar();
    void loadSettings();
    void saveSettings();
    void updateRecentFilesMenu();
    void saveSession();
    void restoreSession();
    void removeFromRecentFiles(const QString& filePath);
    void addToRecentFiles(const QString& filePath);
    void applySettings();
    void applyTheme(ElaThemeType::ThemeMode themeMode);
    void navigateToSearchResult(int index);
    void updateSearchResultsDisplay();
    void addOutlineItemToTree(QTreeWidgetItem* parent, const OutlineItem& item);
    void updateFullScreenPage();
    void updateZoomControls();
    void performSelectionZoom(const QRect& selectionRect);

    // Responsive layout methods
    void handleResponsivePanels(int width);
    void handleResponsiveNavigation(int width);
    void handleResponsiveToolbar(int width);
    void handleResponsiveTabWidget(int width, int height);

    // UI optimization methods
    void scheduleUiUpdate();
    void performScheduledUiUpdate();

    // Welcome screen methods
    void createWelcomeScreen();
    void showWelcomeScreen();
    void hideWelcomeScreen();

    // Enhanced UX methods
    void setupRichTooltips();
    void createOnboardingTour();
    void startOnboardingTour();
    ModernContextMenu* createDocumentContextMenu();
    ModernContextMenu* createPageContextMenu();
    void updateWelcomeScreenRecentFiles();

    // Navigation methods
    void initWindow();
    void initNavigation();
    void initContent();
    void setupRibbonNavigation();
    void applyDesignSystemStyling();
    void performMagnifierZoom(const QPoint& clickPos);
    DocumentTab* getCurrentTab() const;
    DocumentTab* createNewTab();
    DocumentationViewer* createDocumentationTab();
    void updateTabTitle(DocumentTab* tab);

    // Annotation methods
    void onAnnotationToolChanged(AnnotationTool tool);
    void onAnnotationColorChanged(const QColor& color);
    void onAnnotationOpacityChanged(qreal opacity);
    void onAnnotationLineWidthChanged(qreal width);
    void onDeleteSelectedAnnotations();
    void onCopySelectedAnnotations();
    void onPasteAnnotations();

    // Crash handling integration methods
    void connectCrashHandling();
    void trackUserAction(const QString& action);
    void onCrashDetected(const CrashInfo& crashInfo);
    void onCrashReportGenerated(const QString& reportPath);
    void showCrashRecoveryDialog(const CrashInfo& crashInfo);



    // UI Widgets
    ElaTab* m_tabWidget;
    WelcomeScreen* m_welcomeScreen;
    LoadingOverlay* m_loadingOverlay;
    OnboardingTour* m_onboardingTour;
    RibbonInterface* m_ribbonInterface;
    AnnotationToolbar* m_annotationToolbar;

    // Navigation keys
    QString m_homeKey;
    QString m_documentsKey;
    QString m_viewKey;
    QString m_toolsKey;
    QString m_settingsKey;
    ElaSpin* m_pageSpinBox;
    ElaLabel* m_pageCountLabel;
    ElaProgress* m_progressBar;
    ElaDock* m_thumbnailsDock;
    ElaListWidget* m_thumbnailsList;
    ElaDock* m_outlineDock;
    ElaTreeWidget* m_outlineTree;
    ElaDock* m_searchResultsDock;
    SearchResultsPanel* m_searchResultsPanel;
    QWidget* m_searchBar;
    ElaEdit* m_searchEdit;
    ElaButton* m_findNextButton;
    ElaButton* m_findPrevButton;
    ElaButton* m_closeSearchButton;
    ElaCheck* m_caseSensitiveCheckBox;
    ElaCheck* m_wholeWordCheckBox;

    // Enhanced search functionality
    SearchHistory* m_searchHistory;
    SearchWidget* m_enhancedSearchWidget;
    ElaCheck* m_highlightAllCheckBox;
    ElaLabel* m_searchResultsLabel;
    ElaCombo* m_zoomComboBox;
    ElaSliderWidget* m_zoomSlider;

    // Actions
    QAction* m_openAction;
    QAction* m_nextPageAction;
    QAction* m_prevPageAction;
    QAction* m_zoomInAction;
    QAction* m_zoomOutAction;
    QAction* m_fitToWindowAction;
    QAction* m_fitToWidthAction;
    QAction* m_firstPageAction;
    QAction* m_lastPageAction;
    QAction* m_zoom100Action;
    QAction* m_toggleThumbnailsAction;
    QAction* m_toggleOutlineAction;
    QAction* m_toggleSearchResultsAction;
    QAction* m_fullScreenAction;
    QAction* m_selectionZoomAction;
    QAction* m_magnifierAction;
    QAction* m_customZoomAction;
    QAction* m_newTabAction;
    QAction* m_closeTabAction;
    QAction* m_searchAction;
    QAction* m_addBookmarkAction;
    QAction* m_goToBookmarkAction;
    QMenu* m_recentFilesMenu;
    QAction* m_clearRecentAction;
    QAction* m_rotateClockwiseAction;
    QAction* m_rotateCounterClockwiseAction;
    QAction* m_rotate180Action;
    QAction* m_resetRotationAction;
    QAction* m_singlePageModeAction;
    QAction* m_continuousPageModeAction;
    QAction* m_facingPageModeAction;
    QAction* m_clearCacheAction;
    QAction* m_showMemoryUsageAction;
    QAction* m_exportCurrentPageAction;
    QAction* m_exportAllPagesAction;
    QAction* m_exportPageRangeAction;
    QAction* m_printDocumentAction;
    QAction* m_printCurrentPageAction;
    QAction* m_printPreviewAction;
    QAction* m_settingsAction;
    QAction* m_helpAction;
    QAction* m_documentInfoAction;

    // Annotation actions
    QAction* m_undoAction;
    QAction* m_redoAction;
    QAction* m_searchAnnotationsAction;
    QAction* m_copyAnnotationsAction;
    QAction* m_pasteAnnotationsAction;



    // Search state
    QString m_currentSearchTerm;
    QList<SearchResult> m_searchResults;
    int m_currentSearchIndex = -1;
    bool m_caseSensitiveSearch = false;
    bool m_wholeWordSearch = false;
    bool m_highlightAllResults = true;

    // Settings and bookmarks
    QSettings* m_settings;
    QStringList m_recentFiles;
    static const int MaxRecentFiles = 10;

    // Performance monitoring
    qint64 m_currentMemoryUsage = 0;
    int m_cachedPagesCount = 0;

    // UI optimization
    QTimer* m_uiUpdateTimer;
    bool m_uiUpdatePending = false;

    // Fullscreen mode state
    bool m_isFullScreen = false;
    QWidget* m_fullScreenWidget = nullptr;

    // Advanced zoom state
    bool m_selectionZoomMode = false;
    bool m_magnifierMode = false;
    QPoint m_selectionStart;
    QPoint m_selectionEnd;
    bool m_isSelecting = false;

    // Text selection state
    bool m_textSelectionMode = false;
};

#endif // MAINWINDOW_H
