#ifndef ANNOTATIONCLIPBOARD_H
#define ANNOTATIONCLIPBOARD_H

#include <QObject>
#include <QJsonArray>
#include <QJsonObject>
#include <QPointF>
#include <QList>
#include <memory>
#include <vector>
#include "Annotation.h"

class AnnotationManager;

class AnnotationClipboard : public QObject
{
    Q_OBJECT

public:
    static AnnotationClipboard* instance();
    
    // Copy operations
    void copyAnnotation(Annotation* annotation);
    void copyAnnotations(const QList<Annotation*>& annotations);
    void copySelectedAnnotations(AnnotationManager* manager);
    
    // Paste operations
    bool canPaste() const;
    std::vector<std::unique_ptr<Annotation>> pasteAnnotations(int targetPage, const QPointF& pastePosition = QPointF()) const;
    void pasteAnnotationsToManager(AnnotationManager* manager, int targetPage, const QPointF& pastePosition = QPointF());
    
    // Clipboard info
    int getAnnotationCount() const;
    QStringList getAnnotationTypes() const;
    bool isEmpty() const;
    void clear();
    
    // Export/Import
    QString exportToText() const;
    QString exportToJson() const;
    bool importFromJson(const QString& jsonString);

signals:
    void clipboardChanged();

private:
    explicit AnnotationClipboard(QObject* parent = nullptr);
    ~AnnotationClipboard() = default;
    
    // Prevent copying
    AnnotationClipboard(const AnnotationClipboard&) = delete;
    AnnotationClipboard& operator=(const AnnotationClipboard&) = delete;
    
    // Helper methods
    QPointF calculatePasteOffset(const QList<QJsonObject>& annotations, const QPointF& pastePosition) const;
    QJsonObject adjustAnnotationPosition(const QJsonObject& annotation, const QPointF& offset, int newPage) const;
    
    // Data
    QJsonArray m_clipboardData;
    QPointF m_originalCenterPoint;
    
    static AnnotationClipboard* s_instance;
};

// Convenience macros
#define ANNOTATION_CLIPBOARD AnnotationClipboard::instance()

#endif // ANNOTATIONCLIPBOARD_H
