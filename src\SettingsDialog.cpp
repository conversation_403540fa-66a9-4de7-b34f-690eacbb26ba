#include "SettingsDialog.h"
#include "ElaIntegration.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QPushButton>

SettingsDialog::SettingsDialog(QWidget *parent)
    : ElaContentDialog(parent)
{
    // Create main content widget
    QWidget* contentWidget = new QWidget(this);
    QVBoxLayout* mainLayout = new QVBoxLayout(contentWidget);

    // Create tab widget using Ela components
    m_tabWidget = new ElaTab(this);

    createGeneralTab();
    createPerformanceTab();
    createExportTab();
    createInterfaceTab();

    mainLayout->addWidget(m_tabWidget);

    // Add reset button to the layout
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    m_resetButton = new ElaPushButton(tr("Reset to Defaults"), this);
    connect(m_resetButton, &QPushButton::clicked, this, &SettingsDialog::resetToDefaults);
    buttonLayout->addWidget(m_resetButton);
    buttonLayout->addStretch();
    mainLayout->addLayout(buttonLayout);

    // Set the content widget
    setCentralWidget(contentWidget);

    // Configure ElaContentDialog buttons
    setLeftButtonText(tr("Cancel"));
    setRightButtonText(tr("OK"));

    // Connect dialog buttons
    connect(this, &ElaContentDialog::leftButtonClicked, this, &SettingsDialog::reject);
    connect(this, &ElaContentDialog::rightButtonClicked, this, &SettingsDialog::accept);

    resize(600, 500);
}

void SettingsDialog::createGeneralTab()
{
    QWidget* generalTab = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(generalTab);

    // Zoom settings group
    QGroupBox* zoomGroup = new QGroupBox(tr("Zoom Settings"), generalTab);
    QGridLayout* zoomLayout = new QGridLayout(zoomGroup);

    zoomLayout->addWidget(new ElaText(tr("Default zoom level:"), this), 0, 0);
    m_defaultZoomSpinBox = new ElaDoubleSpin();
    m_defaultZoomSpinBox->setRange(0.1, 5.0);
    m_defaultZoomSpinBox->setSingleStep(0.1);
    m_defaultZoomSpinBox->setSuffix("%");
    m_defaultZoomSpinBox->setValue(1.0);
    zoomLayout->addWidget(m_defaultZoomSpinBox, 0, 1);

    m_autoFitOnOpenCheckBox = new ElaCheck(tr("Auto-fit pages when opening documents"));
    zoomLayout->addWidget(m_autoFitOnOpenCheckBox, 1, 0, 1, 2);

    layout->addWidget(zoomGroup);

    // Window settings group
    QGroupBox* windowGroup = new QGroupBox(tr("Window Settings"), generalTab);
    QVBoxLayout* windowLayout = new QVBoxLayout(windowGroup);

    m_rememberWindowStateCheckBox = new ElaCheck(tr("Remember window size and position"));
    m_rememberWindowStateCheckBox->setChecked(true);
    windowLayout->addWidget(m_rememberWindowStateCheckBox);

    layout->addWidget(windowGroup);
    layout->addStretch();

    m_tabWidget->addTab(generalTab, tr("General"));
}

void SettingsDialog::createPerformanceTab()
{
    QWidget* performanceTab = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(performanceTab);
    
    // Cache settings group
    QGroupBox* cacheGroup = new QGroupBox(tr("Cache Settings"), performanceTab);
    QGridLayout* cacheLayout = new QGridLayout(cacheGroup);
    
    cacheLayout->addWidget(new ElaText(tr("Cache size:"), this), 0, 0);
    m_cacheSizeSpinBox = new ElaSpin();
    m_cacheSizeSpinBox->setRange(50, 2000);
    m_cacheSizeSpinBox->setSingleStep(50);
    m_cacheSizeSpinBox->setSuffix(" MB");
    m_cacheSizeSpinBox->setValue(450);
    connect(m_cacheSizeSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &SettingsDialog::onCacheSizeChanged);
    cacheLayout->addWidget(m_cacheSizeSpinBox, 0, 1);

    m_cacheSizeLabel = new ElaLabel();
    updateCacheSizeLabel(450);
    cacheLayout->addWidget(m_cacheSizeLabel, 1, 0, 1, 2);

    cacheLayout->addWidget(new ElaText(tr("Preload range:"), this), 2, 0);
    m_preloadRangeSpinBox = new ElaSpin();
    m_preloadRangeSpinBox->setRange(0, 10);
    m_preloadRangeSpinBox->setSuffix(" pages");
    m_preloadRangeSpinBox->setValue(2);
    cacheLayout->addWidget(m_preloadRangeSpinBox, 2, 1);

    layout->addWidget(cacheGroup);

    // Monitoring group
    QGroupBox* monitorGroup = new QGroupBox(tr("Performance Monitoring"), performanceTab);
    QVBoxLayout* monitorLayout = new QVBoxLayout(monitorGroup);

    m_showMemoryUsageCheckBox = new ElaCheck(tr("Show memory usage in status bar"));
    monitorLayout->addWidget(m_showMemoryUsageCheckBox);
    
    layout->addWidget(monitorGroup);
    layout->addStretch();
    
    m_tabWidget->addTab(performanceTab, tr("Performance"));
}

void SettingsDialog::createExportTab()
{
    QWidget* exportTab = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(exportTab);
    
    // Export settings group
    QGroupBox* exportGroup = new QGroupBox(tr("Export Settings"), exportTab);
    QGridLayout* exportLayout = new QGridLayout(exportGroup);
    
    exportLayout->addWidget(new ElaText(tr("Default format:"), this), 0, 0);
    m_exportFormatComboBox = new ElaCombo();
    m_exportFormatComboBox->addItems({"PNG", "JPEG", "PDF"});
    exportLayout->addWidget(m_exportFormatComboBox, 0, 1);

    exportLayout->addWidget(new ElaText(tr("Image quality:"), this), 1, 0);
    m_exportQualitySlider = new ElaSliderWidget(Qt::Horizontal);
    m_exportQualitySlider->setRange(1, 100);
    m_exportQualitySlider->setValue(90);
    connect(m_exportQualitySlider, &QSlider::valueChanged, [this](int value) {
        m_exportQualityLabel->setText(QString("%1%").arg(value));
    });
    exportLayout->addWidget(m_exportQualitySlider, 1, 1);

    m_exportQualityLabel = new ElaLabel("90%");
    exportLayout->addWidget(m_exportQualityLabel, 1, 2);
    
    layout->addWidget(exportGroup);
    layout->addStretch();
    
    m_tabWidget->addTab(exportTab, tr("Export"));
}

void SettingsDialog::createInterfaceTab()
{
    QWidget* interfaceTab = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(interfaceTab);

    // Theme settings group
    QGroupBox* themeGroup = new QGroupBox(tr("Theme Settings"), interfaceTab);
    QGridLayout* themeLayout = new QGridLayout(themeGroup);

    themeLayout->addWidget(new ElaText(tr("Theme mode:"), this), 0, 0);
    m_themeModeComboBox = new ElaCombo();
    m_themeModeComboBox->addItem(tr("Light"), static_cast<int>(ElaThemeType::Light));
    m_themeModeComboBox->addItem(tr("Dark"), static_cast<int>(ElaThemeType::Dark));
    m_themeModeComboBox->setCurrentIndex(0); // Default to Light
    themeLayout->addWidget(m_themeModeComboBox, 0, 1);

    layout->addWidget(themeGroup);

    // Interface settings group
    QGroupBox* interfaceGroup = new QGroupBox(tr("Interface Settings"), interfaceTab);
    QVBoxLayout* interfaceLayout = new QVBoxLayout(interfaceGroup);

    m_showThumbnailsCheckBox = new ElaCheck(tr("Show thumbnails panel by default"));
    m_showThumbnailsCheckBox->setChecked(true);
    interfaceLayout->addWidget(m_showThumbnailsCheckBox);

    layout->addWidget(interfaceGroup);
    layout->addStretch();

    m_tabWidget->addTab(interfaceTab, tr("Interface"));

    // Add advanced theme customization button
    QWidget* advancedThemeWidget = new QWidget();
    QHBoxLayout* advancedThemeLayout = new QHBoxLayout(advancedThemeWidget);

    ElaPushButton* customizeThemeButton = new ElaPushButton(tr("Advanced Theme Customization..."));
    customizeThemeButton->setToolTip(tr("Open advanced theme customization dialog"));

    connect(customizeThemeButton, &ElaPushButton::clicked, this, []() {
        // This would open the ThemeCustomizer dialog
        // ThemeCustomizer customizer(this);
        // customizer.exec();
    });

    advancedThemeLayout->addWidget(customizeThemeButton);
    advancedThemeLayout->addStretch();

    layout->addWidget(advancedThemeWidget);
}

void SettingsDialog::resetToDefaults()
{
    m_defaultZoomSpinBox->setValue(1.0);
    m_autoFitOnOpenCheckBox->setChecked(false);
    m_rememberWindowStateCheckBox->setChecked(true);
    m_cacheSizeSpinBox->setValue(450);
    m_preloadRangeSpinBox->setValue(2);
    m_showMemoryUsageCheckBox->setChecked(false);
    m_exportFormatComboBox->setCurrentText("PNG");
    m_exportQualitySlider->setValue(90);
    m_showThumbnailsCheckBox->setChecked(true);
    m_themeModeComboBox->setCurrentIndex(0); // Default to Light theme
}

void SettingsDialog::onCacheSizeChanged(int value)
{
    updateCacheSizeLabel(value);
}

void SettingsDialog::updateCacheSizeLabel(int sizeMB)
{
    const int pages = sizeMB / 5; // Rough estimate: 5MB per page
    m_cacheSizeLabel->setText(tr("Approximately %1 pages at normal zoom").arg(pages));
}

// Getters
double SettingsDialog::getDefaultZoom() const { return m_defaultZoomSpinBox->value(); }
int SettingsDialog::getCacheSize() const { return m_cacheSizeSpinBox->value(); }
int SettingsDialog::getPreloadRange() const { return m_preloadRangeSpinBox->value(); }
bool SettingsDialog::getShowMemoryUsage() const { return m_showMemoryUsageCheckBox->isChecked(); }
bool SettingsDialog::getShowThumbnails() const { return m_showThumbnailsCheckBox->isChecked(); }
QString SettingsDialog::getExportFormat() const { return m_exportFormatComboBox->currentText(); }
int SettingsDialog::getExportQuality() const { return m_exportQualitySlider->value(); }
bool SettingsDialog::getRememberWindowState() const { return m_rememberWindowStateCheckBox->isChecked(); }
bool SettingsDialog::getAutoFitOnOpen() const { return m_autoFitOnOpenCheckBox->isChecked(); }
int SettingsDialog::getThemeMode() const { return m_themeModeComboBox->currentData().toInt(); }

// Setters
void SettingsDialog::setDefaultZoom(double zoom) { m_defaultZoomSpinBox->setValue(zoom); }
void SettingsDialog::setCacheSize(int sizeMB) { m_cacheSizeSpinBox->setValue(sizeMB); }
void SettingsDialog::setPreloadRange(int range) { m_preloadRangeSpinBox->setValue(range); }
void SettingsDialog::setShowMemoryUsage(bool show) { m_showMemoryUsageCheckBox->setChecked(show); }
void SettingsDialog::setShowThumbnails(bool show) { m_showThumbnailsCheckBox->setChecked(show); }
void SettingsDialog::setExportFormat(const QString& format) { m_exportFormatComboBox->setCurrentText(format); }
void SettingsDialog::setExportQuality(int quality) { m_exportQualitySlider->setValue(quality); }
void SettingsDialog::setRememberWindowState(bool remember) { m_rememberWindowStateCheckBox->setChecked(remember); }
void SettingsDialog::setAutoFitOnOpen(bool autoFit) { m_autoFitOnOpenCheckBox->setChecked(autoFit); }
void SettingsDialog::setThemeMode(int themeMode) {
    for (int i = 0; i < m_themeModeComboBox->count(); ++i) {
        if (m_themeModeComboBox->itemData(i).toInt() == themeMode) {
            m_themeModeComboBox->setCurrentIndex(i);
            break;
        }
    }
}
