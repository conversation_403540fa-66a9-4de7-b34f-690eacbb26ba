// ElaIntegration.h
#ifndef ELA_INTEGRATION_H
#define ELA_INTEGRATION_H

#ifdef ELA_WIDGETS_ENABLED
// ElaWidgetTools includes
#include "Def.h"
#include "ElaApplication.h"
#include "ElaWindow.h"
#include "ElaWidget.h"
#include "ElaPushButton.h"
#include "ElaLineEdit.h"
#include "ElaComboBox.h"
#include "ElaCheckBox.h"
#include "ElaRadioButton.h"
#include "ElaSpinBox.h"
#include "ElaDoubleSpinBox.h"
#include "ElaSlider.h"
#include "ElaProgressBar.h"
#include "ElaTabWidget.h"
#include "ElaTabBar.h"
#include "ElaListView.h"
#include "ElaTreeView.h"
#include "ElaTableView.h"
#include "ElaScrollArea.h"
#include "ElaScrollBar.h"
#include "ElaMenu.h"
#include "ElaMenuBar.h"
#include "ElaToolBar.h"
#include "ElaToolButton.h"
#include "ElaIconButton.h"
#include "ElaIcon.h"
#include "ElaStatusBar.h"
#include "ElaDockWidget.h"
#include "ElaMessageBar.h"
#include "ElaContentDialog.h"
#include "ElaColorDialog.h"
#include "ElaCalendarPicker.h"
#include "ElaMultiSelectComboBox.h"
#include "ElaToggleButton.h"
#include "ElaToggleSwitch.h"
#include "ElaSuggestBox.h"
#include "ElaPlainTextEdit.h"
#include "ElaText.h"
#include "ElaTheme.h"
#include <QListWidget>
#include <QTreeWidget>

// Widget type aliases for easy replacement
using ElaMainWindow = ElaWindow;
using ElaButton = ElaPushButton;
using ElaEdit = ElaLineEdit;
using ElaCombo = ElaComboBox;
using ElaCheck = ElaCheckBox;
using ElaRadio = ElaRadioButton;
using ElaSpin = ElaSpinBox;
using ElaDoubleSpin = ElaDoubleSpinBox;
using ElaSliderWidget = ElaSlider;
using ElaProgress = ElaProgressBar;
using ElaTab = ElaTabWidget;
// For complex widgets, we need to use Qt widgets with Ela styling
using ElaListWidget = QListWidget;
using ElaTreeWidget = QTreeWidget;
using ElaTable = ElaTableView;
using ElaScroll = ElaScrollArea;
using ElaScrollBarWidget = ElaScrollBar;
using ElaMenuWidget = ElaMenu;
using ElaMenuBarWidget = ElaMenuBar;
using ElaToolBarWidget = ElaToolBar;
using ElaToolButtonWidget = ElaToolButton;
using ElaIconButtonWidget = ElaIconButton;
using ElaStatusBarWidget = ElaStatusBar;
using ElaDock = ElaDockWidget;
using ElaMessage = ElaMessageBar;
using ElaDialog = ElaContentDialog;
using ElaColorPicker = ElaColorDialog;
using ElaDatePicker = ElaCalendarPicker;
using ElaMultiCombo = ElaMultiSelectComboBox;
using ElaSwitch = ElaToggleButton;
using ElaToggle = ElaToggleSwitch;
using ElaSuggest = ElaSuggestBox;
using ElaTextEdit = ElaPlainTextEdit;
using ElaLabel = ElaText;

#else
// Fallback to standard Qt widgets when ElaWidgetTools is not available
#include <QMainWindow>
#include <QWidget>
#include <QPushButton>
#include <QLineEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QRadioButton>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QSlider>
#include <QProgressBar>
#include <QTabWidget>
#include <QTabBar>
#include <QListView>
#include <QListWidget>
#include <QTreeView>
#include <QTreeWidget>
#include <QTableView>
#include <QScrollArea>
#include <QScrollBar>
#include <QMenu>
#include <QMenuBar>
#include <QToolBar>
#include <QToolButton>
#include <QStatusBar>
#include <QDockWidget>
#include <QMessageBox>
#include <QDialog>
#include <QColorDialog>
#include <QDateEdit>
#include <QPlainTextEdit>
#include <QLabel>

// Fallback type definitions for ElaWidgetTools types (only when ElaWidgetTools is not available)
#ifndef ELA_WIDGETS_ENABLED
namespace ElaNavigationType {
    enum NavigationNodeType {
        PageNode = 0x0000,
        FooterNode = 0x0001,
    };
    enum NavigationDisplayMode {
        Auto = 0x0000,
        Minimal = 0x0001,
        Compact = 0x0002,
        Maximal = 0x0003,
    };
}

namespace ElaIconType {
    enum IconName {
        None = 0x0,
        GearComplex = 0xe000, // Placeholder value
    };
}

namespace ElaAppBarType {
    enum ButtonType {
        MinimizeButtonHint = 0x0001,
        MaximizeButtonHint = 0x0002,
        CloseButtonHint = 0x0004,
    };
    typedef int ButtonFlags;
}
#endif // ELA_WIDGETS_ENABLED

// Fallback wrapper class for ElaWindow functionality (only when ElaWidgetTools is not available)
#ifndef ELA_WIDGETS_ENABLED
class ElaWindowFallback : public QMainWindow {
    Q_OBJECT
public:
    explicit ElaWindowFallback(QWidget* parent = nullptr) : QMainWindow(parent) {}

    // Fallback implementations for ElaWindow methods
    void addCentralWidget(QWidget* widget) { setCentralWidget(widget); }
    void setUserInfoCardTitle(const QString&) { /* No-op */ }
    void setUserInfoCardSubTitle(const QString&) { /* No-op */ }
    void setIsNavigationBarEnable(bool) { /* No-op */ }
    void setNavigationBarDisplayMode(ElaNavigationType::NavigationDisplayMode) { /* No-op */ }
    void setWindowButtonFlag(ElaAppBarType::ButtonType, bool = true) { /* No-op */ }
    void addPageNode(const QString&, QWidget*, ElaIconType::IconName = ElaIconType::None) { /* No-op */ }
    void addExpanderNode(const QString&, QString&, ElaIconType::IconName = ElaIconType::None) { /* No-op */ }
    void addFooterNode(const QString&, QString&, int = 0, ElaIconType::IconName = ElaIconType::None) { /* No-op */ }
    void navigation(const QString&) { /* No-op */ }
    void setCustomWidgetMaximumWidth(int) { /* No-op */ }

signals:
    void navigationNodeClicked(ElaNavigationType::NavigationNodeType, QString);
};

// Widget type aliases using standard Qt widgets
using ElaMainWindow = ElaWindowFallback;
using ElaWindow = ElaWindowFallback; // Fallback to ElaWindowFallback
using ElaButton = QPushButton;
using ElaEdit = QLineEdit;
using ElaCombo = QComboBox;
#else
// When ElaWidgetTools is available, use the actual ElaWidgetTools classes
// Include the main ElaWidgetTools headers
#include "ElaWindow.h"
#include "ElaProgressBar.h"
// Add other ElaWidgetTools includes as needed

// Type aliases for ElaWidgetTools classes
using ElaMainWindow = ElaWindow;
#endif // ELA_WIDGETS_ENABLED

// Common type aliases that work with both ElaWidgetTools and fallback Qt widgets
using ElaCheck = QCheckBox;
using ElaRadio = QRadioButton;
using ElaSpin = QSpinBox;
using ElaDoubleSpin = QDoubleSpinBox;
using ElaSliderWidget = QSlider;
#ifndef ELA_WIDGETS_ENABLED
using ElaProgress = QProgressBar;
#else
using ElaProgress = ElaProgressBar;
#endif
using ElaTab = QTabWidget;
// For complex widgets, we need to use Qt widgets with Ela styling
using ElaListWidget = QListWidget;
using ElaTreeWidget = QTreeWidget;
using ElaTable = QTableView;
using ElaScroll = QScrollArea;
using ElaScrollBarWidget = QScrollBar;
using ElaMenuWidget = QMenu;
using ElaMenuBarWidget = QMenuBar;
using ElaToolBarWidget = QToolBar;
using ElaToolButtonWidget = QToolButton;
using ElaStatusBarWidget = QStatusBar;
using ElaDock = QDockWidget;
using ElaDialog = QDialog;
using ElaColorPicker = QColorDialog;
using ElaDatePicker = QDateEdit;
using ElaTextEdit = QPlainTextEdit;
using ElaLabel = QLabel;

#endif // ELA_WIDGETS_ENABLED

// Utility functions for ElaWidgetTools integration
namespace ElaIntegration {
    
    // Initialize ElaWidgetTools application if available
    void initializeElaApplication();
    
    // Apply ElaWidgetTools theme if available
    void applyElaTheme();
    
    // Check if ElaWidgetTools is available
    bool isElaWidgetsAvailable();
    
    // Create styled widgets with fallback to Qt widgets
    template<typename ElaWidget, typename QtWidget>
    QWidget* createWidget(QWidget* parent = nullptr) {
#ifdef ELA_WIDGETS_ENABLED
        return new ElaWidget(parent);
#else
        return new QtWidget(parent);
#endif
    }
}

#endif // ELA_INTEGRATION_H
