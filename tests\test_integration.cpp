#include <QtTest/QtTest>
#include <QSignalSpy>
#include <QApplication>
#include <QTemporaryFile>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>

#include "DocumentTab.h"
#include "AnnotationManager.h"
#include "AnnotationClipboard.h"
#include "PdfController.h"
#include "Annotation.h"

class TestIntegration : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Integration tests
    void testDocumentTabWithAnnotations();
    void testPdfControllerWithAnnotationManager();
    void testAnnotationPersistence();
    void testAnnotationClipboard();
    void testSearchWithAnnotations();
    void testPageRenderingWithAnnotations();
    void testMultipleDocumentTabs();
    void testAnnotationSynchronization();
    void testMemoryManagement();
    void testConcurrentOperations();

private:
    DocumentTab* m_tab1;
    DocumentTab* m_tab2;
    QString m_testPdfPath;
    
    void waitForSignal(QSignalSpy* spy, int timeout = 5000);
    void createTestAnnotations(AnnotationManager* manager);
    void verifyAnnotationRendering(DocumentTab* tab);
};

void TestIntegration::initTestCase()
{
    // Setup test environment
    if (!QApplication::instance()) {
        int argc = 0;
        char* argv[] = {nullptr};
        new QApplication(argc, argv);
    }
    
    // Try to find a test PDF file
    QStringList possiblePaths = {
        "test.pdf",
        QDir::currentPath() + "/test.pdf",
        "data/test.pdf"
    };
    
    for (const QString& path : possiblePaths) {
        if (QFile::exists(path)) {
            m_testPdfPath = path;
            break;
        }
    }
}

void TestIntegration::cleanupTestCase()
{
    // Cleanup
}

void TestIntegration::init()
{
    m_tab1 = new DocumentTab();
    m_tab2 = new DocumentTab();
}

void TestIntegration::cleanup()
{
    delete m_tab1;
    delete m_tab2;
}

void TestIntegration::testDocumentTabWithAnnotations()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    // Load document
    QSignalSpy documentLoadedSpy(m_tab1, &DocumentTab::documentLoaded);
    m_tab1->loadDocument(m_testPdfPath);
    waitForSignal(&documentLoadedSpy);
    
    QVERIFY(m_tab1->isDocumentLoaded());
    
    // Get annotation manager
    AnnotationManager* manager = m_tab1->getAnnotationManager();
    QVERIFY(manager != nullptr);
    
    // Create test annotations
    createTestAnnotations(manager);
    
    // Verify annotations were added
    QCOMPARE(manager->getAnnotationCount(), 3);
    QCOMPARE(manager->getAnnotationsForPage(0).size(), 2);
    QCOMPARE(manager->getAnnotationsForPage(1).size(), 1);
    
    // Test annotation rendering integration
    verifyAnnotationRendering(m_tab1);
}

void TestIntegration::testPdfControllerWithAnnotationManager()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    // Setup
    PdfController controller;
    AnnotationManager manager;
    controller.setAnnotationManager(&manager);
    
    // Load document
    QSignalSpy documentLoadedSpy(&controller, &PdfController::documentLoaded);
    controller.loadDocument(m_testPdfPath);
    waitForSignal(&documentLoadedSpy);
    
    // Add annotations
    auto highlight = std::make_unique<HighlightAnnotation>();
    highlight->setPageNumber(0);
    highlight->setColor(QColor(255, 255, 0, 128));
    highlight->addQuad(QRectF(10, 20, 100, 15));
    manager.addAnnotation(std::move(highlight));
    
    // Request page with annotations
    QSignalSpy pageWithAnnotationsSpy(&controller, &PdfController::pageWithAnnotationsReady);
    controller.requestPageWithAnnotations(0, 1.0);
    waitForSignal(&pageWithAnnotationsSpy);
    
    QCOMPARE(pageWithAnnotationsSpy.count(), 1);
    QList<QVariant> arguments = pageWithAnnotationsSpy.takeFirst();
    int pageNum = arguments.at(0).toInt();
    QPixmap pixmap = arguments.at(1).value<QPixmap>();
    
    QCOMPARE(pageNum, 0);
    QVERIFY(!pixmap.isNull());
    
    // Verify annotation manager is properly integrated
    QCOMPARE(controller.getAnnotationManager(), &manager);
}

void TestIntegration::testAnnotationPersistence()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    // Load document in first tab
    QSignalSpy documentLoadedSpy(m_tab1, &DocumentTab::documentLoaded);
    m_tab1->loadDocument(m_testPdfPath);
    waitForSignal(&documentLoadedSpy);
    
    AnnotationManager* manager1 = m_tab1->getAnnotationManager();
    createTestAnnotations(manager1);
    
    // Save annotations to temporary file
    QTemporaryFile tempFile;
    QVERIFY(tempFile.open());
    QString filePath = tempFile.fileName();
    tempFile.close();
    
    bool saveResult = manager1->saveAnnotations(filePath);
    QVERIFY(saveResult);
    
    // Load document in second tab
    QSignalSpy documentLoadedSpy2(m_tab2, &DocumentTab::documentLoaded);
    m_tab2->loadDocument(m_testPdfPath);
    waitForSignal(&documentLoadedSpy2);
    
    AnnotationManager* manager2 = m_tab2->getAnnotationManager();
    
    // Load annotations from file
    bool loadResult = manager2->loadAnnotations(filePath);
    QVERIFY(loadResult);
    
    // Verify annotations were loaded correctly
    QCOMPARE(manager2->getAnnotationCount(), manager1->getAnnotationCount());
    QCOMPARE(manager2->getAnnotationsForPage(0).size(), manager1->getAnnotationsForPage(0).size());
    QCOMPARE(manager2->getAnnotationsForPage(1).size(), manager1->getAnnotationsForPage(1).size());
    
    // Verify annotation properties
    QList<Annotation*> originalAnnotations = manager1->getAnnotations();
    QList<Annotation*> loadedAnnotations = manager2->getAnnotations();
    
    QCOMPARE(loadedAnnotations.size(), originalAnnotations.size());
    
    for (int i = 0; i < originalAnnotations.size(); ++i) {
        Annotation* original = originalAnnotations[i];
        Annotation* loaded = loadedAnnotations[i];
        
        QCOMPARE(loaded->getType(), original->getType());
        QCOMPARE(loaded->getPageNumber(), original->getPageNumber());
        QCOMPARE(loaded->getColor(), original->getColor());
        QCOMPARE(loaded->getContent(), original->getContent());
    }
}

void TestIntegration::testAnnotationClipboard()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    // Load document
    QSignalSpy documentLoadedSpy(m_tab1, &DocumentTab::documentLoaded);
    m_tab1->loadDocument(m_testPdfPath);
    waitForSignal(&documentLoadedSpy);
    
    AnnotationManager* manager = m_tab1->getAnnotationManager();
    createTestAnnotations(manager);
    
    // Test clipboard operations using singleton
    AnnotationClipboard* clipboard = AnnotationClipboard::instance();
    
    // Copy annotations
    QList<Annotation*> annotationsToCopy = manager->getAnnotationsForPage(0);
    clipboard->copyAnnotations(annotationsToCopy);

    QVERIFY(!clipboard->isEmpty());
    QCOMPARE(clipboard->getAnnotationCount(), annotationsToCopy.size());
    
    // Paste annotations to different page
    auto pastedAnnotations = clipboard->pasteAnnotations(2, QPointF(50, 50));
    
    QCOMPARE(pastedAnnotations.size(), annotationsToCopy.size());
    
    // Add pasted annotations to manager
    for (auto& annotation : pastedAnnotations) {
        QCOMPARE(annotation->getPageNumber(), 2);
        manager->addAnnotation(std::move(annotation));
    }
    
    // Verify annotations were pasted correctly
    QList<Annotation*> pastedList = manager->getAnnotationsForPage(2);
    QCOMPARE(pastedList.size(), annotationsToCopy.size());
    
    // Clear clipboard
    clipboard->clear();
    QVERIFY(clipboard->isEmpty());
    QCOMPARE(clipboard->getAnnotationCount(), 0);
}

void TestIntegration::testSearchWithAnnotations()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    // Load document
    QSignalSpy documentLoadedSpy(m_tab1, &DocumentTab::documentLoaded);
    m_tab1->loadDocument(m_testPdfPath);
    waitForSignal(&documentLoadedSpy);
    
    PdfController* controller = m_tab1->getPdfController();
    AnnotationManager* manager = m_tab1->getAnnotationManager();
    
    // Add text annotation with searchable content
    auto textAnnotation = std::make_unique<TextAnnotation>();
    textAnnotation->setPageNumber(0);
    textAnnotation->setText("Searchable annotation text");
    textAnnotation->setRect(QRectF(100, 200, 200, 50));
    manager->addAnnotation(std::move(textAnnotation));
    
    // Perform search
    QSignalSpy searchCompletedSpy(controller, &PdfController::searchCompleted);
    controller->performSearch("annotation", false);
    waitForSignal(&searchCompletedSpy);
    
    QCOMPARE(searchCompletedSpy.count(), 1);
    QList<QVariant> arguments = searchCompletedSpy.takeFirst();
    QList<SearchResult> results = arguments.at(0).value<QList<SearchResult>>();
    
    // Verify search results (may include results from PDF text and annotations)
    QVERIFY(results.size() >= 0);
    
    // Set search results in tab
    m_tab1->setSearchResults(results);
    QCOMPARE(m_tab1->getSearchResults().size(), results.size());
}

void TestIntegration::testPageRenderingWithAnnotations()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    // Load document
    QSignalSpy documentLoadedSpy(m_tab1, &DocumentTab::documentLoaded);
    m_tab1->loadDocument(m_testPdfPath);
    waitForSignal(&documentLoadedSpy);
    
    PdfController* controller = m_tab1->getPdfController();
    AnnotationManager* manager = m_tab1->getAnnotationManager();
    
    // Add visible annotations
    auto highlight = std::make_unique<HighlightAnnotation>();
    highlight->setPageNumber(0);
    highlight->setColor(QColor(255, 255, 0, 128));
    highlight->addQuad(QRectF(50, 100, 200, 20));
    manager->addAnnotation(std::move(highlight));
    
    auto shape = std::make_unique<ShapeAnnotation>(ShapeAnnotation::ShapeType::Rectangle);
    shape->setPageNumber(0);
    shape->setColor(QColor(255, 0, 0, 128));
    shape->setRect(QRectF(100, 200, 150, 100));
    manager->addAnnotation(std::move(shape));
    
    // Request page without annotations
    QSignalSpy pageReadySpy(controller, &PdfController::pageReady);
    controller->requestPage(0, 1.0);
    waitForSignal(&pageReadySpy);
    
    QCOMPARE(pageReadySpy.count(), 1);
    QPixmap pageWithoutAnnotations = pageReadySpy.takeFirst().at(1).value<QPixmap>();
    
    // Request page with annotations
    QSignalSpy pageWithAnnotationsSpy(controller, &PdfController::pageWithAnnotationsReady);
    controller->requestPageWithAnnotations(0, 1.0);
    waitForSignal(&pageWithAnnotationsSpy);
    
    QCOMPARE(pageWithAnnotationsSpy.count(), 1);
    QPixmap pageWithAnnotations = pageWithAnnotationsSpy.takeFirst().at(1).value<QPixmap>();
    
    // Verify both pixmaps are valid
    QVERIFY(!pageWithoutAnnotations.isNull());
    QVERIFY(!pageWithAnnotations.isNull());
    
    // Pixmaps should have same dimensions
    QCOMPARE(pageWithAnnotations.size(), pageWithoutAnnotations.size());
    
    // Pixmaps should be different (annotations should be visible)
    // Note: This is a basic check - in practice, you might want more sophisticated comparison
    QVERIFY(pageWithAnnotations.toImage() != pageWithoutAnnotations.toImage());
}

void TestIntegration::testMultipleDocumentTabs()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    // Load same document in both tabs
    QSignalSpy documentLoadedSpy1(m_tab1, &DocumentTab::documentLoaded);
    QSignalSpy documentLoadedSpy2(m_tab2, &DocumentTab::documentLoaded);
    
    m_tab1->loadDocument(m_testPdfPath);
    m_tab2->loadDocument(m_testPdfPath);
    
    waitForSignal(&documentLoadedSpy1);
    waitForSignal(&documentLoadedSpy2);
    
    QVERIFY(m_tab1->isDocumentLoaded());
    QVERIFY(m_tab2->isDocumentLoaded());
    
    // Verify tabs are independent
    QVERIFY(m_tab1->getAnnotationManager() != m_tab2->getAnnotationManager());
    QVERIFY(m_tab1->getPdfController() != m_tab2->getPdfController());
    
    // Add annotations to first tab
    AnnotationManager* manager1 = m_tab1->getAnnotationManager();
    createTestAnnotations(manager1);
    
    // Verify second tab is not affected
    AnnotationManager* manager2 = m_tab2->getAnnotationManager();
    QCOMPARE(manager2->getAnnotationCount(), 0);
    
    // Test independent navigation
    if (m_tab1->getPageCount() > 1) {
        m_tab1->setCurrentPage(1);
        m_tab2->setCurrentPage(0);
        
        QCOMPARE(m_tab1->getCurrentPage(), 1);
        QCOMPARE(m_tab2->getCurrentPage(), 0);
    }
    
    // Test independent zoom
    m_tab1->setZoomFactor(1.5);
    m_tab2->setZoomFactor(0.8);
    
    QCOMPARE(m_tab1->getZoomFactor(), 1.5);
    QCOMPARE(m_tab2->getZoomFactor(), 0.8);
}

void TestIntegration::testAnnotationSynchronization()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    // Load document
    QSignalSpy documentLoadedSpy(m_tab1, &DocumentTab::documentLoaded);
    m_tab1->loadDocument(m_testPdfPath);
    waitForSignal(&documentLoadedSpy);
    
    AnnotationManager* manager = m_tab1->getAnnotationManager();
    PdfController* controller = m_tab1->getPdfController();
    
    // Test annotation signals
    QSignalSpy annotationAddedSpy(manager, &AnnotationManager::annotationAdded);
    QSignalSpy annotationRemovedSpy(manager, &AnnotationManager::annotationRemoved);
    QSignalSpy annotationSelectedSpy(manager, &AnnotationManager::annotationSelected);
    
    // Add annotation
    auto highlight = std::make_unique<HighlightAnnotation>();
    highlight->setPageNumber(0);
    QString annotationId = highlight->getId();
    Annotation* annotationPtr = highlight.get();
    
    manager->addAnnotation(std::move(highlight));
    
    QCOMPARE(annotationAddedSpy.count(), 1);
    QCOMPARE(annotationAddedSpy.takeFirst().at(0).value<Annotation*>(), annotationPtr);
    
    // Select annotation
    manager->selectAnnotation(annotationPtr);
    
    QCOMPARE(annotationSelectedSpy.count(), 1);
    QCOMPARE(annotationSelectedSpy.takeFirst().at(0).value<Annotation*>(), annotationPtr);
    
    // Remove annotation
    manager->removeAnnotation(annotationId);
    
    QCOMPARE(annotationRemovedSpy.count(), 1);
    QCOMPARE(annotationRemovedSpy.takeFirst().at(0).toString(), annotationId);
}

void TestIntegration::testMemoryManagement()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    // Load document
    QSignalSpy documentLoadedSpy(m_tab1, &DocumentTab::documentLoaded);
    m_tab1->loadDocument(m_testPdfPath);
    waitForSignal(&documentLoadedSpy);
    
    PdfController* controller = m_tab1->getPdfController();
    AnnotationManager* manager = m_tab1->getAnnotationManager();
    
    // Monitor memory usage
    qint64 initialMemory = controller->getCacheMemoryUsage();
    
    // Create many annotations
    for (int i = 0; i < 50; ++i) {
        auto highlight = std::make_unique<HighlightAnnotation>();
        highlight->setPageNumber(i % m_tab1->getPageCount());
        highlight->addQuad(QRectF(10 + i, 20 + i, 100, 15));
        manager->addAnnotation(std::move(highlight));
    }
    
    // Request multiple pages
    for (int i = 0; i < qMin(5, m_tab1->getPageCount()); ++i) {
        controller->requestPage(i, 1.0);
        controller->requestPageWithAnnotations(i, 1.0);
    }
    
    // Wait for operations to complete
    QTest::qWait(2000);
    
    qint64 finalMemory = controller->getCacheMemoryUsage();
    
    // Memory usage should be reasonable
    QVERIFY(finalMemory < 200 * 1024 * 1024); // Less than 200MB
    
    // Clear cache and annotations
    controller->clearCache();
    manager->clearAnnotations();
    
    // Memory should be reduced
    qint64 clearedMemory = controller->getCacheMemoryUsage();
    QVERIFY(clearedMemory <= finalMemory);
    QCOMPARE(manager->getAnnotationCount(), 0);
}

void TestIntegration::testConcurrentOperations()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    // Load document
    QSignalSpy documentLoadedSpy(m_tab1, &DocumentTab::documentLoaded);
    m_tab1->loadDocument(m_testPdfPath);
    waitForSignal(&documentLoadedSpy);
    
    PdfController* controller = m_tab1->getPdfController();
    AnnotationManager* manager = m_tab1->getAnnotationManager();
    
    // Perform concurrent operations
    QSignalSpy pageReadySpy(controller, &PdfController::pageReady);
    QSignalSpy annotationAddedSpy(manager, &AnnotationManager::annotationAdded);
    
    // Request multiple pages concurrently
    int pageCount = qMin(3, m_tab1->getPageCount());
    for (int i = 0; i < pageCount; ++i) {
        controller->requestPage(i, 1.0);
        controller->requestPreview(i);
        
        // Add annotation while page is rendering
        auto highlight = std::make_unique<HighlightAnnotation>();
        highlight->setPageNumber(i);
        highlight->addQuad(QRectF(10 + i * 10, 20, 100, 15));
        manager->addAnnotation(std::move(highlight));
    }
    
    // Wait for operations to complete
    QTest::qWait(3000);
    
    // Verify operations completed successfully
    QVERIFY(pageReadySpy.count() >= pageCount);
    QCOMPARE(annotationAddedSpy.count(), pageCount);
    QCOMPARE(manager->getAnnotationCount(), pageCount);
}

void TestIntegration::waitForSignal(QSignalSpy* spy, int timeout)
{
    if (spy->count() == 0) {
        spy->wait(timeout);
    }
}

void TestIntegration::createTestAnnotations(AnnotationManager* manager)
{
    // Create highlight annotation
    auto highlight = std::make_unique<HighlightAnnotation>();
    highlight->setPageNumber(0);
    highlight->setColor(QColor(255, 255, 0, 128));
    highlight->setContent("Test highlight");
    highlight->addQuad(QRectF(10, 20, 100, 15));
    manager->addAnnotation(std::move(highlight));
    
    // Create note annotation
    auto note = std::make_unique<NoteAnnotation>();
    note->setPageNumber(0);
    note->setColor(QColor(255, 255, 0));
    note->setContent("Test note");
    note->setPosition(QPointF(200, 300));
    manager->addAnnotation(std::move(note));
    
    // Create shape annotation
    auto shape = std::make_unique<ShapeAnnotation>(ShapeAnnotation::ShapeType::Rectangle);
    shape->setPageNumber(1);
    shape->setColor(QColor(255, 0, 0, 128));
    shape->setContent("Test rectangle");
    shape->setRect(QRectF(50, 100, 200, 150));
    manager->addAnnotation(std::move(shape));
}

void TestIntegration::verifyAnnotationRendering(DocumentTab* tab)
{
    PdfController* controller = tab->getPdfController();
    
    if (tab->getPageCount() > 0) {
        // Request page with annotations
        QSignalSpy pageWithAnnotationsSpy(controller, &PdfController::pageWithAnnotationsReady);
        controller->requestPageWithAnnotations(0, 1.0);
        waitForSignal(&pageWithAnnotationsSpy);
        
        if (pageWithAnnotationsSpy.count() > 0) {
            QPixmap pixmap = pageWithAnnotationsSpy.takeFirst().at(1).value<QPixmap>();
            QVERIFY(!pixmap.isNull());
            QVERIFY(pixmap.width() > 0);
            QVERIFY(pixmap.height() > 0);
        }
    }
}

QTEST_MAIN(TestIntegration)
#include "test_integration.moc"
