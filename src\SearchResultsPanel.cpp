#include "SearchResultsPanel.h"
#include <QHeaderView>
#include <QScrollBar>
#include <QTextDocument>
#include <QApplication>
#include <QStyle>

SearchResultsPanel::SearchResultsPanel(QWidget *parent)
    : QWidget(parent)
    , m_currentResultIndex(-1)
{
    setupUi();
    setVisible(false);
}

void SearchResultsPanel::setupUi()
{
    setFixedWidth(300);
    setWindowTitle(tr("Search Results"));
    
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(8);
    m_mainLayout->setContentsMargins(8, 8, 8, 8);
    
    // Search input section
    m_searchGroup = new QGroupBox(tr("Search"));
    QVBoxLayout* searchLayout = new QVBoxLayout(m_searchGroup);
    
    // Search input
    m_searchEdit = new QLineEdit();
    m_searchEdit->setPlaceholderText(tr("Enter search term..."));
    connect(m_searchEdit, &QLineEdit::textChanged, this, &SearchResultsPanel::onSearchTextChanged);
    connect(m_searchEdit, &QLineEdit::returnPressed, this, [this]() {
        emit searchRequested(m_searchEdit->text(), m_caseSensitiveBox->isChecked(), m_wholeWordsBox->isChecked());
    });
    
    // Search options
    m_caseSensitiveBox = new QCheckBox(tr("Case sensitive"));
    connect(m_caseSensitiveBox, &QCheckBox::toggled, this, &SearchResultsPanel::onSearchOptionsChanged);
    
    m_wholeWordsBox = new QCheckBox(tr("Whole words"));
    connect(m_wholeWordsBox, &QCheckBox::toggled, this, &SearchResultsPanel::onSearchOptionsChanged);
    
    // Search buttons
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    m_searchButton = new QPushButton(tr("Search"));
    m_searchButton->setDefault(true);
    connect(m_searchButton, &QPushButton::clicked, this, [this]() {
        emit searchRequested(m_searchEdit->text(), m_caseSensitiveBox->isChecked(), m_wholeWordsBox->isChecked());
    });
    
    m_clearButton = new QPushButton(tr("Clear"));
    connect(m_clearButton, &QPushButton::clicked, this, &SearchResultsPanel::onClearClicked);
    
    buttonLayout->addWidget(m_searchButton);
    buttonLayout->addWidget(m_clearButton);
    
    searchLayout->addWidget(m_searchEdit);
    searchLayout->addWidget(m_caseSensitiveBox);
    searchLayout->addWidget(m_wholeWordsBox);
    searchLayout->addLayout(buttonLayout);
    
    m_mainLayout->addWidget(m_searchGroup);
    
    // Results section
    m_resultsGroup = new QGroupBox(tr("Results"));
    QVBoxLayout* resultsLayout = new QVBoxLayout(m_resultsGroup);
    
    // Results count
    m_resultsCountLabel = new QLabel(tr("No results"));
    m_resultsCountLabel->setAlignment(Qt::AlignCenter);
    m_resultsCountLabel->setStyleSheet("color: #666666; font-style: italic;");
    
    // Results list
    m_resultsList = new QListWidget();
    m_resultsList->setAlternatingRowColors(true);
    m_resultsList->setSelectionMode(QAbstractItemView::SingleSelection);
    connect(m_resultsList, &QListWidget::currentRowChanged, this, &SearchResultsPanel::onResultClicked);
    
    resultsLayout->addWidget(m_resultsCountLabel);
    resultsLayout->addWidget(m_resultsList);
    
    m_mainLayout->addWidget(m_resultsGroup);
    
    // Close button
    m_closeButton = new QPushButton(tr("Close Panel"));
    connect(m_closeButton, &QPushButton::clicked, this, &SearchResultsPanel::onCloseClicked);
    m_mainLayout->addWidget(m_closeButton);
    
    // Stretch to fill remaining space
    m_mainLayout->addStretch();
}

void SearchResultsPanel::setSearchResults(const QList<SearchResult>& results, const QString& searchTerm)
{
    m_searchResults = results;
    m_currentSearchTerm = searchTerm;
    m_currentResultIndex = -1;
    
    updateResultsDisplay();
    
    if (!results.isEmpty()) {
        setVisible(true);
    }
}

void SearchResultsPanel::clearResults()
{
    m_searchResults.clear();
    m_currentSearchTerm.clear();
    m_currentResultIndex = -1;
    m_resultsList->clear();
    m_resultsCountLabel->setText(tr("No results"));
    m_resultsCountLabel->setStyleSheet("color: #666666; font-style: italic;");
}

void SearchResultsPanel::setCurrentResult(int index)
{
    if (index >= 0 && index < m_searchResults.size()) {
        m_currentResultIndex = index;
        m_resultsList->setCurrentRow(index);
        m_resultsList->scrollToItem(m_resultsList->item(index));
    }
}

void SearchResultsPanel::updateResultsDisplay()
{
    m_resultsList->clear();
    
    if (m_searchResults.isEmpty()) {
        m_resultsCountLabel->setText(tr("No results found"));
        m_resultsCountLabel->setStyleSheet("color: #666666; font-style: italic;");
        return;
    }
    
    // Update count label
    m_resultsCountLabel->setText(tr("%1 results found").arg(m_searchResults.size()));
    m_resultsCountLabel->setStyleSheet("color: #000000; font-weight: bold;");
    
    // Add results to list
    for (int i = 0; i < m_searchResults.size(); ++i) {
        const SearchResult& result = m_searchResults[i];
        QString displayText = formatSearchResult(result, m_currentSearchTerm);
        
        QListWidgetItem* item = new QListWidgetItem(displayText);
        item->setData(Qt::UserRole, i); // Store result index
        item->setToolTip(tr("Page %1 - Click to navigate").arg(result.pageNumber + 1));
        
        m_resultsList->addItem(item);
    }
}

QString SearchResultsPanel::formatSearchResult(const SearchResult& result, const QString& searchTerm)
{
    QString pageText = tr("Page %1").arg(result.pageNumber + 1);
    
    // Get context around the found text
    QString context = result.context;
    
    return QString("%1: %2").arg(pageText, context);
}

QString SearchResultsPanel::getContextText(const QString& fullText, int position, int length, const QString& searchTerm)
{
    const int contextLength = 50; // Characters before and after
    
    // Find the search term in the text
    int searchPos = fullText.indexOf(searchTerm, 0, Qt::CaseInsensitive);
    if (searchPos == -1) {
        // Fallback: just return truncated text
        return fullText.left(100) + (fullText.length() > 100 ? "..." : "");
    }
    
    // Calculate context boundaries
    int start = qMax(0, searchPos - contextLength);
    int end = qMin(fullText.length(), searchPos + searchTerm.length() + contextLength);
    
    QString context = fullText.mid(start, end - start);
    
    // Add ellipsis if truncated
    if (start > 0) {
        context = "..." + context;
    }
    if (end < fullText.length()) {
        context = context + "...";
    }
    
    // Clean up whitespace
    context = context.simplified();
    
    return context;
}

void SearchResultsPanel::onResultClicked(int row)
{
    if (row >= 0 && row < m_searchResults.size()) {
        const SearchResult& result = m_searchResults[row];
        m_currentResultIndex = row;
        emit resultSelected(result.pageNumber, result.boundingBox);
    }
}

void SearchResultsPanel::onSearchTextChanged()
{
    m_searchButton->setEnabled(!m_searchEdit->text().trimmed().isEmpty());
}

void SearchResultsPanel::onSearchOptionsChanged()
{
    // If we have a current search term, automatically re-search with new options
    if (!m_searchEdit->text().trimmed().isEmpty()) {
        emit searchRequested(m_searchEdit->text(), m_caseSensitiveBox->isChecked(), m_wholeWordsBox->isChecked());
    }
}

void SearchResultsPanel::onCloseClicked()
{
    setVisible(false);
    emit panelClosed();
}

void SearchResultsPanel::onClearClicked()
{
    m_searchEdit->clear();
    clearResults();
}

void SearchResultsPanel::setVisible(bool visible)
{
    QWidget::setVisible(visible);
}
