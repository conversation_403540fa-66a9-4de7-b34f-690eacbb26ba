#ifndef ANNOTATION_H
#define ANNOTATION_H

#include <QObject>
#include <QRectF>
#include <QPointF>
#include <QColor>
#include <QDateTime>
#include <QJsonObject>
#include <QJsonArray>
#include <QPainterPath>
#include <QUuid>
#include <QFont>

// Forward declarations
class QPainter;

enum class AnnotationType {
    Highlight,
    Note,
    Drawing,
    Rectangle,
    Circle,
    Arrow,
    Text
};

class Annotation : public QObject
{
    Q_OBJECT

public:
    explicit Annotation(AnnotationType type, QObject *parent = nullptr);
    virtual ~Annotation() = default;

    // Basic properties
    QString getId() const { return m_id; }
    AnnotationType getType() const { return m_type; }
    int getPageNumber() const { return m_pageNumber; }
    void setPageNumber(int page) { m_pageNumber = page; }
    
    // Visual properties
    QColor getColor() const { return m_color; }
    void setColor(const QColor& color) { m_color = color; }
    qreal getOpacity() const { return m_opacity; }
    void setOpacity(qreal opacity) { m_opacity = opacity; }
    qreal getLineWidth() const { return m_lineWidth; }
    void setLineWidth(qreal width) { m_lineWidth = width; }
    
    // Content
    QString getContent() const { return m_content; }
    void setContent(const QString& content) { m_content = content; }
    QString getAuthor() const { return m_author; }
    void setAuthor(const QString& author) { m_author = author; }
    QDateTime getCreationDate() const { return m_creationDate; }
    QDateTime getModificationDate() const { return m_modificationDate; }
    void setModificationDate(const QDateTime& date) { m_modificationDate = date; }
    
    // Geometry (in PDF coordinates - points)
    virtual QRectF getBoundingRect() const = 0;
    virtual bool contains(const QPointF& point) const = 0;
    virtual void move(const QPointF& offset) = 0;
    virtual void resize(const QRectF& newBounds) = 0;
    
    // Rendering
    virtual void render(QPainter* painter, double dpi, double zoomFactor) const = 0;
    
    // Serialization
    virtual QJsonObject toJson() const;
    virtual void fromJson(const QJsonObject& json);
    
    // Selection and editing
    bool isSelected() const { return m_selected; }
    void setSelected(bool selected) { m_selected = selected; }
    bool isEditable() const { return m_editable; }
    void setEditable(bool editable) { m_editable = editable; }

signals:
    void annotationChanged();
    void annotationSelected(bool selected);

protected:
    QString m_id;
    AnnotationType m_type;
    int m_pageNumber = -1;  // Default to -1 (no page assigned)
    QColor m_color = Qt::yellow;
    qreal m_opacity = 0.5;
    qreal m_lineWidth = 2.0;
    QString m_content;
    QString m_author;
    QDateTime m_creationDate;
    QDateTime m_modificationDate;
    bool m_selected = false;
    bool m_editable = true;
};

// Highlight annotation for text selection
class HighlightAnnotation : public Annotation
{
    Q_OBJECT

public:
    explicit HighlightAnnotation(QObject *parent = nullptr);
    
    void addQuad(const QRectF& quad) { m_quads.append(quad); }
    void setQuads(const QList<QRectF>& quads) { m_quads = quads; }
    QList<QRectF> getQuads() const { return m_quads; }
    
    // Annotation interface
    QRectF getBoundingRect() const override;
    bool contains(const QPointF& point) const override;
    void move(const QPointF& offset) override;
    void resize(const QRectF& newBounds) override;
    void render(QPainter* painter, double dpi, double zoomFactor) const override;
    
    // Serialization
    QJsonObject toJson() const override;
    void fromJson(const QJsonObject& json) override;

private:
    QList<QRectF> m_quads; // Text selection rectangles in PDF coordinates
};

// Note annotation (sticky note)
class NoteAnnotation : public Annotation
{
    Q_OBJECT

public:
    explicit NoteAnnotation(QObject *parent = nullptr);
    
    QPointF getPosition() const { return m_position; }
    void setPosition(const QPointF& pos) { m_position = pos; }
    QSizeF getSize() const { return m_size; }
    void setSize(const QSizeF& size) { m_size = size; }
    
    // Annotation interface
    QRectF getBoundingRect() const override;
    bool contains(const QPointF& point) const override;
    void move(const QPointF& offset) override;
    void resize(const QRectF& newBounds) override;
    void render(QPainter* painter, double dpi, double zoomFactor) const override;
    
    // Serialization
    QJsonObject toJson() const override;
    void fromJson(const QJsonObject& json) override;

private:
    QPointF m_position; // Position in PDF coordinates
    QSizeF m_size = QSizeF(20, 20); // Size in PDF coordinates
};

// Drawing annotation (free-hand drawing)
class DrawingAnnotation : public Annotation
{
    Q_OBJECT

public:
    explicit DrawingAnnotation(QObject *parent = nullptr);
    
    void addPoint(const QPointF& point) { m_path.lineTo(point); updateBounds(); }
    void moveTo(const QPointF& point) { m_path.moveTo(point); updateBounds(); }
    QPainterPath getPath() const { return m_path; }
    void setPath(const QPainterPath& path) { m_path = path; updateBounds(); }
    
    // Annotation interface
    QRectF getBoundingRect() const override;
    bool contains(const QPointF& point) const override;
    void move(const QPointF& offset) override;
    void resize(const QRectF& newBounds) override;
    void render(QPainter* painter, double dpi, double zoomFactor) const override;
    
    // Serialization
    QJsonObject toJson() const override;
    void fromJson(const QJsonObject& json) override;

private:
    void updateBounds();
    
    QPainterPath m_path; // Drawing path in PDF coordinates
    QRectF m_boundingRect;
};

// Shape annotations (rectangle, circle, arrow)
class ShapeAnnotation : public Annotation
{
    Q_OBJECT

public:
    enum class ShapeType { Rectangle, Circle, Arrow };
    
    explicit ShapeAnnotation(ShapeType shapeType, QObject *parent = nullptr);
    
    QRectF getRect() const { return m_rect; }
    void setRect(const QRectF& rect) { m_rect = rect; }
    ShapeType getShapeType() const { return m_shapeType; }
    
    // Annotation interface
    QRectF getBoundingRect() const override;
    bool contains(const QPointF& point) const override;
    void move(const QPointF& offset) override;
    void resize(const QRectF& newBounds) override;
    void render(QPainter* painter, double dpi, double zoomFactor) const override;
    
    // Serialization
    QJsonObject toJson() const override;
    void fromJson(const QJsonObject& json) override;

private:
    ShapeType m_shapeType;
    QRectF m_rect; // Shape bounds in PDF coordinates
};

// Text annotation (text box)
class TextAnnotation : public Annotation
{
    Q_OBJECT

public:
    explicit TextAnnotation(QObject *parent = nullptr);
    
    QRectF getRect() const { return m_rect; }
    void setRect(const QRectF& rect) { m_rect = rect; }
    QString getText() const { return m_text; }
    void setText(const QString& text) { m_text = text; }
    QFont getFont() const { return m_font; }
    void setFont(const QFont& font) { m_font = font; }
    
    // Annotation interface
    QRectF getBoundingRect() const override;
    bool contains(const QPointF& point) const override;
    void move(const QPointF& offset) override;
    void resize(const QRectF& newBounds) override;
    void render(QPainter* painter, double dpi, double zoomFactor) const override;
    
    // Serialization
    QJsonObject toJson() const override;
    void fromJson(const QJsonObject& json) override;

private:
    QRectF m_rect; // Text box bounds in PDF coordinates
    QString m_text;
    QFont m_font;
};

#endif // ANNOTATION_H
