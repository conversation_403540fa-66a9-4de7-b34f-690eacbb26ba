#ifndef SEARCHRESULTSPANEL_H
#define SEARCHRESULTSPANEL_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QListWidget>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QCheckBox>
#include <QGroupBox>
#include <QSplitter>
#include "PdfController.h"

class SearchResultsPanel : public QWidget
{
    Q_OBJECT

public:
    explicit SearchResultsPanel(QWidget *parent = nullptr);
    
    void setSearchResults(const QList<SearchResult>& results, const QString& searchTerm);
    void clearResults();
    void setCurrentResult(int index);
    
    bool isVisible() const;
    void setVisible(bool visible) override;

signals:
    void resultSelected(int pageNumber, const QRectF& rect);
    void searchRequested(const QString& term, bool caseSensitive, bool wholeWords);
    void panelClosed();

private slots:
    void onResultClicked(int row);
    void onSearchTextChanged();
    void onSearchOptionsChanged();
    void onCloseClicked();
    void onClearClicked();

private:
    void setupUi();
    void updateResultsDisplay();
    QString formatSearchResult(const SearchResult& result, const QString& searchTerm);
    QString getContextText(const QString& fullText, int position, int length, const QString& searchTerm);

    // UI components
    QVBoxLayout* m_mainLayout;
    QGroupBox* m_searchGroup;
    QLineEdit* m_searchEdit;
    QCheckBox* m_caseSensitiveBox;
    QCheckBox* m_wholeWordsBox;
    QPushButton* m_searchButton;
    QPushButton* m_clearButton;
    QPushButton* m_closeButton;
    
    QGroupBox* m_resultsGroup;
    QListWidget* m_resultsList;
    QLabel* m_resultsCountLabel;
    
    // Data
    QList<SearchResult> m_searchResults;
    QString m_currentSearchTerm;
    int m_currentResultIndex;
};

#endif // SEARCHRESULTSPANEL_H
