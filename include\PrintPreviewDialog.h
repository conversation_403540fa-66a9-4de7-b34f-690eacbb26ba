#ifndef PRINTPREVIEWDIALOG_H
#define PRINTPREVIEWDIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QPushButton>
#include <QLabel>
#include <QSpinBox>
#include <QComboBox>
#include <QCheckBox>
#include <QGroupBox>
#include <QScrollArea>
#include <QSplitter>
#include <QPrintPreviewWidget>
#include <QPrinter>
#include <QPageSetupDialog>
#include <QPrintDialog>

class PdfController;

class PrintPreviewDialog : public QDialog
{
    Q_OBJECT

public:
    explicit PrintPreviewDialog(PdfController* controller, QWidget *parent = nullptr);

private slots:
    void onPrintClicked();
    void onPageSetupClicked();
    void onZoomChanged();
    void onOrientationChanged();
    void onPageRangeChanged();
    void onScalingChanged();
    void updatePreview();
    void printDocument(QPrinter* printer);

private:
    void setupUi();
    void setupPrintOptions();
    void updatePageRangeControls();
    QString getPageRangeText() const;

    PdfController* m_controller;
    QPrinter* m_printer;
    
    // UI components
    QVBoxLayout* m_mainLayout;
    QSplitter* m_splitter;
    
    // Preview area
    QPrintPreviewWidget* m_previewWidget;
    
    // Controls panel
    QWidget* m_controlsPanel;
    QVBoxLayout* m_controlsLayout;
    
    // Print options
    QGroupBox* m_printGroup;
    QPushButton* m_printButton;
    QPushButton* m_pageSetupButton;
    QPushButton* m_closeButton;
    
    // Page range options
    QGroupBox* m_pageRangeGroup;
    QCheckBox* m_allPagesRadio;
    QCheckBox* m_currentPageRadio;
    QCheckBox* m_pageRangeRadio;
    QSpinBox* m_fromPageSpinBox;
    QSpinBox* m_toPageSpinBox;
    QLabel* m_pageRangeLabel;
    
    // Layout and scaling options
    QGroupBox* m_layoutGroup;
    QComboBox* m_orientationCombo;
    QComboBox* m_scalingCombo;
    QCheckBox* m_fitToPageCheckBox;
    QCheckBox* m_centerOnPageCheckBox;
    
    // Preview controls
    QGroupBox* m_previewGroup;
    QComboBox* m_zoomCombo;
    QPushButton* m_firstPageButton;
    QPushButton* m_prevPageButton;
    QPushButton* m_nextPageButton;
    QPushButton* m_lastPageButton;
    QLabel* m_pageInfoLabel;
    
    // Data
    int m_totalPages;
    int m_currentPreviewPage;
};

#endif // PRINTPREVIEWDIALOG_H
