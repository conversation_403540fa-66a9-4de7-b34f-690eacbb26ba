#include "RichTooltip.h"
#include <QPainter>
#include <QPainterPath>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPropertyAnimation>
#include <QGraphicsDropShadowEffect>
#include <QApplication>
#include <QScreen>

// Static instance
RichTooltip* RichTooltip::s_instance = nullptr;

RichTooltip::RichTooltip(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_headerLayout(nullptr)
    , m_iconLabel(nullptr)
    , m_titleLabel(nullptr)
    , m_descriptionLabel(nullptr)
    , m_shortcutLabel(nullptr)
    , m_fadeAnimation(nullptr)
    , m_showTimer(nullptr)
    , m_hideTimer(nullptr)
    , m_themeMode(eTheme->getThemeMode())
    , m_currentIcon(ElaIconType::None)
{
    setupUI();
    
    // Connect to theme changes
    connect(eTheme, &ElaTheme::themeModeChanged, this, [this](ElaThemeType::ThemeMode themeMode) {
        m_themeMode = themeMode;
        updateTheme();
    });
}

RichTooltip::~RichTooltip()
{
    if (s_instance == this) {
        s_instance = nullptr;
    }
}

void RichTooltip::setupUI()
{
    // Set window properties
    setWindowFlags(Qt::ToolTip | Qt::FramelessWindowHint | Qt::NoDropShadowWindowHint);
    setAttribute(Qt::WA_TranslucentBackground);
    setAttribute(Qt::WA_ShowWithoutActivating);
    setMaximumWidth(MAX_WIDTH);
    
    // Add drop shadow
    QGraphicsDropShadowEffect* shadowEffect = new QGraphicsDropShadowEffect(this);
    shadowEffect->setBlurRadius(12);
    shadowEffect->setColor(QColor(0, 0, 0, 100));
    shadowEffect->setOffset(0, 2);
    setGraphicsEffect(shadowEffect);
    
    // Create main layout
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(TOOLTIP_PADDING, TOOLTIP_PADDING, TOOLTIP_PADDING, TOOLTIP_PADDING);
    m_mainLayout->setSpacing(8);
    
    // Create header layout with icon and title
    m_headerLayout = new QHBoxLayout();
    m_headerLayout->setSpacing(8);
    
    // Icon label
    m_iconLabel = new ElaLabel(this);
    m_iconLabel->setFixedSize(ICON_SIZE, ICON_SIZE);
    m_iconLabel->setAlignment(Qt::AlignCenter);
    m_iconLabel->hide(); // Hidden by default
    m_headerLayout->addWidget(m_iconLabel);
    
    // Title label
    m_titleLabel = new ElaText(this);
    m_titleLabel->setTextPixelSize(13);
    QFont titleFont = m_titleLabel->font();
    titleFont.setBold(true);
    m_titleLabel->setFont(titleFont);
    m_headerLayout->addWidget(m_titleLabel);
    
    m_headerLayout->addStretch();
    
    // Shortcut label
    m_shortcutLabel = new ElaText(this);
    m_shortcutLabel->setTextPixelSize(11);
    m_shortcutLabel->setAlignment(Qt::AlignRight);
    m_shortcutLabel->hide(); // Hidden by default
    m_headerLayout->addWidget(m_shortcutLabel);
    
    m_mainLayout->addLayout(m_headerLayout);
    
    // Description label
    m_descriptionLabel = new ElaText(this);
    m_descriptionLabel->setTextPixelSize(11);
    m_descriptionLabel->setWordWrap(true);
    m_descriptionLabel->hide(); // Hidden by default
    m_mainLayout->addWidget(m_descriptionLabel);
    
    // Set up animations
    m_fadeAnimation = new QPropertyAnimation(this, "windowOpacity", this);
    m_fadeAnimation->setDuration(200);
    m_fadeAnimation->setEasingCurve(QEasingCurve::OutCubic);
    
    // Set up timers
    m_showTimer = new QTimer(this);
    m_showTimer->setSingleShot(true);
    connect(m_showTimer, &QTimer::timeout, this, &RichTooltip::fadeIn);
    
    m_hideTimer = new QTimer(this);
    m_hideTimer->setSingleShot(true);
    connect(m_hideTimer, &QTimer::timeout, this, &RichTooltip::fadeOut);
    
    updateTheme();
}

void RichTooltip::updateTheme()
{
    // Update colors based on theme
    QPalette palette = this->palette();

    if (m_themeMode == ElaThemeType::Light) {
        palette.setColor(QPalette::WindowText, QColor(51, 51, 51));
        if (m_shortcutLabel) {
            m_shortcutLabel->setStyleSheet("color: #666666; background-color: rgba(0, 0, 0, 0.05); "
                                          "border-radius: 3px; padding: 2px 6px;");
        }
    } else {
        palette.setColor(QPalette::WindowText, QColor(255, 255, 255));
        if (m_shortcutLabel) {
            m_shortcutLabel->setStyleSheet("color: #CCCCCC; background-color: rgba(255, 255, 255, 0.1); "
                                          "border-radius: 3px; padding: 2px 6px;");
        }
    }

    QWidget::setPalette(palette);
    update();
}

void RichTooltip::showTooltip(const QPoint& position, const QString& title, 
                             const QString& description, const QString& shortcut,
                             ElaIconType::IconName icon)
{
    if (!s_instance) {
        s_instance = new RichTooltip();
    }
    
    s_instance->setContent(title, description, shortcut, icon);
    s_instance->updatePosition(position);
    s_instance->m_showTimer->start(SHOW_DELAY);
}

void RichTooltip::hideTooltip()
{
    if (s_instance) {
        s_instance->m_showTimer->stop();
        s_instance->m_hideTimer->start(HIDE_DELAY);
    }
}

void RichTooltip::setContent(const QString& title, const QString& description, 
                            const QString& shortcut, ElaIconType::IconName icon)
{
    m_currentTitle = title;
    m_currentDescription = description;
    m_currentShortcut = shortcut;
    m_currentIcon = icon;
    
    // Update title
    m_titleLabel->setText(title);
    
    // Update icon
    if (icon != ElaIconType::None) {
        m_iconLabel->setPixmap(ElaIcon::getInstance()->getElaIcon(icon, ICON_SIZE).pixmap(ICON_SIZE, ICON_SIZE));
        m_iconLabel->show();
    } else {
        m_iconLabel->hide();
    }
    
    // Update description
    if (!description.isEmpty()) {
        m_descriptionLabel->setText(description);
        m_descriptionLabel->show();
    } else {
        m_descriptionLabel->hide();
    }
    
    // Update shortcut
    if (!shortcut.isEmpty()) {
        m_shortcutLabel->setText(shortcut);
        m_shortcutLabel->show();
    } else {
        m_shortcutLabel->hide();
    }
    
    // Adjust size
    adjustSize();
}

void RichTooltip::updatePosition(const QPoint& position)
{
    QScreen* screen = QApplication::screenAt(position);
    if (!screen) {
        screen = QApplication::primaryScreen();
    }
    
    QRect screenGeometry = screen->availableGeometry();
    QSize tooltipSize = sizeHint();
    
    QPoint tooltipPos = position + QPoint(10, 10);
    
    // Adjust if tooltip would go off screen
    if (tooltipPos.x() + tooltipSize.width() > screenGeometry.right()) {
        tooltipPos.setX(position.x() - tooltipSize.width() - 10);
    }
    
    if (tooltipPos.y() + tooltipSize.height() > screenGeometry.bottom()) {
        tooltipPos.setY(position.y() - tooltipSize.height() - 10);
    }
    
    move(tooltipPos);
}

void RichTooltip::fadeIn()
{
    setWindowOpacity(0.0);
    show();
    raise();
    
    m_fadeAnimation->setStartValue(0.0);
    m_fadeAnimation->setEndValue(0.95);
    m_fadeAnimation->start();
}

void RichTooltip::fadeOut()
{
    m_fadeAnimation->setStartValue(windowOpacity());
    m_fadeAnimation->setEndValue(0.0);
    
    connect(m_fadeAnimation, &QPropertyAnimation::finished, this, [this]() {
        hide();
        m_fadeAnimation->disconnect();
    });
    
    m_fadeAnimation->start();
}

void RichTooltip::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Draw rounded background
    QPainterPath path;
    path.addRoundedRect(rect().adjusted(2, 2, -2, -2), TOOLTIP_RADIUS, TOOLTIP_RADIUS);
    
    QColor backgroundColor;
    QColor borderColor;
    
    if (m_themeMode == ElaThemeType::Light) {
        backgroundColor = QColor(255, 255, 255, 250);
        borderColor = QColor(0, 0, 0, 30);
    } else {
        backgroundColor = QColor(45, 45, 45, 250);
        borderColor = QColor(255, 255, 255, 30);
    }
    
    painter.fillPath(path, backgroundColor);
    painter.setPen(QPen(borderColor, 1));
    painter.drawPath(path);
}

void RichTooltip::showEvent(QShowEvent *event)
{
    updateTheme();
    QWidget::showEvent(event);
}

void RichTooltip::hideEvent(QHideEvent *event)
{
    QWidget::hideEvent(event);
}

bool RichTooltip::eventFilter(QObject *obj, QEvent *event)
{
    return QWidget::eventFilter(obj, event);
}
