# Documentation for Optimized PDF Viewer

Welcome to the comprehensive documentation system for the Optimized PDF Viewer project. This directory contains all documentation sources, tools, and configurations for generating high-quality, maintainable documentation.

## 📚 Documentation Structure

```
docs/
├── README.md                          # This file - documentation overview
├── USER_MANUAL.md                     # Comprehensive user guide
├── DEVELOPER_GUIDE.md                 # Developer documentation
├── INSTALLATION_GUIDE.md              # Installation instructions
├── RIBBON_INTERFACE_GUIDE.md          # UI interface guide
├── DOCUMENTATION_STANDARDS.md         # Documentation standards and guidelines
├── config/
│   └── documentation.cmake            # CMake documentation configuration
├── doxygen-theme/                     # Modern Doxygen theme
│   ├── doxygen-awesome.css            # Base theme styles
│   ├── doxygen-awesome-sidebar-only.css # Sidebar-only variant
│   ├── custom.css                     # Project-specific customizations
│   ├── header.html                    # Custom HTML header
│   ├── footer.html                    # Custom HTML footer
│   ├── doxygen-awesome-darkmode-toggle.js # Dark mode functionality
│   ├── doxygen-awesome-fragment-copy-button.js # Code copy buttons
│   └── doxygen-awesome-paragraph-link.js # Paragraph anchors
├── examples/                          # Code examples for documentation
│   └── README.md                      # Examples organization guide
└── images/                            # Documentation images and assets
    └── README.md                      # Image guidelines and requirements
```

## 🚀 Quick Start

### Prerequisites

Before generating documentation, ensure you have:

- **Python 3.7+** - For documentation generation scripts
- **Doxygen** - For API documentation (optional but recommended)
- **Graphviz** - For diagrams and charts (optional)
- **CMake** - For build system integration

### Generate All Documentation

#### Using Scripts (Recommended)

**Windows:**
```cmd
scripts\generate_docs.bat --clean --open
```

**Linux/macOS:**
```bash
./scripts/generate_docs.sh --clean --open
```

#### Using CMake

```bash
# Configure with documentation enabled
cmake -B build -DBUILD_DOCUMENTATION=ON

# Generate documentation
cmake --build build --target docs

# Or generate specific documentation types
cmake --build build --target doxygen_docs    # API documentation only
cmake --build build --target user_docs_gen   # User documentation only
```

#### Using Python Directly

```bash
python scripts/generate_docs.py --project-root . --clean
```

### View Documentation

After generation, open `build/docs/index.html` in your browser to access:

- **API Documentation** - Complete code reference with examples
- **User Manual** - End-user guides and tutorials
- **Developer Guide** - Technical documentation for contributors
- **Installation Guide** - Setup and build instructions

## 📖 Documentation Types

### 1. API Documentation

**Generated from:** Source code comments using Doxygen  
**Location:** `build/docs/api/html/`  
**Features:**
- Comprehensive class and method documentation
- Code examples and usage patterns
- Interactive diagrams and inheritance charts
- Dark/light theme support
- Advanced search functionality
- Mobile-responsive design

### 2. User Documentation

**Generated from:** Markdown files in `docs/`  
**Location:** `build/docs/user/`  
**Includes:**
- User Manual with step-by-step guides
- Installation instructions for all platforms
- Interface guides with screenshots
- Troubleshooting and FAQ sections
- Keyboard shortcuts reference

### 3. Developer Documentation

**Generated from:** Markdown files and code analysis  
**Location:** `build/docs/developer/`  
**Includes:**
- Architecture overview and design decisions
- Build system documentation
- Contributing guidelines and code standards
- Plugin development guide
- Performance optimization tips

## 🛠️ Documentation Tools

### Generation Scripts

- **`generate_docs.py`** - Main documentation generator
- **`generate_docs.bat`** - Windows batch script wrapper
- **`generate_docs.sh`** - Linux/macOS shell script wrapper

### Maintenance Tools

- **`validate_docs.py`** - Comprehensive documentation validator
- **`maintain_docs.py`** - Documentation maintenance utilities

### Validation Features

- **Link Checking** - Validates all internal and external links
- **HTML Validation** - Checks HTML structure and standards compliance
- **Accessibility Testing** - Ensures WCAG compliance
- **Performance Analysis** - Identifies optimization opportunities
- **Content Analysis** - Detects outdated or missing content

### Maintenance Features

- **Image Optimization** - Automatic image compression and optimization
- **Asset Cleanup** - Removes unused images and files
- **Link Updates** - Bulk update links when files are moved
- **Metrics Collection** - Documentation health and usage statistics
- **Outdated Content Detection** - Identifies potentially stale documentation

## 🎨 Theme and Styling

The documentation uses a modern, professional theme based on Doxygen Awesome CSS with custom enhancements:

### Features

- **Responsive Design** - Works on desktop, tablet, and mobile
- **Dark/Light Themes** - Automatic system theme detection with manual toggle
- **Modern UI** - Clean, professional appearance inspired by VS Code
- **Enhanced Navigation** - Improved sidebar and search functionality
- **Code Features** - Syntax highlighting, copy buttons, and line numbers
- **Accessibility** - Screen reader support and keyboard navigation

### Customization

Theme customization is available through:
- **`custom.css`** - Project-specific style overrides
- **`header.html`** - Custom header with branding and navigation
- **`footer.html`** - Enhanced footer with links and information
- **JavaScript enhancements** - Interactive features and functionality

## 🔧 Configuration

### CMake Options

```cmake
# Documentation build options
option(BUILD_DOCUMENTATION "Build comprehensive documentation" ON)
option(BUILD_API_DOCS "Build API documentation with Doxygen" ON)
option(BUILD_USER_DOCS "Build user documentation" ON)
option(DOCS_GENERATE_PDF "Generate PDF versions" OFF)
option(DOCS_DEPLOY_READY "Generate deployment-ready docs" OFF)
```

### Doxygen Configuration

Key Doxygen settings are configured in `Doxyfile.in`:
- **Input Sources** - Include directories and source files
- **Output Formats** - HTML, XML for further processing
- **Theme Integration** - Custom CSS and JavaScript files
- **Diagram Generation** - Class diagrams and call graphs
- **Search Configuration** - Client-side search functionality

## 🚀 CI/CD Integration

### GitHub Actions Workflow

The project includes a comprehensive GitHub Actions workflow (`.github/workflows/documentation.yml`) that:

- **Builds documentation** on every push and pull request
- **Validates content** for broken links and accessibility
- **Runs performance tests** using Lighthouse
- **Deploys to GitHub Pages** automatically
- **Generates reports** and comments on pull requests

### Workflow Features

- **Multi-platform support** - Tests on Linux, Windows, and macOS
- **Dependency caching** - Faster builds with cached tools
- **Artifact management** - Stores documentation packages
- **Quality gates** - Prevents deployment of broken documentation
- **Automated reporting** - Detailed feedback on documentation changes

## 📊 Quality Assurance

### Documentation Standards

All documentation follows established standards defined in `DOCUMENTATION_STANDARDS.md`:

- **Code Documentation** - Comprehensive Doxygen comments
- **Writing Style** - Clear, consistent, and accessible language
- **Structure** - Logical organization and navigation
- **Examples** - Practical, tested code examples
- **Maintenance** - Regular updates and validation

### Validation Checks

- **Link Validation** - All internal and external links verified
- **HTML Compliance** - Valid HTML5 structure
- **Accessibility** - WCAG 2.1 AA compliance
- **Performance** - Optimized loading and rendering
- **Content Quality** - Spelling, grammar, and technical accuracy

## 🤝 Contributing to Documentation

### Getting Started

1. **Read the standards** - Review `DOCUMENTATION_STANDARDS.md`
2. **Set up tools** - Install required dependencies
3. **Make changes** - Edit documentation files
4. **Test locally** - Generate and validate documentation
5. **Submit PR** - Include documentation updates with code changes

### Best Practices

- **Update with code changes** - Keep documentation synchronized
- **Include examples** - Provide practical usage examples
- **Test thoroughly** - Validate all changes before submission
- **Follow standards** - Maintain consistency with existing documentation
- **Consider accessibility** - Ensure content is accessible to all users

### Review Process

1. **Automated checks** - CI/CD validates all changes
2. **Peer review** - Team members review for accuracy and clarity
3. **User testing** - Test with target audience when appropriate
4. **Final approval** - Documentation lead approves changes

## 📞 Support and Resources

### Getting Help

- **Issues** - Report documentation bugs or requests on GitHub
- **Discussions** - Ask questions in GitHub Discussions
- **Wiki** - Additional resources and community contributions
- **Email** - Contact the documentation team directly

### External Resources

- **Doxygen Documentation** - https://www.doxygen.nl/manual/
- **Markdown Guide** - https://www.markdownguide.org/
- **Accessibility Guidelines** - https://www.w3.org/WAI/WCAG21/quickref/
- **GitHub Pages** - https://docs.github.com/en/pages

---

**Last Updated:** December 2024  
**Version:** 1.0.0  
**Maintainers:** PDF Viewer Documentation Team
