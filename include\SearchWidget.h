#ifndef SEARCHWIDGET_H
#define SEARCHWIDGET_H

#include <QWidget>
#include <QLineEdit>
#include <QCheckBox>
#include <QPushButton>
#include <QLabel>
#include <QComboBox>
#include <QCompleter>
#include <QStringListModel>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QMenu>
#include <QAction>
#include <QTimer>
#include "SearchHistory.h"

class QToolButton;
class QProgressBar;

/**
 * @brief Enhanced search widget with history, auto-completion, and advanced options
 */
class SearchWidget : public QWidget
{
    Q_OBJECT

public:
    explicit SearchWidget(QWidget *parent = nullptr);
    ~SearchWidget();

    // Search configuration
    QString getSearchTerm() const;
    void setSearchTerm(const QString& term);
    bool isCaseSensitive() const;
    void setCaseSensitive(bool enabled);
    bool isWholeWords() const;
    void setWholeWords(bool enabled);
    bool isHighlightAll() const;
    void setHighlightAll(bool enabled);
    
    // Search history
    void setSearchHistory(SearchHistory* history);
    SearchHistory* getSearchHistory() const;
    
    // Search state
    void setSearchInProgress(bool inProgress);
    bool isSearchInProgress() const;
    void setSearchResults(int currentIndex, int totalResults);
    void clearSearchResults();
    
    // UI state
    void setEnabled(bool enabled);
    void setVisible(bool visible) override;
    void focusSearchField();
    
    // Advanced features
    void setAutoSearchEnabled(bool enabled);
    bool isAutoSearchEnabled() const;
    void setAutoSearchDelay(int milliseconds);
    int getAutoSearchDelay() const;

signals:
    void searchRequested(const QString& term, bool caseSensitive, bool wholeWords);
    void searchCleared();
    void findNext();
    void findPrevious();
    void searchOptionsChanged();
    void historyItemSelected(const QString& term, bool caseSensitive, bool wholeWords);

public slots:
    void performSearch();
    void clearSearch();
    void showSearchHistory();
    void selectHistoryItem(const SearchHistoryEntry& entry);

private slots:
    void onSearchTextChanged();
    void onSearchTextEdited();
    void onSearchOptionsChanged();
    void onAutoSearchTimeout();
    void onHistoryMenuTriggered(QAction* action);
    void onCompleterActivated(const QString& text);
    void updateCompleter();

private:
    void setupUi();
    void setupSearchField();
    void setupNavigationButtons();
    void setupOptionsCheckboxes();
    void setupHistoryButton();
    void setupProgressIndicator();
    void connectSignals();
    void updateNavigationButtons();
    void updateHistoryCompleter();
    void createHistoryMenu();
    QString formatHistoryEntry(const SearchHistoryEntry& entry) const;
    void applySearchStyling();

    // UI Components
    QHBoxLayout* m_mainLayout;
    QVBoxLayout* m_topLayout;
    QHBoxLayout* m_searchLayout;
    QHBoxLayout* m_optionsLayout;
    QHBoxLayout* m_navigationLayout;
    
    // Search field and controls
    QLineEdit* m_searchEdit;
    QCompleter* m_completer;
    QStringListModel* m_completerModel;
    QPushButton* m_searchButton;
    QPushButton* m_clearButton;
    QToolButton* m_historyButton;
    QMenu* m_historyMenu;
    
    // Navigation controls
    QPushButton* m_findPrevButton;
    QPushButton* m_findNextButton;
    QLabel* m_resultsLabel;
    QProgressBar* m_progressBar;
    
    // Search options
    QCheckBox* m_caseSensitiveCheckBox;
    QCheckBox* m_wholeWordsCheckBox;
    QCheckBox* m_highlightAllCheckBox;
    
    // Search state
    SearchHistory* m_searchHistory;
    bool m_searchInProgress;
    int m_currentResultIndex;
    int m_totalResults;
    
    // Auto-search functionality
    bool m_autoSearchEnabled;
    int m_autoSearchDelay;
    QTimer* m_autoSearchTimer;
    QString m_lastSearchTerm;
    
    // Styling
    QString m_normalStyleSheet;
    QString m_searchingStyleSheet;
    QString m_foundStyleSheet;
    QString m_notFoundStyleSheet;
};

#endif // SEARCHWIDGET_H
