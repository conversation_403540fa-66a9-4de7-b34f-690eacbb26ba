#include "Logger.h"
#include <QStandardPaths>
#include <QDir>
#include <QApplication>
#include <QSysInfo>
#include <QThread>

// Static members
Logger* Logger::s_instance = nullptr;
QMutex Logger::s_mutex;
const QString Logger::DEFAULT_LOG_FILE = "pdf_viewer.log";

Logger* Logger::instance()
{
    QMutexLocker locker(&s_mutex);
    if (!s_instance) {
        s_instance = new Logger();
    }
    return s_instance;
}

Logger::Logger(QObject *parent)
    : QObject(parent)
    , m_logLevel(LogLevel::Info)
    , m_logToFile(true)
    , m_logToConsole(true)
    , m_maxLogFileSize(DEFAULT_MAX_FILE_SIZE)
    , m_logFile(nullptr)
    , m_logStream(nullptr)
{
    // Set default log file path
    QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(appDataPath);
    m_logFilePath = QDir(appDataPath).filePath(DEFAULT_LOG_FILE);
    
    // Initialize log file
    if (m_logToFile) {
        m_logFile = new QFile(m_logFilePath);
        if (m_logFile->open(QIODevice::WriteOnly | QIODevice::Append)) {
            m_logStream = new QTextStream(m_logFile);
            // Qt6 uses UTF-8 by default, no need to set codec
        }
    }
    
    // Log application startup
    logSystemInfo();
    info("Application started", "System");
}

Logger::~Logger()
{
    info("Application shutting down", "System");
    
    if (m_logStream) {
        delete m_logStream;
        m_logStream = nullptr;
    }
    
    if (m_logFile) {
        m_logFile->close();
        delete m_logFile;
        m_logFile = nullptr;
    }
}

void Logger::debug(const QString& message, const QString& category)
{
    if (m_logLevel <= LogLevel::Debug) {
        QString formatted = formatMessage(LogLevel::Debug, message, category);
        
        if (m_logToConsole) {
            writeToConsole(formatted);
        }
        
        if (m_logToFile) {
            writeToFile(formatted);
        }
        
        emit logMessage(LogLevel::Debug, message, category);
    }
}

void Logger::info(const QString& message, const QString& category)
{
    if (m_logLevel <= LogLevel::Info) {
        QString formatted = formatMessage(LogLevel::Info, message, category);
        
        if (m_logToConsole) {
            writeToConsole(formatted);
        }
        
        if (m_logToFile) {
            writeToFile(formatted);
        }
        
        emit logMessage(LogLevel::Info, message, category);
    }
}

void Logger::warning(const QString& message, const QString& category)
{
    if (m_logLevel <= LogLevel::Warning) {
        QString formatted = formatMessage(LogLevel::Warning, message, category);
        
        if (m_logToConsole) {
            writeToConsole(formatted);
        }
        
        if (m_logToFile) {
            writeToFile(formatted);
        }
        
        emit logMessage(LogLevel::Warning, message, category);
    }
}

void Logger::error(const QString& message, const QString& category)
{
    if (m_logLevel <= LogLevel::Error) {
        QString formatted = formatMessage(LogLevel::Error, message, category);
        
        if (m_logToConsole) {
            writeToConsole(formatted);
        }
        
        if (m_logToFile) {
            writeToFile(formatted);
        }
        
        emit logMessage(LogLevel::Error, message, category);
    }
}

void Logger::critical(const QString& message, const QString& category)
{
    QString formatted = formatMessage(LogLevel::Critical, message, category);
    
    if (m_logToConsole) {
        writeToConsole(formatted);
    }
    
    if (m_logToFile) {
        writeToFile(formatted);
    }
    
    emit logMessage(LogLevel::Critical, message, category);
}

QString Logger::formatMessage(LogLevel level, const QString& message, const QString& category) const
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    QString levelStr = levelToString(level);
    QString threadId = QString::number(reinterpret_cast<quintptr>(QThread::currentThreadId()), 16);
    
    QString formatted = QString("[%1] [%2] [Thread:%3]")
                       .arg(timestamp)
                       .arg(levelStr)
                       .arg(threadId);
    
    if (!category.isEmpty()) {
        formatted += QString(" [%1]").arg(category);
    }
    
    formatted += QString(" %1").arg(message);
    
    return formatted;
}

QString Logger::levelToString(LogLevel level) const
{
    switch (level) {
        case LogLevel::Debug: return "DEBUG";
        case LogLevel::Info: return "INFO ";
        case LogLevel::Warning: return "WARN ";
        case LogLevel::Error: return "ERROR";
        case LogLevel::Critical: return "CRIT ";
        default: return "UNKN ";
    }
}

void Logger::writeToFile(const QString& formattedMessage)
{
    QMutexLocker locker(&m_fileMutex);
    
    if (m_logStream) {
        *m_logStream << formattedMessage << Qt::endl;
        m_logStream->flush();
        
        checkLogFileSize();
    }
}

void Logger::writeToConsole(const QString& formattedMessage)
{
    qDebug().noquote() << formattedMessage;
}

void Logger::checkLogFileSize()
{
    if (m_logFile && m_logFile->size() > m_maxLogFileSize) {
        rotateLogFile();
    }
}

void Logger::rotateLogFile()
{
    if (!m_logFile) return;
    
    // Close current file
    if (m_logStream) {
        delete m_logStream;
        m_logStream = nullptr;
    }
    m_logFile->close();
    
    // Rename current file to backup
    QString backupPath = m_logFilePath + ".old";
    QFile::remove(backupPath);
    QFile::rename(m_logFilePath, backupPath);
    
    // Create new log file
    if (m_logFile->open(QIODevice::WriteOnly | QIODevice::Append)) {
        m_logStream = new QTextStream(m_logFile);
        // Qt6 uses UTF-8 by default, no need to set codec
        info("Log file rotated", "System");
    }
}

void Logger::logSystemInfo()
{
    info(QString("Operating System: %1").arg(QSysInfo::prettyProductName()), "System");
    info(QString("Qt Version: %1").arg(QT_VERSION_STR), "System");
    info(QString("Application Version: %1").arg(QApplication::applicationVersion()), "System");
    info(QString("Build Architecture: %1").arg(QSysInfo::buildCpuArchitecture()), "System");
}

void Logger::startTimer(const QString& operation)
{
    QMutexLocker locker(&m_timerMutex);
    m_timers[operation] = QDateTime::currentDateTime();
    debug(QString("Timer started: %1").arg(operation), "Performance");
}

void Logger::endTimer(const QString& operation)
{
    QMutexLocker locker(&m_timerMutex);
    
    if (m_timers.contains(operation)) {
        QDateTime startTime = m_timers.take(operation);
        qint64 elapsed = startTime.msecsTo(QDateTime::currentDateTime());
        info(QString("Timer ended: %1 (%2 ms)").arg(operation).arg(elapsed), "Performance");
    } else {
        warning(QString("Timer not found: %1").arg(operation), "Performance");
    }
}

void Logger::logException(const QString& exception, const QString& location)
{
    critical(QString("Exception in %1: %2").arg(location).arg(exception), "Exception");
}

// Getters and setters
void Logger::setLogLevel(LogLevel level) { m_logLevel = level; }
LogLevel Logger::getLogLevel() const { return m_logLevel; }

void Logger::setLogToFile(bool enabled) { m_logToFile = enabled; }
bool Logger::getLogToFile() const { return m_logToFile; }

void Logger::setLogToConsole(bool enabled) { m_logToConsole = enabled; }
bool Logger::getLogToConsole() const { return m_logToConsole; }

void Logger::setLogFilePath(const QString& path) { m_logFilePath = path; }
QString Logger::getLogFilePath() const { return m_logFilePath; }

void Logger::setMaxLogFileSize(qint64 maxSize) { m_maxLogFileSize = maxSize; }
qint64 Logger::getMaxLogFileSize() const { return m_maxLogFileSize; }
