#!/usr/bin/env python3
"""
Documentation Generation Script for Optimized PDF Viewer

This script automates the generation of comprehensive documentation including:
- API documentation with Doxygen
- User documentation compilation
- Documentation validation and link checking
- Multi-format output generation
"""

import os
import sys
import subprocess
import argparse
import shutil
import json
from pathlib import Path
from typing import List, Dict, Optional

class DocumentationGenerator:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root).resolve()
        self.build_dir = self.project_root / "build"
        self.docs_dir = self.project_root / "docs"
        self.output_dir = self.build_dir / "docs"
        
    def setup_directories(self):
        """Create necessary directories for documentation generation."""
        print("Setting up documentation directories...")
        
        # Create output directories
        self.output_dir.mkdir(parents=True, exist_ok=True)
        (self.output_dir / "api").mkdir(exist_ok=True)
        (self.output_dir / "user").mkdir(exist_ok=True)
        (self.output_dir / "developer").mkdir(exist_ok=True)
        
        print(f"✓ Documentation output directory: {self.output_dir}")
    
    def check_dependencies(self) -> bool:
        """Check if required tools are available."""
        print("Checking documentation dependencies...")
        
        required_tools = {
            'doxygen': 'Doxygen (for API documentation)',
            'dot': 'Graphviz (for diagrams)',
            'cmake': 'CMake (for build integration)'
        }
        
        missing_tools = []
        
        for tool, description in required_tools.items():
            if not shutil.which(tool):
                missing_tools.append(f"  - {tool}: {description}")
                print(f"✗ {tool} not found")
            else:
                print(f"✓ {tool} found")
        
        if missing_tools:
            print("\nMissing required tools:")
            print("\n".join(missing_tools))
            print("\nPlease install the missing tools and try again.")
            return False
        
        return True
    
    def generate_doxygen_config(self) -> Path:
        """Generate Doxygen configuration file from template."""
        print("Generating Doxygen configuration...")
        
        doxyfile_template = self.project_root / "Doxyfile.in"
        doxyfile_output = self.build_dir / "Doxyfile"
        
        if not doxyfile_template.exists():
            raise FileNotFoundError(f"Doxygen template not found: {doxyfile_template}")
        
        # Read template and substitute variables
        with open(doxyfile_template, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Substitute CMake-style variables
        substitutions = {
            '@DOXYGEN_PROJECT_NAME@': 'Optimized PDF Viewer',
            '@DOXYGEN_PROJECT_NUMBER@': '1.0.0',
            '@DOXYGEN_PROJECT_BRIEF@': 'High-performance PDF viewer with advanced annotation capabilities',
            '@DOXYGEN_OUTPUT_DIRECTORY@': str(self.output_dir / "api"),
            '@DOXYGEN_INPUT_DIRECTORY@': f"{self.project_root / 'include'} {self.project_root / 'src'}",
            '@CMAKE_CURRENT_SOURCE_DIR@': str(self.project_root),
            '@DOXYGEN_USE_MDFILE_AS_MAINPAGE@': str(self.project_root / "README.md"),
            '@DOXYGEN_EXAMPLE_PATH@': str(self.docs_dir / "examples"),
            '@DOXYGEN_IMAGE_PATH@': str(self.docs_dir / "images"),
            '@DOXYGEN_STRIP_FROM_PATH@': str(self.project_root),
            '@DOXYGEN_STRIP_FROM_INC_PATH@': str(self.project_root / "include"),
            '@DOXYGEN_HTML_HEADER@': str(self.docs_dir / "doxygen-theme" / "header.html"),
            '@DOXYGEN_HTML_FOOTER@': str(self.docs_dir / "doxygen-theme" / "footer.html"),
            '@DOXYGEN_HTML_EXTRA_STYLESHEET@': " ".join([
                str(self.docs_dir / "doxygen-theme" / "doxygen-awesome.css"),
                str(self.docs_dir / "doxygen-theme" / "doxygen-awesome-sidebar-only.css"),
                str(self.docs_dir / "doxygen-theme" / "custom.css")
            ]),
            '@DOXYGEN_HTML_EXTRA_FILES@': " ".join([
                str(self.docs_dir / "doxygen-theme" / "doxygen-awesome-darkmode-toggle.js"),
                str(self.docs_dir / "doxygen-theme" / "doxygen-awesome-fragment-copy-button.js"),
                str(self.docs_dir / "doxygen-theme" / "doxygen-awesome-paragraph-link.js")
            ]),
            # Boolean values
            '@DOXYGEN_RECURSIVE@': 'YES',
            '@DOXYGEN_EXTRACT_ALL@': 'YES',
            '@DOXYGEN_EXTRACT_PRIVATE@': 'NO',
            '@DOXYGEN_EXTRACT_STATIC@': 'YES',
            '@DOXYGEN_GENERATE_HTML@': 'YES',
            '@DOXYGEN_GENERATE_LATEX@': 'NO',
            '@DOXYGEN_GENERATE_XML@': 'YES',
            '@DOXYGEN_HTML_OUTPUT@': 'html',
            '@DOXYGEN_XML_OUTPUT@': 'xml',
            '@DOXYGEN_JAVADOC_AUTOBRIEF@': 'YES',
            '@DOXYGEN_QT_AUTOBRIEF@': 'YES',
            '@DOXYGEN_MULTILINE_CPP_IS_BRIEF@': 'YES',
            '@DOXYGEN_INHERIT_DOCS@': 'YES',
            '@DOXYGEN_SEPARATE_MEMBER_PAGES@': 'NO',
            '@DOXYGEN_TAB_SIZE@': '4',
            '@DOXYGEN_ALIASES@': '',
            '@DOXYGEN_SOURCE_BROWSER@': 'YES',
            '@DOXYGEN_INLINE_SOURCES@': 'NO',
            '@DOXYGEN_VERBATIM_HEADERS@': 'YES',
            '@DOXYGEN_CLANG_ASSISTED_PARSING@': 'YES',
            '@DOXYGEN_CLANG_OPTIONS@': '-std=c++17',
            '@DOXYGEN_HTML_COLORSTYLE_HUE@': '220',
            '@DOXYGEN_HTML_COLORSTYLE_SAT@': '100',
            '@DOXYGEN_HTML_COLORSTYLE_GAMMA@': '80',
            '@DOXYGEN_HTML_TIMESTAMP@': 'YES',
            '@DOXYGEN_HTML_DYNAMIC_SECTIONS@': 'YES',
            '@DOXYGEN_HTML_INDEX_NUM_ENTRIES@': '100',
            '@DOXYGEN_SEARCHENGINE@': 'YES',
            '@DOXYGEN_SERVER_BASED_SEARCH@': 'NO',
            '@DOXYGEN_HAVE_DOT@': 'YES',
            '@DOXYGEN_CLASS_DIAGRAMS@': 'YES',
            '@DOXYGEN_COLLABORATION_GRAPH@': 'YES',
            '@DOXYGEN_GROUP_GRAPHS@': 'YES',
            '@DOXYGEN_UML_LOOK@': 'YES',
            '@DOXYGEN_UML_LIMIT_NUM_FIELDS@': '50',
            '@DOXYGEN_TEMPLATE_RELATIONS@': 'YES',
            '@DOXYGEN_INCLUDE_GRAPH@': 'YES',
            '@DOXYGEN_INCLUDED_BY_GRAPH@': 'YES',
            '@DOXYGEN_CALL_GRAPH@': 'NO',
            '@DOXYGEN_CALLER_GRAPH@': 'NO',
            '@DOXYGEN_GRAPHICAL_HIERARCHY@': 'YES',
            '@DOXYGEN_DIRECTORY_GRAPH@': 'YES',
            '@DOXYGEN_DOT_IMAGE_FORMAT@': 'svg',
            '@DOXYGEN_INTERACTIVE_SVG@': 'YES',
            '@DOXYGEN_DOT_GRAPH_MAX_NODES@': '50',
            '@DOXYGEN_DOT_TRANSPARENT@': 'YES'
        }
        
        for placeholder, value in substitutions.items():
            content = content.replace(placeholder, value)
        
        # Write the configured Doxyfile
        with open(doxyfile_output, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✓ Doxygen configuration generated: {doxyfile_output}")
        return doxyfile_output
    
    def generate_api_docs(self, doxyfile: Path) -> bool:
        """Generate API documentation using Doxygen."""
        print("Generating API documentation...")
        
        try:
            # Run Doxygen
            result = subprocess.run(
                ['doxygen', str(doxyfile)],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                check=True
            )
            
            print("✓ API documentation generated successfully")
            
            # Check if warnings were generated
            if result.stderr:
                print("Doxygen warnings:")
                print(result.stderr)
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"✗ Doxygen failed with exit code {e.returncode}")
            print("Error output:")
            print(e.stderr)
            return False
    
    def copy_user_docs(self):
        """Copy and organize user documentation."""
        print("Copying user documentation...")
        
        user_docs = [
            ("README.md", "index.md"),
            ("docs/USER_MANUAL.md", "user-manual.md"),
            ("docs/INSTALLATION_GUIDE.md", "installation.md"),
            ("docs/DEVELOPER_GUIDE.md", "developer-guide.md"),
            ("docs/RIBBON_INTERFACE_GUIDE.md", "ribbon-interface.md"),
            ("CONTRIBUTING.md", "contributing.md"),
            ("LICENSE", "license.txt")
        ]
        
        user_output_dir = self.output_dir / "user"
        
        for src_file, dest_file in user_docs:
            src_path = self.project_root / src_file
            dest_path = user_output_dir / dest_file
            
            if src_path.exists():
                shutil.copy2(src_path, dest_path)
                print(f"✓ Copied {src_file} -> {dest_file}")
            else:
                print(f"⚠ Warning: {src_file} not found")
    
    def generate_index_page(self):
        """Generate a main index page that links to all documentation."""
        print("Generating documentation index...")
        
        index_content = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Optimized PDF Viewer - Documentation</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 2rem; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 3rem; }
        .header h1 { color: #1779c4; margin-bottom: 0.5rem; }
        .header p { color: #6c757d; font-size: 1.1rem; }
        .docs-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; }
        .doc-card { background: white; border-radius: 8px; padding: 2rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .doc-card h2 { color: #1779c4; margin-top: 0; }
        .doc-card p { color: #6c757d; margin-bottom: 1.5rem; }
        .doc-card a { display: inline-block; background: #1779c4; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 4px; }
        .doc-card a:hover { background: #0056b3; }
        .footer { text-align: center; margin-top: 3rem; color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Optimized PDF Viewer</h1>
            <p>High-performance PDF viewer with advanced annotation capabilities</p>
        </div>
        
        <div class="docs-grid">
            <div class="doc-card">
                <h2>📚 API Documentation</h2>
                <p>Complete API reference with class documentation, method signatures, and code examples.</p>
                <a href="api/html/index.html">Browse API Docs</a>
            </div>
            
            <div class="doc-card">
                <h2>👤 User Manual</h2>
                <p>User-friendly guides for installation, configuration, and daily usage of the PDF viewer.</p>
                <a href="user/user-manual.html">Read User Manual</a>
            </div>
            
            <div class="doc-card">
                <h2>🔧 Developer Guide</h2>
                <p>Technical documentation for developers including architecture, build system, and contribution guidelines.</p>
                <a href="user/developer-guide.html">View Developer Guide</a>
            </div>
            
            <div class="doc-card">
                <h2>🚀 Installation Guide</h2>
                <p>Step-by-step instructions for building and installing the PDF viewer on different platforms.</p>
                <a href="user/installation.html">Installation Instructions</a>
            </div>
        </div>
        
        <div class="footer">
            <p>Generated on """ + str(subprocess.check_output(['date'], text=True).strip()) + """</p>
        </div>
    </div>
</body>
</html>"""
        
        index_path = self.output_dir / "index.html"
        with open(index_path, 'w', encoding='utf-8') as f:
            f.write(index_content)
        
        print(f"✓ Documentation index generated: {index_path}")
    
    def validate_documentation(self) -> bool:
        """Validate generated documentation for common issues."""
        print("Validating documentation...")
        
        issues = []
        
        # Check if API documentation was generated
        api_index = self.output_dir / "api" / "html" / "index.html"
        if not api_index.exists():
            issues.append("API documentation index not found")
        
        # Check for broken internal links (basic check)
        # This is a simplified check - a full link checker would be more comprehensive
        
        if issues:
            print("✗ Documentation validation failed:")
            for issue in issues:
                print(f"  - {issue}")
            return False
        else:
            print("✓ Documentation validation passed")
            return True
    
    def generate_all(self, skip_api: bool = False) -> bool:
        """Generate all documentation."""
        print("Starting documentation generation...")
        print(f"Project root: {self.project_root}")
        print(f"Output directory: {self.output_dir}")
        
        # Setup
        if not self.check_dependencies():
            return False
        
        self.setup_directories()
        
        # Generate API documentation
        if not skip_api:
            doxyfile = self.generate_doxygen_config()
            if not self.generate_api_docs(doxyfile):
                return False
        
        # Copy user documentation
        self.copy_user_docs()
        
        # Generate index page
        self.generate_index_page()
        
        # Validate
        if not self.validate_documentation():
            return False
        
        print(f"\n✓ Documentation generation completed successfully!")
        print(f"📖 Open {self.output_dir / 'index.html'} to view the documentation")
        
        return True

def main():
    parser = argparse.ArgumentParser(description="Generate documentation for Optimized PDF Viewer")
    parser.add_argument("--project-root", default=".", help="Project root directory")
    parser.add_argument("--skip-api", action="store_true", help="Skip API documentation generation")
    parser.add_argument("--clean", action="store_true", help="Clean output directory before generation")
    
    args = parser.parse_args()
    
    generator = DocumentationGenerator(args.project_root)
    
    if args.clean and generator.output_dir.exists():
        print(f"Cleaning output directory: {generator.output_dir}")
        shutil.rmtree(generator.output_dir)
    
    success = generator.generate_all(skip_api=args.skip_api)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
