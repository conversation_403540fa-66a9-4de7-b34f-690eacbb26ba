/**
 * @file PdfController.h
 * @brief High-performance PDF processing and rendering engine
 * <AUTHOR> Viewer Team
 * @date 2024-12-01
 * @version 1.0.0
 *
 * This file contains the PdfController class which handles all PDF document
 * processing, rendering, caching, and search operations with optimized
 * performance for large documents and memory-constrained environments.
 */

#ifndef PDFCONTROLLER_H
#define PDFCONTROLLER_H

#include <QObject>
#include <QThreadPool>
#include <QCache>
#include <QPixmap>
#include <QRectF>
#include <QMutex>
#include <QAtomicInt>
#include <memory>
#include <optional>

class AnnotationManager;
namespace Poppler {
    class Document;
    class Page;
}

/**
 * @brief Search result structure containing match information
 *
 * Represents a single search result within a PDF document, including
 * location information and context for display purposes.
 */
struct SearchResult {
    int pageNumber;      ///< Zero-based page number containing the match
    QRectF boundingBox;  ///< Bounding rectangle of the matched text
    QString context;     ///< Surrounding text context for preview
    int startIndex;      ///< Character index where match starts in context
    int length;          ///< Length of the matched text
};

/**
 * @brief Document outline/bookmark item structure
 *
 * Represents a hierarchical outline item (bookmark) in the PDF document
 * with support for nested structures and navigation.
 */
struct OutlineItem {
    QString title;                  ///< Display title of the outline item
    int pageNumber;                 ///< Target page number (zero-based)
    int level;                      ///< Nesting level in the hierarchy
    QList<OutlineItem> children;    ///< Child outline items
    bool isOpen;                    ///< Whether the item is expanded in UI

    /**
     * @brief Default constructor
     *
     * Initializes an outline item with default values.
     */
    OutlineItem() : pageNumber(-1), level(0), isOpen(false) {}
};

/**
 * @brief High-performance PDF processing and rendering controller
 *
 * The PdfController class provides comprehensive PDF document processing
 * capabilities including asynchronous rendering, intelligent caching,
 * full-text search, and outline extraction. It's designed for optimal
 * performance with large documents and memory efficiency.
 *
 * @details
 * Key features include:
 * - **Asynchronous Rendering**: Non-blocking page rendering with priority queuing
 * - **Intelligent Caching**: LRU cache with memory pressure awareness
 * - **Multi-threaded Operations**: Parallel processing for search and rendering
 * - **Memory Management**: Automatic cache cleanup and memory monitoring
 * - **Search Capabilities**: Full-text search with regex and case sensitivity
 * - **Outline Support**: Hierarchical bookmark extraction and navigation
 * - **Performance Monitoring**: Real-time memory usage and cache statistics
 *
 * The controller uses Poppler-Qt6 as the underlying PDF engine and provides
 * a high-level, Qt-friendly interface with signals and slots for integration
 * with UI components.
 *
 * @example Basic usage:
 * @code
 * PdfController controller;
 *
 * // Load a document
 * if (controller.loadDocument("document.pdf")) {
 *     // Get page count
 *     auto pageCount = controller.pageCount();
 *     if (pageCount) {
 *         qDebug() << "Document has" << *pageCount << "pages";
 *     }
 *
 *     // Request page rendering
 *     controller.requestPage(0, 150.0); // Page 0 at 150% zoom
 * }
 * @endcode
 *
 * @example Advanced search:
 * @code
 * PdfController controller;
 * controller.loadDocument("document.pdf");
 *
 * // Connect to search results
 * connect(&controller, &PdfController::searchCompleted,
 *         [](const QList<SearchResult>& results) {
 *             qDebug() << "Found" << results.size() << "matches";
 *         });
 *
 * // Perform case-sensitive search
 * controller.searchText("Qt Framework", true, false);
 * @endcode
 *
 * @note This class is thread-safe for most operations, but document loading
 *       and some configuration changes should be performed on the main thread.
 *
 * @warning Always check return values and connect to error signals for
 *          robust error handling, especially when working with large or
 *          potentially corrupted PDF files.
 *
 * @see DocumentTab, AnnotationManager, SearchResult, OutlineItem
 * @since 1.0.0
 */
class PdfController : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Constructs a new PdfController instance
     * @param parent Parent QObject for memory management
     *
     * Initializes the PDF controller with default settings, sets up the
     * thread pool for asynchronous operations, and configures the page cache
     * with reasonable defaults based on available system memory.
     *
     * The constructor automatically detects system capabilities and optimizes
     * settings for the current hardware configuration.
     */
    explicit PdfController(QObject *parent = nullptr);

    /**
     * @brief Destructor
     *
     * Cleans up all resources including:
     * - Closing the current document
     * - Clearing all caches
     * - Stopping background threads
     * - Releasing Poppler resources
     */
    ~PdfController();

    /**
     * @brief Gets the total number of pages in the current document
     * @return Optional containing page count, or std::nullopt if no document is loaded
     *
     * Returns the total number of pages in the currently loaded document.
     * This operation is fast as the page count is cached after document loading.
     *
     * @note Page numbers are zero-based throughout the API
     *
     * @see loadDocument(), isDocumentLoaded()
     * @since 1.0.0
     */
    [[nodiscard]] std::optional<int> pageCount() const;

    /**
     * @brief Gets the size of a specific page
     * @param pageNum Zero-based page number
     * @return Size of the page in points, or invalid QSizeF if page doesn't exist
     *
     * Returns the dimensions of the specified page in PDF points (1/72 inch).
     * This information is useful for calculating zoom levels and layout.
     *
     * @note Page sizes may vary within a single document
     *
     * @see pageCount(), requestPage()
     * @since 1.0.0
     */
    [[nodiscard]] QSizeF getPageSize(int pageNum) const;

    /**
     * @brief Gets current memory usage of the page cache
     * @return Memory usage in bytes
     *
     * Returns the current memory consumption of the page cache, including
     * rendered pixmaps and associated metadata. Useful for monitoring
     * memory usage and implementing memory pressure handling.
     *
     * @see getCachedPagesCount(), setCacheSize()
     * @since 1.0.0
     */
    [[nodiscard]] qint64 getCacheMemoryUsage() const;

    /**
     * @brief Gets the number of pages currently cached
     * @return Number of cached pages
     *
     * Returns the count of pages that are currently stored in the cache.
     * This includes both high-quality and preview-quality renderings.
     *
     * @see getCacheMemoryUsage(), clearCache()
     * @since 1.0.0
     */
    [[nodiscard]] int getCachedPagesCount() const;
    [[nodiscard]] int getActiveRenderTasks() const;
    [[nodiscard]] QString getPageText(int pageNum) const;
    [[nodiscard]] QList<SearchResult> searchText(const QString& searchTerm, bool caseSensitive = false, bool wholeWords = false) const;
    [[nodiscard]] QList<OutlineItem> getDocumentOutline() const;
    [[nodiscard]] double getEffectiveDpi(double zoomFactor, double devicePixelRatio = 1.0) const;
    [[nodiscard]] QImage renderPageToImage(int pageNum, double dpi = 150.0, int rotation = 0) const;

    // Annotation support
    void setAnnotationManager(AnnotationManager* manager) { m_annotationManager = manager; }
    AnnotationManager* getAnnotationManager() const { return m_annotationManager; }

public slots:
    void loadDocument(const QString& filePath);
    void closeDocument(); // Close and reset the current document
    void requestPage(int pageNum, double zoomFactor, int rotation = 0);
    void requestPageWithHighlights(int pageNum, double zoomFactor, const QList<QRectF>& highlights, int rotation = 0);
    void requestPageWithAnnotations(int pageNum, double zoomFactor, int rotation = 0);
    void requestPreview(int pageNum); // Request a preview of the given page
    void preloadAdjacentPages(int currentPage, double zoomFactor, int rotation = 0);
    void clearCache();
    void stopAllOperations(); // Stop all ongoing operations safely
    void performSearch(const QString& searchTerm, bool caseSensitive = false, bool wholeWords = false);

signals:
    // Signals to the MainWindow
    void documentLoaded(bool success, const QString& errorString);
    void loadingProgress(int percentage, const QString& message);
    void pageReady(int pageNum, const QPixmap& pixmap, bool isHighQuality);
    void pageWithAnnotationsReady(int pageNum, const QPixmap& pixmap);
    void previewCacheProgress(int pagesRendered, int totalPages);
    void memoryUsageChanged(qint64 bytesUsed, int cachedPages);
    void searchCompleted(const QList<SearchResult>& results);
    void searchProgress(int pagesSearched, int totalPages);
    void outlineReady(const QList<OutlineItem>& outline);

private:
    void renderPageTask(int pageNum, double zoomFactor, bool isPreview, int rotation = 0);
    void renderPageWithHighlightsTask(int pageNum, double zoomFactor, const QList<QRectF>& highlights, int rotation = 0);
    void renderPageWithAnnotationsTask(int pageNum, double zoomFactor, int rotation = 0);
    void generatePreviewCache();
    QString generateCacheKey(int pageNum, double zoomFactor, int rotation = 0) const;
    int getRotationEnum(int degrees) const;
    void performSmartCleanup();
    QPixmap addHighlightsToPixmap(const QPixmap& originalPixmap, const QList<QRectF>& highlights, double dpi) const;
    QImage applySharpeningFilter(const QImage& image) const;
    bool isWholeWordMatch(const QString& text, int position, int length) const;

    // Performance optimization methods
    double getAdaptiveDpi(double zoomFactor) const;
    void optimizeRenderingSettings(Poppler::Page* page, double zoomFactor) const;
    bool shouldUseProgressiveRendering(double zoomFactor) const;
    double calculateCachePriority(const QString& cacheKey, double zoomFactor) const;

    std::unique_ptr<Poppler::Document> m_pdfDocument;
    QThreadPool m_threadPool;
    QThreadPool m_previewThreadPool;            // Separate thread pool for preview generation
    QCache<QString, QPixmap> m_pageCache;       // For high-quality pages (key: "pageNum_zoomFactor")
    QCache<int, QPixmap> m_previewCache;        // For low-quality previews

    // Thread synchronization
    mutable QMutex m_documentMutex;             // Protects m_pdfDocument access
    mutable QMutex m_cacheMutex;                // Protects cache operations
    QAtomicInt m_activeRenderTasks;             // Track active rendering tasks

    // Annotation support
    AnnotationManager* m_annotationManager = nullptr;

    // Constants for rendering - improved DPI for better quality
    const double m_defaultDpi = 150.0;  // Base DPI for standard displays
    const double m_previewDpi = 72.0;   // Improved preview DPI for better thumbnails
    const double m_maxDpi = 600.0;      // Increased maximum DPI for high-resolution displays

    // Performance settings
    const int m_preloadRange = 2;       // Number of pages to preload ahead/behind
    const qint64 m_maxCacheSize = 500 * 1024 * 1024; // 500 MB total cache limit
};

#endif // PDFCONTROLLER_H
