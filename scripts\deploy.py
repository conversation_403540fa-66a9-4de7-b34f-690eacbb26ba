#!/usr/bin/env python3
"""
PDF Viewer Deployment Script
Automates building and packaging for multiple platforms
"""

import os
import sys
import subprocess
import shutil
import platform
import argparse
from pathlib import Path

class DeploymentManager:
    def __init__(self):
        self.script_dir = Path(__file__).parent
        self.project_root = self.script_dir.parent
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        self.platform = platform.system().lower()
        
    def clean_build(self):
        """Clean previous build artifacts"""
        print("🧹 Cleaning build directory...")
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
        
        self.build_dir.mkdir(exist_ok=True)
        self.dist_dir.mkdir(exist_ok=True)
        
    def configure_cmake(self, build_type="Release", static=False):
        """Configure CMake build"""
        print(f"⚙️  Configuring CMake ({build_type})...")
        
        cmake_args = [
            "cmake",
            str(self.project_root),
            f"-DCMAKE_BUILD_TYPE={build_type}",
            "-DBUILD_TESTS=OFF",
            "-DENABLE_LTO=ON"
        ]
        
        if static:
            cmake_args.append("-DSTATIC_BUILD=ON")
            
        if self.platform == "windows":
            cmake_args.extend(["-G", "Visual Studio 16 2019", "-A", "x64"])
        elif self.platform == "darwin":
            cmake_args.append("-DCMAKE_OSX_DEPLOYMENT_TARGET=10.15")
            
        subprocess.run(cmake_args, cwd=self.build_dir, check=True)
        
    def build_application(self, config="Release"):
        """Build the application"""
        print("🔨 Building application...")
        
        build_args = ["cmake", "--build", ".", "--config", config]
        
        if self.platform != "windows":
            build_args.extend(["-j", str(os.cpu_count())])
            
        subprocess.run(build_args, cwd=self.build_dir, check=True)
        
    def run_tests(self):
        """Run unit tests"""
        print("🧪 Running tests...")
        
        test_args = ["ctest", "--output-on-failure", "--parallel", str(os.cpu_count())]
        result = subprocess.run(test_args, cwd=self.build_dir)
        
        if result.returncode != 0:
            print("⚠️  Some tests failed, but continuing with deployment...")
            
    def package_windows(self):
        """Package for Windows"""
        print("📦 Packaging for Windows...")
        
        # Find executable
        exe_path = self.build_dir / "Release" / "optimized-pdf-viewer.exe"
        if not exe_path.exists():
            exe_path = self.build_dir / "optimized-pdf-viewer.exe"
            
        if not exe_path.exists():
            raise FileNotFoundError("Could not find executable")
            
        # Create distribution directory
        app_dir = self.dist_dir / "PDF-Viewer"
        app_dir.mkdir(exist_ok=True)
        
        # Copy executable
        shutil.copy2(exe_path, app_dir)
        
        # Deploy Qt dependencies
        self.deploy_qt_windows(app_dir)
        
        # Copy resources
        self.copy_resources(app_dir)
        
        # Create installer
        self.create_windows_installer(app_dir)
        
        # Create portable zip
        self.create_portable_zip(app_dir)
        
    def deploy_qt_windows(self, app_dir):
        """Deploy Qt dependencies on Windows"""
        print("📚 Deploying Qt dependencies...")
        
        windeployqt = shutil.which("windeployqt")
        if not windeployqt:
            print("⚠️  windeployqt not found, manual DLL copying required")
            return
            
        subprocess.run([
            windeployqt,
            "--release",
            "--no-translations",
            "--no-system-d3d-compiler",
            str(app_dir / "optimized-pdf-viewer.exe")
        ], check=True)
        
    def package_macos(self):
        """Package for macOS"""
        print("📦 Packaging for macOS...")
        
        # Find app bundle
        app_bundle = self.build_dir / "optimized-pdf-viewer.app"
        if not app_bundle.exists():
            raise FileNotFoundError("Could not find app bundle")
            
        # Copy to distribution
        dist_app = self.dist_dir / "PDF Viewer.app"
        shutil.copytree(app_bundle, dist_app)
        
        # Deploy Qt dependencies
        self.deploy_qt_macos(dist_app)
        
        # Copy resources
        self.copy_resources(dist_app / "Contents" / "Resources")
        
        # Create DMG
        self.create_dmg(dist_app)
        
    def deploy_qt_macos(self, app_bundle):
        """Deploy Qt dependencies on macOS"""
        print("📚 Deploying Qt dependencies...")
        
        macdeployqt = shutil.which("macdeployqt")
        if not macdeployqt:
            print("⚠️  macdeployqt not found, manual framework copying required")
            return
            
        subprocess.run([
            macdeployqt,
            str(app_bundle),
            "-always-overwrite"
        ], check=True)
        
    def package_linux(self):
        """Package for Linux"""
        print("📦 Packaging for Linux...")
        
        # Find executable
        exe_path = self.build_dir / "optimized-pdf-viewer"
        if not exe_path.exists():
            raise FileNotFoundError("Could not find executable")
            
        # Create AppDir structure
        appdir = self.dist_dir / "PDF-Viewer.AppDir"
        appdir.mkdir(exist_ok=True)
        
        # Copy executable
        (appdir / "usr" / "bin").mkdir(parents=True, exist_ok=True)
        shutil.copy2(exe_path, appdir / "usr" / "bin")
        
        # Copy resources
        self.copy_resources(appdir / "usr" / "share" / "pdf-viewer")
        
        # Create desktop file
        self.create_desktop_file(appdir)
        
        # Create AppImage
        self.create_appimage(appdir)
        
        # Create DEB package
        self.create_deb_package()
        
    def copy_resources(self, target_dir):
        """Copy application resources"""
        print("📄 Copying resources...")
        
        target_dir.mkdir(parents=True, exist_ok=True)
        
        # Copy documentation
        docs_dir = target_dir / "docs"
        if (self.project_root / "docs").exists():
            shutil.copytree(self.project_root / "docs", docs_dir)
            
        # Copy license
        if (self.project_root / "LICENSE").exists():
            shutil.copy2(self.project_root / "LICENSE", target_dir)
            
        # Copy README
        if (self.project_root / "README.md").exists():
            shutil.copy2(self.project_root / "README.md", target_dir)
            
    def create_windows_installer(self, app_dir):
        """Create Windows NSIS installer"""
        print("🎁 Creating Windows installer...")
        
        nsis_script = self.script_dir / "installer.nsi"
        if not nsis_script.exists():
            print("⚠️  NSIS script not found, skipping installer creation")
            return
            
        makensis = shutil.which("makensis")
        if not makensis:
            print("⚠️  NSIS not found, skipping installer creation")
            return
            
        subprocess.run([
            makensis,
            f"/DAPP_DIR={app_dir}",
            f"/DOUTPUT_DIR={self.dist_dir}",
            str(nsis_script)
        ], check=True)
        
    def create_portable_zip(self, app_dir):
        """Create portable ZIP package"""
        print("📦 Creating portable ZIP...")
        
        zip_path = self.dist_dir / "PDF-Viewer-Portable-x64.zip"
        shutil.make_archive(
            str(zip_path.with_suffix("")),
            "zip",
            str(app_dir.parent),
            app_dir.name
        )
        
    def create_dmg(self, app_bundle):
        """Create macOS DMG"""
        print("💿 Creating DMG...")
        
        dmg_path = self.dist_dir / "PDF-Viewer-macOS.dmg"
        
        subprocess.run([
            "hdiutil", "create",
            "-volname", "PDF Viewer",
            "-srcfolder", str(app_bundle),
            "-ov", "-format", "UDZO",
            str(dmg_path)
        ], check=True)
        
    def create_appimage(self, appdir):
        """Create Linux AppImage"""
        print("📱 Creating AppImage...")
        
        appimagetool = shutil.which("appimagetool")
        if not appimagetool:
            print("⚠️  appimagetool not found, skipping AppImage creation")
            return
            
        subprocess.run([
            appimagetool,
            str(appdir),
            str(self.dist_dir / "PDF-Viewer-x86_64.AppImage")
        ], check=True)
        
    def create_deb_package(self):
        """Create Debian package"""
        print("📦 Creating DEB package...")
        
        # This would require a more complex implementation
        # with proper debian/ directory structure
        print("⚠️  DEB package creation not implemented yet")
        
    def create_desktop_file(self, appdir):
        """Create Linux desktop file"""
        desktop_content = """[Desktop Entry]
Name=PDF Viewer
Comment=Modern PDF viewer with Microsoft Office-style interface
Exec=optimized-pdf-viewer
Icon=pdf-viewer
Type=Application
Categories=Office;Viewer;
MimeType=application/pdf;
"""
        
        desktop_file = appdir / "pdf-viewer.desktop"
        desktop_file.write_text(desktop_content)
        
        # Make AppRun executable
        apprun = appdir / "AppRun"
        apprun.write_text("#!/bin/bash\nexec \"$APPDIR/usr/bin/optimized-pdf-viewer\" \"$@\"\n")
        apprun.chmod(0o755)
        
    def deploy(self, build_type="Release", static=False, run_tests=False):
        """Main deployment function"""
        print(f"🚀 Starting deployment for {self.platform}...")
        
        try:
            self.clean_build()
            self.configure_cmake(build_type, static)
            self.build_application(build_type)
            
            if run_tests:
                self.run_tests()
                
            if self.platform == "windows":
                self.package_windows()
            elif self.platform == "darwin":
                self.package_macos()
            elif self.platform == "linux":
                self.package_linux()
            else:
                raise ValueError(f"Unsupported platform: {self.platform}")
                
            print("✅ Deployment completed successfully!")
            print(f"📁 Packages available in: {self.dist_dir}")
            
        except Exception as e:
            print(f"❌ Deployment failed: {e}")
            sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description="PDF Viewer Deployment Script")
    parser.add_argument("--build-type", choices=["Debug", "Release"], 
                       default="Release", help="Build configuration")
    parser.add_argument("--static", action="store_true", 
                       help="Create static build")
    parser.add_argument("--test", action="store_true", 
                       help="Run tests before packaging")
    parser.add_argument("--clean", action="store_true", 
                       help="Clean build directory only")
    
    args = parser.parse_args()
    
    deployer = DeploymentManager()
    
    if args.clean:
        deployer.clean_build()
        print("✅ Build directory cleaned")
        return
        
    deployer.deploy(args.build_type, args.static, args.test)

if __name__ == "__main__":
    main()
