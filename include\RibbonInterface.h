#ifndef RIBBONINTERFACE_H
#define RIBBONINTERFACE_H

#include "ElaIntegration.h"
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QStackedWidget>
#include <QButtonGroup>
#include <QMap>
#include <QList>

class MainWindow;

class RibbonInterface : public QWidget
{
    Q_OBJECT

public:
    explicit RibbonInterface(MainWindow* parent = nullptr);
    ~RibbonInterface();

    // Ribbon sections
    void createHomeSection();
    void createFileSection();
    void createViewSection();
    void createToolsSection();
    void createAnnotateSection();
    void createReviewSection();
    
    // Section management
    void showSection(const QString& sectionName);
    void hideSection(const QString& sectionName);
    void setCurrentSection(const QString& sectionName);
    QString getCurrentSection() const;
    
    // Responsive design
    void setCompactMode(bool compact);
    bool isCompactMode() const;

    // Dynamic content
    void updateDocumentActions(bool hasDocument);
    void updatePageActions(int currentPage, int totalPages);
    void updateZoomActions(double zoomFactor);
    void updateAnnotationActions(bool annotationsEnabled);

signals:
    void sectionChanged(const QString& sectionName);
    void actionTriggered(const QString& actionName);

protected:
    void paintEvent(QPaintEvent* event) override;
    void resizeEvent(QResizeEvent* event) override;

private slots:
    void onSectionButtonClicked();
    void onQuickAccessClicked();

private:
    void setupUI();
    void createSectionTabs();
    void createQuickAccessToolbar();
    void createRibbonGroups();
    void updateSectionVisibility();
    void styleRibbonButton(ElaPushButton* button, const QString& text, 
                          ElaIconType::IconName icon, const QString& tooltip = QString());
    
    // UI Components
    MainWindow* m_mainWindow;
    QVBoxLayout* m_mainLayout;
    QHBoxLayout* m_tabLayout;
    QStackedWidget* m_contentStack;
    
    // Section tabs
    QButtonGroup* m_sectionButtonGroup;
    QMap<QString, ElaPushButton*> m_sectionButtons;
    QMap<QString, QWidget*> m_sectionWidgets;
    
    // Quick Access Toolbar
    QHBoxLayout* m_quickAccessLayout;
    QList<ElaPushButton*> m_quickAccessButtons;
    
    // Ribbon groups for each section
    struct RibbonGroupData {
        QString title;
        QWidget* widget;
        QVBoxLayout* layout;
        QHBoxLayout* buttonLayout;
        ElaText* titleLabel;
        QList<ElaPushButton*> buttons;
    };

    QMap<QString, QList<RibbonGroupData*>> m_ribbonGroups;
    
    // State
    QString m_currentSection;
    bool m_hasDocument;
    int m_currentPage;
    int m_totalPages;
    double m_zoomFactor;
    bool m_compactMode;
    
    // Styling constants
    static const int RIBBON_HEIGHT = 120;
    static const int TAB_HEIGHT = 30;
    static const int GROUP_SPACING = 15;
    static const int BUTTON_SIZE = 32;
    static const int LARGE_BUTTON_SIZE = 48;
};

// Ribbon group helper class
class RibbonGroup : public QWidget
{
    Q_OBJECT

public:
    explicit RibbonGroup(const QString& title, QWidget* parent = nullptr);
    
    // Button management
    ElaPushButton* addLargeButton(const QString& text, ElaIconType::IconName icon, 
                                 const QString& tooltip = QString());
    ElaPushButton* addSmallButton(const QString& text, ElaIconType::IconName icon, 
                                 const QString& tooltip = QString());
    ElaPushButton* addSplitButton(const QString& text, ElaIconType::IconName icon,
                                 ElaMenu* menu, const QString& tooltip = QString());
    
    void addSeparator();
    void setGroupEnabled(bool enabled);

protected:
    void paintEvent(QPaintEvent* event) override;

private:
    void setupUI();
    
    QString m_title;
    QVBoxLayout* m_mainLayout;
    QHBoxLayout* m_buttonLayout;
    ElaText* m_titleLabel;
    QList<ElaPushButton*> m_buttons;
};

#endif // RIBBONINTERFACE_H
