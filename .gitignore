# Build directories
build/
build-*/
debug/
release/
Debug/
Release/
x64/
x86/
Win32/

# CMake generated files
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
CTestTestfile.cmake
Makefile
*.cmake
!CMakeLists.txt

# Qt generated files
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
*.moc
*.moc.cpp
moc_*.cpp
moc_*.h
qrc_*.cpp
ui_*.h
*.qm
*.prl

# Qt Creator
*.autosave
*.user
*.user.*

# Visual Studio / Visual Studio Code
.vs/
.vscode/
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
*.sln
*.suo
*.user
*.userosscache
*.sln.docstates

# Compiled Object files
*.o
*.obj
*.elf
*.ko
*.so
*.dylib
*.dll
*.exe
*.out
*.app

# Precompiled Headers
*.gch
*.pch

# Libraries
*.lib
*.a
*.la
*.lo

# Shared objects (inc. Windows DLLs)
*.so.*
*.dylib

# Executables
*.exe
*.out
*.app
*.i*86
*.x86_64
*.hex

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Kernel Module Compile Results
*.mod*
*.cmd
.tmp_versions/
modules.order
Module.symvers
Mkfile.old
dkms.conf

# IDE files
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Temporary files
*.tmp
*.temp
*.log
*.bak
*.backup
*.orig

# Package managers
node_modules/
.npm
.yarn/
.pnp.*

# Test results
test-results/
coverage/
*.gcov
*.gcda
*.gcno

# Documentation build
docs/_build/
docs/html/
docs/latex/

# Dependency directories
third_party/
external/
vendor/

# Local configuration
.env
.env.local
config.local.*

# Profiling data
*.prof
gmon.out

# Core dumps
core
core.*

# Valgrind
*.memcheck
*.helgrind
*.drd
*.massif

# Clang tools
.clang_complete
compile_commands.json

# Conan
conanfile.txt.user
conaninfo.txt
conanbuildinfo.*
conan.lock

# vcpkg
vcpkg_installed/
.vcpkg-root

# Custom project specific
*.pdf
test_files/
sample_data/
.cache/