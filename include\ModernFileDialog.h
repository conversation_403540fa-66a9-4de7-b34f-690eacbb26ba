#ifndef MODERNFILEDIALOG_H
#define MODERNFILEDIALOG_H

#include "ElaIntegration.h"
#include <QFileSystemModel>
#include <QCompleter>

class ModernFileDialog : public ElaContentDialog
{
    Q_OBJECT

public:
    enum FileMode {
        AnyFile,
        ExistingFile,
        ExistingFiles,
        Directory
    };

    explicit ModernFileDialog(QWidget *parent = nullptr, FileMode mode = ExistingFile);
    ~ModernFileDialog();

    // Static convenience methods
    static QString getOpenFileName(QWidget *parent = nullptr,
                                  const QString &caption = QString(),
                                  const QString &dir = QString(),
                                  const QString &filter = QString());
    
    static QStringList getOpenFileNames(QWidget *parent = nullptr,
                                       const QString &caption = QString(),
                                       const QString &dir = QString(),
                                       const QString &filter = QString());
    
    static QString getSaveFileName(QWidget *parent = nullptr,
                                  const QString &caption = QString(),
                                  const QString &dir = QString(),
                                  const QString &filter = QString());
    
    static QString getExistingDirectory(QWidget *parent = nullptr,
                                       const QString &caption = QString(),
                                       const QString &dir = QString());

    // Configuration
    void setFileMode(FileMode mode);
    FileMode fileMode() const;
    
    void setNameFilter(const QString &filter);
    void setNameFilters(const QStringList &filters);
    QStringList nameFilters() const;
    
    void setDirectory(const QString &directory);
    QString directory() const;
    
    void selectFile(const QString &filename);
    QStringList selectedFiles() const;
    
    // Appearance
    void setViewMode(QFileDialog::ViewMode mode);
    QFileDialog::ViewMode viewMode() const;
    
    void setShowHidden(bool show);
    bool showHidden() const;

signals:
    void fileSelected(const QString &file);
    void filesSelected(const QStringList &files);
    void directoryEntered(const QString &directory);

protected:
    void showEvent(QShowEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private slots:
    void onLocationChanged(const QString &path);
    void onFileActivated(const QModelIndex &index);
    void onFileClicked(const QModelIndex &index);
    void onFilterChanged(const QString &filter);
    void onViewModeChanged();
    void onShowHiddenToggled(bool show);
    void onUpButtonClicked();
    void onHomeButtonClicked();
    void onNewFolderClicked();
    void onRefreshClicked();

private:
    void setupUI();
    void setupToolbar();
    void setupFileView();
    void setupSidebar();
    void setupBottomPanel();
    void updateFileView();
    void updateButtonStates();
    void populateSidebar();
    void addQuickAccess(const QString &name, const QString &path, ElaIconType::IconName icon);
    
    // UI Components
    QHBoxLayout* m_mainLayout;
    QVBoxLayout* m_leftLayout;
    QVBoxLayout* m_rightLayout;
    
    // Toolbar
    QHBoxLayout* m_toolbarLayout;
    ElaPushButton* m_upButton;
    ElaPushButton* m_homeButton;
    ElaPushButton* m_newFolderButton;
    ElaPushButton* m_refreshButton;
    ElaPushButton* m_viewModeButton;
    ElaCheck* m_showHiddenCheck;
    
    // Location bar
    ElaLineEdit* m_locationEdit;
    QCompleter* m_locationCompleter;
    
    // Sidebar
    QWidget* m_sidebar;
    QVBoxLayout* m_sidebarLayout;
    ElaText* m_quickAccessLabel;
    QListWidget* m_quickAccessList;
    ElaText* m_devicesLabel;
    QListWidget* m_devicesList;
    
    // File view
    QTreeView* m_fileView;
    QListView* m_fileListView;
    QFileSystemModel* m_fileModel;
    
    // Bottom panel
    QHBoxLayout* m_bottomLayout;
    ElaText* m_fileNameLabel;
    ElaLineEdit* m_fileNameEdit;
    ElaText* m_filterLabel;
    ElaCombo* m_filterCombo;
    
    // State
    FileMode m_fileMode;
    QFileDialog::ViewMode m_viewMode;
    QString m_currentDirectory;
    QStringList m_nameFilters;
    QString m_selectedFilter;
    QStringList m_selectedFiles;
    bool m_showHidden;
    
    // Constants
    static const int SIDEBAR_WIDTH = 200;
    static const int MIN_WIDTH = 640;
    static const int MIN_HEIGHT = 480;
};

#endif // MODERNFILEDIALOG_H
