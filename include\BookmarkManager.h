#ifndef BOOKMARKMANAGER_H
#define BOOKMARKMANAGER_H

#include <QObject>
#include <QDateTime>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QSettings>
#include <QStringList>
#include <QColor>
#include <QIcon>

/**
 * @brief Enhanced bookmark structure with additional metadata
 */
struct EnhancedBookmark {
    QString id;                    // Unique identifier
    QString name;                  // User-defined name
    QString description;           // Optional description
    QString filePath;              // Document file path
    QString fileName;              // Document file name
    int pageNumber;                // Zero-based page number
    double zoomFactor;             // Zoom level
    int rotation;                  // Page rotation
    QDateTime created;             // Creation timestamp
    QDateTime lastAccessed;        // Last access timestamp
    int accessCount;               // Number of times accessed
    QStringList tags;              // User-defined tags
    QColor color;                  // Bookmark color/category
    QString notes;                 // User notes
    bool isFavorite;               // Favorite flag
    QString category;              // Bookmark category
    QRectF viewportRect;           // Specific viewport area
    
    EnhancedBookmark() 
        : pageNumber(0)
        , zoomFactor(1.0)
        , rotation(0)
        , created(QDateTime::currentDateTime())
        , lastAccessed(QDateTime::currentDateTime())
        , accessCount(0)
        , color(Qt::blue)
        , isFavorite(false) {}
    
    // Serialization
    QJsonObject toJson() const;
    static EnhancedBookmark fromJson(const QJsonObject& json);
    
    // Comparison
    bool operator==(const EnhancedBookmark& other) const {
        return id == other.id;
    }
    
    // Generate unique ID
    void generateId();
    
    // Update access info
    void updateAccess();
};

/**
 * @brief Advanced bookmark management system
 */
class BookmarkManager : public QObject
{
    Q_OBJECT

public:
    enum class SortOrder {
        Name,
        Created,
        LastAccessed,
        AccessCount,
        PageNumber,
        Category
    };
    
    enum class FilterType {
        All,
        Favorites,
        RecentlyAccessed,
        MostAccessed,
        ByCategory,
        ByTag,
        ByDocument
    };

    explicit BookmarkManager(QObject *parent = nullptr);
    ~BookmarkManager();

    // Bookmark management
    QString addBookmark(const EnhancedBookmark& bookmark);
    bool updateBookmark(const QString& id, const EnhancedBookmark& bookmark);
    bool removeBookmark(const QString& id);
    bool removeBookmarksForDocument(const QString& filePath);
    void clearAllBookmarks();
    
    // Retrieval
    EnhancedBookmark getBookmark(const QString& id) const;
    QList<EnhancedBookmark> getAllBookmarks() const;
    QList<EnhancedBookmark> getBookmarksForDocument(const QString& filePath) const;
    QList<EnhancedBookmark> getFavoriteBookmarks() const;
    QList<EnhancedBookmark> getRecentBookmarks(int maxCount = 10) const;
    QList<EnhancedBookmark> getMostAccessedBookmarks(int maxCount = 10) const;
    
    // Search and filtering
    QList<EnhancedBookmark> searchBookmarks(const QString& query) const;
    QList<EnhancedBookmark> filterBookmarks(FilterType filter, const QString& value = QString()) const;
    QList<EnhancedBookmark> getBookmarksByCategory(const QString& category) const;
    QList<EnhancedBookmark> getBookmarksByTag(const QString& tag) const;
    
    // Sorting
    QList<EnhancedBookmark> sortBookmarks(const QList<EnhancedBookmark>& bookmarks, 
                                         SortOrder order, bool ascending = true) const;
    
    // Categories and tags
    QStringList getAllCategories() const;
    QStringList getAllTags() const;
    QStringList getCategoriesForDocument(const QString& filePath) const;
    QStringList getTagsForDocument(const QString& filePath) const;
    
    // Statistics
    int getBookmarkCount() const;
    int getBookmarkCountForDocument(const QString& filePath) const;
    QDateTime getLastBookmarkTime() const;
    QStringList getMostUsedCategories(int maxCount = 5) const;
    QStringList getMostUsedTags(int maxCount = 10) const;
    
    // Favorites
    bool toggleFavorite(const QString& id);
    bool setFavorite(const QString& id, bool favorite);
    
    // Access tracking
    void recordAccess(const QString& id);
    
    // Import/Export
    bool exportBookmarks(const QString& filePath) const;
    bool importBookmarks(const QString& filePath);
    bool exportBookmarksForDocument(const QString& documentPath, const QString& exportPath) const;
    
    // Persistence
    void saveToSettings();
    void loadFromSettings();
    
    // Validation
    bool isValidBookmark(const EnhancedBookmark& bookmark) const;
    bool bookmarkExists(const QString& id) const;
    bool hasBookmarkForPage(const QString& filePath, int pageNumber) const;
    
    // Cleanup
    void removeOrphanedBookmarks();
    void removeOldBookmarks(int daysOld = 365);
    
    // Settings
    void setAutoSaveEnabled(bool enabled);
    bool isAutoSaveEnabled() const;
    void setMaxBookmarks(int maxBookmarks);
    int getMaxBookmarks() const;

signals:
    void bookmarkAdded(const EnhancedBookmark& bookmark);
    void bookmarkUpdated(const EnhancedBookmark& bookmark);
    void bookmarkRemoved(const QString& id);
    void bookmarksCleared();
    void bookmarkAccessed(const QString& id);
    void favoriteToggled(const QString& id, bool isFavorite);

private:
    void trimBookmarks();
    QString generateUniqueId() const;
    void updateBookmarkInList(const EnhancedBookmark& bookmark);
    int findBookmarkIndex(const QString& id) const;
    
    QList<EnhancedBookmark> m_bookmarks;
    QSettings* m_settings;
    bool m_autoSaveEnabled;
    int m_maxBookmarks;
    
    static const int DEFAULT_MAX_BOOKMARKS = 1000;
    static const QString SETTINGS_GROUP;
    static const QString BOOKMARKS_KEY;
};

// Make EnhancedBookmark available to QVariant system
Q_DECLARE_METATYPE(EnhancedBookmark)

#endif // BOOKMARKMANAGER_H
