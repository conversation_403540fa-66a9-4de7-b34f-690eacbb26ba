#include "PerformanceMonitor.h"
#include <QApplication>
#include <QProcess>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QTextStream>
#include <QDebug>
#include <QThread>

#ifdef Q_OS_WIN
#include <windows.h>
#include <psapi.h>
#elif defined(Q_OS_LINUX)
#include <unistd.h>
#include <sys/resource.h>
#elif defined(Q_OS_MAC)
#include <mach/mach.h>
#include <sys/resource.h>
#endif

PerformanceMonitor::PerformanceMonitor(QObject* parent)
    : QObject(parent)
    , m_isMonitoring(false)
    , m_monitoringTimer(new QTimer(this))
    , m_cleanupTimer(new QTimer(this))
    , m_monitoringInterval(1000) // 1 second
    , m_maxHistorySize(1000)
    , m_metricRetentionMinutes(60) // 1 hour
    , m_memoryThreshold(500 * 1024 * 1024) // 500 MB
    , m_cpuThreshold(80.0) // 80%
    , m_maxActiveThreads(8)
{
    initializeMonitoring();
}

PerformanceMonitor::~PerformanceMonitor()
{
    stopMonitoring();
}

void PerformanceMonitor::initializeMonitoring()
{
    // Configure monitoring timer
    m_monitoringTimer->setSingleShot(false);
    connect(m_monitoringTimer, &QTimer::timeout, this, &PerformanceMonitor::updateSystemMetrics);
    connect(m_monitoringTimer, &QTimer::timeout, this, &PerformanceMonitor::checkPerformanceThresholds);
    
    // Configure cleanup timer (every 5 minutes)
    m_cleanupTimer->setSingleShot(false);
    m_cleanupTimer->setInterval(5 * 60 * 1000);
    connect(m_cleanupTimer, &QTimer::timeout, this, &PerformanceMonitor::cleanupOldMetrics);
    
    // Initialize stats
    m_renderingStats.lastUpdate = QDateTime::currentDateTime();
    m_lastOptimizationCheck = QDateTime::currentDateTime();
}

void PerformanceMonitor::startMonitoring()
{
    if (m_isMonitoring) return;
    
    m_isMonitoring = true;
    m_monitoringTimer->start(m_monitoringInterval);
    m_cleanupTimer->start();
    
    // Initial system metrics collection
    updateSystemMetrics();
    
    qDebug() << "Performance monitoring started";
}

void PerformanceMonitor::stopMonitoring()
{
    if (!m_isMonitoring) return;
    
    m_isMonitoring = false;
    m_monitoringTimer->stop();
    m_cleanupTimer->stop();
    
    qDebug() << "Performance monitoring stopped";
}

void PerformanceMonitor::recordMetric(const QString& name, const QString& category, double value, const QString& unit)
{
    QMutexLocker locker(&m_metricsMutex);
    
    PerformanceMetric metric(name, category, value, unit);
    m_metrics.enqueue(metric);
    
    // Maintain size limit
    while (m_metrics.size() > m_maxHistorySize) {
        m_metrics.dequeue();
    }
    
    emit metricsUpdated();
}

void PerformanceMonitor::recordRenderTime(double milliseconds)
{
    recordMetric("RenderTime", "Rendering", milliseconds, "ms");
    
    // Update rendering stats
    m_renderingStats.totalPagesRendered++;
    
    // Calculate rolling average
    double totalTime = m_renderingStats.averageRenderTime * (m_renderingStats.totalPagesRendered - 1) + milliseconds;
    m_renderingStats.averageRenderTime = totalTime / m_renderingStats.totalPagesRendered;
    
    m_renderingStats.lastUpdate = QDateTime::currentDateTime();
    emit renderingPerformanceChanged(m_renderingStats);
}

void PerformanceMonitor::recordCacheHit()
{
    m_renderingStats.cacheHits++;
    recordMetric("CacheHit", "Cache", 1, "count");
}

void PerformanceMonitor::recordCacheMiss()
{
    m_renderingStats.cacheMisses++;
    recordMetric("CacheMiss", "Cache", 1, "count");
}

void PerformanceMonitor::recordMemoryUsage(qint64 bytes)
{
    double megabytes = bytes / (1024.0 * 1024.0);
    recordMetric("MemoryUsage", "Memory", megabytes, "MB");
    
    if (megabytes > m_renderingStats.peakMemoryUsage) {
        m_renderingStats.peakMemoryUsage = megabytes;
    }
    
    if (bytes > m_memoryThreshold) {
        emit memoryThresholdExceeded(bytes, m_memoryThreshold);
    }
}

void PerformanceMonitor::recordActiveThreads(int count)
{
    m_renderingStats.activeThreads = count;
    recordMetric("ActiveThreads", "Threading", count, "count");
}

void PerformanceMonitor::startTimer(const QString& operation)
{
    QMutexLocker locker(&m_metricsMutex);
    m_activeTimers[operation].start();
}

void PerformanceMonitor::endTimer(const QString& operation)
{
    QMutexLocker locker(&m_metricsMutex);
    
    if (m_activeTimers.contains(operation)) {
        qint64 elapsed = m_activeTimers[operation].elapsed();
        m_activeTimers.remove(operation);
        
        recordMetric(operation, "Performance", elapsed, "ms");
    }
}

QList<PerformanceMetric> PerformanceMonitor::getMetrics(const QString& category) const
{
    QMutexLocker locker(&m_metricsMutex);
    
    QList<PerformanceMetric> result;
    for (const auto& metric : m_metrics) {
        if (category.isEmpty() || metric.category == category) {
            result.append(metric);
        }
    }
    
    return result;
}

QList<PerformanceMetric> PerformanceMonitor::getRecentMetrics(int minutes) const
{
    QMutexLocker locker(&m_metricsMutex);
    
    QDateTime cutoff = QDateTime::currentDateTime().addSecs(-minutes * 60);
    QList<PerformanceMetric> result;
    
    for (const auto& metric : m_metrics) {
        if (metric.timestamp >= cutoff) {
            result.append(metric);
        }
    }
    
    return result;
}

RenderingStats PerformanceMonitor::getRenderingStats() const
{
    return m_renderingStats;
}

SystemMetrics PerformanceMonitor::getSystemMetrics() const
{
    return m_lastSystemMetrics;
}

double PerformanceMonitor::getAverageMetric(const QString& name, int minutes) const
{
    QList<PerformanceMetric> recent = getRecentMetrics(minutes);
    
    double sum = 0.0;
    int count = 0;
    
    for (const auto& metric : recent) {
        if (metric.name == name) {
            sum += metric.value;
            count++;
        }
    }
    
    return count > 0 ? sum / count : 0.0;
}

double PerformanceMonitor::getPeakMetric(const QString& name, int minutes) const
{
    QList<PerformanceMetric> recent = getRecentMetrics(minutes);
    
    double peak = 0.0;
    for (const auto& metric : recent) {
        if (metric.name == name && metric.value > peak) {
            peak = metric.value;
        }
    }
    
    return peak;
}

QMap<QString, double> PerformanceMonitor::getCategorySummary() const
{
    QMutexLocker locker(&m_metricsMutex);
    
    QMap<QString, double> summary;
    QMap<QString, int> counts;
    
    for (const auto& metric : m_metrics) {
        summary[metric.category] += metric.value;
        counts[metric.category]++;
    }
    
    // Calculate averages
    for (auto it = summary.begin(); it != summary.end(); ++it) {
        if (counts[it.key()] > 0) {
            it.value() /= counts[it.key()];
        }
    }
    
    return summary;
}

void PerformanceMonitor::updateSystemMetrics()
{
    SystemMetrics metrics;
    metrics.timestamp = QDateTime::currentDateTime();
    metrics.cpuUsage = getCurrentCpuUsage();
    metrics.memoryUsage = getCurrentMemoryUsage();
    metrics.availableMemory = getAvailableMemory();
    metrics.threadCount = getCurrentThreadCount();
    
    m_lastSystemMetrics = metrics;
    m_systemHistory.enqueue(metrics);
    
    // Keep only recent history (last hour)
    while (m_systemHistory.size() > 3600) { // 1 hour at 1-second intervals
        m_systemHistory.dequeue();
    }
    
    // Record as metrics
    recordMetric("CPU", "System", metrics.cpuUsage, "%");
    recordMetric("Memory", "System", metrics.memoryUsage / (1024.0 * 1024.0), "MB");
    recordMetric("Threads", "System", metrics.threadCount, "count");
}

void PerformanceMonitor::checkPerformanceThresholds()
{
    const SystemMetrics& current = m_lastSystemMetrics;
    
    // Check CPU threshold
    if (current.cpuUsage > m_cpuThreshold) {
        emit performanceAlert(
            QString("High CPU usage: %1%").arg(current.cpuUsage, 0, 'f', 1),
            "System"
        );
    }
    
    // Check memory threshold
    if (current.memoryUsage > m_memoryThreshold) {
        emit performanceAlert(
            QString("High memory usage: %1 MB").arg(current.memoryUsage / (1024.0 * 1024.0), 0, 'f', 1),
            "Memory"
        );
    }
    
    // Check thread count
    if (current.threadCount > m_maxActiveThreads) {
        emit performanceAlert(
            QString("High thread count: %1").arg(current.threadCount),
            "Threading"
        );
    }
    
    // Generate optimization recommendations periodically
    if (m_lastOptimizationCheck.secsTo(QDateTime::currentDateTime()) > 60) { // Every minute
        generateRecommendations();
        m_lastOptimizationCheck = QDateTime::currentDateTime();
    }
}

void PerformanceMonitor::cleanupOldMetrics()
{
    QMutexLocker locker(&m_metricsMutex);
    
    QDateTime cutoff = QDateTime::currentDateTime().addSecs(-m_metricRetentionMinutes * 60);
    
    while (!m_metrics.isEmpty() && m_metrics.head().timestamp < cutoff) {
        m_metrics.dequeue();
    }
    
    qDebug() << "Cleaned up old metrics, current count:" << m_metrics.size();
}

QStringList PerformanceMonitor::getOptimizationRecommendations() const
{
    return m_currentRecommendations;
}

bool PerformanceMonitor::shouldOptimizeCache() const
{
    double hitRatio = m_renderingStats.getCacheHitRatio();
    return hitRatio < 70.0; // Less than 70% hit ratio
}

bool PerformanceMonitor::shouldReduceThreads() const
{
    return m_renderingStats.activeThreads > m_maxActiveThreads;
}

bool PerformanceMonitor::shouldClearMemory() const
{
    return m_renderingStats.peakMemoryUsage > (m_memoryThreshold / (1024.0 * 1024.0));
}

void PerformanceMonitor::setMonitoringInterval(int milliseconds)
{
    m_monitoringInterval = milliseconds;
    if (m_isMonitoring) {
        m_monitoringTimer->setInterval(milliseconds);
    }
}

void PerformanceMonitor::setMaxHistorySize(int size)
{
    m_maxHistorySize = size;
}

void PerformanceMonitor::setMetricRetentionTime(int minutes)
{
    m_metricRetentionMinutes = minutes;
}

QString PerformanceMonitor::exportMetricsToJson() const
{
    QMutexLocker locker(&m_metricsMutex);

    QJsonObject root;
    QJsonArray metricsArray;

    for (const auto& metric : m_metrics) {
        QJsonObject metricObj;
        metricObj["name"] = metric.name;
        metricObj["category"] = metric.category;
        metricObj["value"] = metric.value;
        metricObj["unit"] = metric.unit;
        metricObj["timestamp"] = metric.timestamp.toString(Qt::ISODate);
        metricsArray.append(metricObj);
    }

    root["metrics"] = metricsArray;
    root["exportTime"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    QJsonDocument doc(root);
    return doc.toJson();
}

QString PerformanceMonitor::exportMetricsToCsv() const
{
    QMutexLocker locker(&m_metricsMutex);

    QString csv;
    QTextStream stream(&csv);

    // Header
    stream << "Name,Category,Value,Unit,Timestamp\n";

    // Data
    for (const auto& metric : m_metrics) {
        stream << metric.name << ","
               << metric.category << ","
               << metric.value << ","
               << metric.unit << ","
               << metric.timestamp.toString(Qt::ISODate) << "\n";
    }

    return csv;
}

bool PerformanceMonitor::importMetricsFromJson(const QString& json)
{
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(json.toUtf8(), &error);

    if (error.error != QJsonParseError::NoError) {
        return false;
    }

    QJsonObject root = doc.object();
    QJsonArray metricsArray = root["metrics"].toArray();

    QMutexLocker locker(&m_metricsMutex);

    for (const auto& value : metricsArray) {
        QJsonObject metricObj = value.toObject();

        PerformanceMetric metric;
        metric.name = metricObj["name"].toString();
        metric.category = metricObj["category"].toString();
        metric.value = metricObj["value"].toDouble();
        metric.unit = metricObj["unit"].toString();
        metric.timestamp = QDateTime::fromString(metricObj["timestamp"].toString(), Qt::ISODate);

        m_metrics.enqueue(metric);
    }

    return true;
}

double PerformanceMonitor::getCurrentCpuUsage() const
{
#ifdef Q_OS_WIN
    static ULARGE_INTEGER lastCPU, lastSysCPU, lastUserCPU;
    static int numProcessors = 0;
    static bool initialized = false;

    if (!initialized) {
        SYSTEM_INFO sysInfo;
        GetSystemInfo(&sysInfo);
        numProcessors = sysInfo.dwNumberOfProcessors;

        FILETIME ftime, fsys, fuser;
        GetSystemTimeAsFileTime(&ftime);
        memcpy(&lastCPU, &ftime, sizeof(FILETIME));

        GetProcessTimes(GetCurrentProcess(), &ftime, &ftime, &fsys, &fuser);
        memcpy(&lastSysCPU, &fsys, sizeof(FILETIME));
        memcpy(&lastUserCPU, &fuser, sizeof(FILETIME));

        initialized = true;
        return 0.0;
    }

    FILETIME ftime, fsys, fuser;
    ULARGE_INTEGER now, sys, user;

    GetSystemTimeAsFileTime(&ftime);
    memcpy(&now, &ftime, sizeof(FILETIME));

    GetProcessTimes(GetCurrentProcess(), &ftime, &ftime, &fsys, &fuser);
    memcpy(&sys, &fsys, sizeof(FILETIME));
    memcpy(&user, &fuser, sizeof(FILETIME));

    double percent = (sys.QuadPart - lastSysCPU.QuadPart) + (user.QuadPart - lastUserCPU.QuadPart);
    percent /= (now.QuadPart - lastCPU.QuadPart);
    percent /= numProcessors;

    lastCPU = now;
    lastUserCPU = user;
    lastSysCPU = sys;

    return percent * 100.0;
#else
    // Simplified implementation for other platforms
    return 0.0;
#endif
}

qint64 PerformanceMonitor::getCurrentMemoryUsage() const
{
#ifdef Q_OS_WIN
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
        return pmc.WorkingSetSize;
    }
#elif defined(Q_OS_LINUX)
    QFile file("/proc/self/status");
    if (file.open(QIODevice::ReadOnly)) {
        QTextStream stream(&file);
        QString line;
        while (stream.readLineInto(&line)) {
            if (line.startsWith("VmRSS:")) {
                QStringList parts = line.split(QRegExp("\\s+"));
                if (parts.size() >= 2) {
                    return parts[1].toLongLong() * 1024; // Convert KB to bytes
                }
            }
        }
    }
#endif
    return 0;
}

qint64 PerformanceMonitor::getAvailableMemory() const
{
#ifdef Q_OS_WIN
    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);
    if (GlobalMemoryStatusEx(&memInfo)) {
        return memInfo.ullAvailPhys;
    }
#elif defined(Q_OS_LINUX)
    QFile file("/proc/meminfo");
    if (file.open(QIODevice::ReadOnly)) {
        QTextStream stream(&file);
        QString line;
        while (stream.readLineInto(&line)) {
            if (line.startsWith("MemAvailable:")) {
                QStringList parts = line.split(QRegExp("\\s+"));
                if (parts.size() >= 2) {
                    return parts[1].toLongLong() * 1024; // Convert KB to bytes
                }
            }
        }
    }
#endif
    return 0;
}

int PerformanceMonitor::getCurrentThreadCount() const
{
    return QThread::idealThreadCount();
}

void PerformanceMonitor::generateRecommendations()
{
    m_currentRecommendations.clear();

    // Cache optimization
    if (shouldOptimizeCache()) {
        m_currentRecommendations << tr("Consider increasing cache size or adjusting cache strategy");
    }

    // Memory optimization
    if (shouldClearMemory()) {
        m_currentRecommendations << tr("High memory usage detected - consider clearing cache");
    }

    // Thread optimization
    if (shouldReduceThreads()) {
        m_currentRecommendations << tr("Too many active threads - consider reducing thread pool size");
    }

    // CPU optimization
    double avgCpu = getAverageMetric("CPU", 5);
    if (avgCpu > m_cpuThreshold) {
        m_currentRecommendations << tr("High CPU usage - consider reducing rendering quality or frequency");
    }

    // Rendering performance
    if (m_renderingStats.averageRenderTime > 1000) { // More than 1 second
        m_currentRecommendations << tr("Slow rendering detected - consider optimizing DPI settings");
    }

    // Emit recommendations
    for (const QString& recommendation : m_currentRecommendations) {
        emit optimizationRecommended(recommendation);
    }
}
