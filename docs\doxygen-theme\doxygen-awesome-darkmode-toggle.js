/**
 * Dark mode toggle functionality for Doxygen Awesome CSS
 * Based on doxygen-awesome-css project
 */

class DoxygenAwesomeDarkModeToggle extends HTMLElement {
    static title = "Toggle Light/Dark Mode"
    static lightModeIcon = `<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="24px" viewBox="0 0 24 24" width="24px" fill="#FFCA28"><rect fill="none" height="24" width="24"/><circle cx="12" cy="12" opacity=".3" r="3"/><path d="m12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3-3-1.35-3-3,1.35-3,3-3m12,3c0,.23-.01,.45-.03,.68l1.86,1.41c.18.14.23.41.12.61l-1.9,3.27c-.12.22-.37.29-.59.22l-2.39-.96c-.5.38-1.03.7-1.62.94l-.36,2.54c-.04.24-.24.41-.48.41h-3.8c-.24,0-.43-.17-.47-.41l-.36-2.54c-.59-.24-1.13-.56-1.62-.94l-2.39.96c-.22.08-.47,0-.59-.22l-1.9-3.27c-.12-.21-.07-.47.12-.61l1.86-1.41c-.02-.22-.03-.44-.03-.68s.01-.45.03-.68l-1.86-1.41c-.18-.14-.23-.41-.12-.61l1.9-3.27c.12-.22.37-.29.59-.22l2.39.96c.5-.38,1.03-.7,1.62-.94l.36-2.54c.04-.24.24-.41.48-.41h3.8c.24,0,.43.17.47.41l.36,2.54c.59.24,1.13.56,1.62.94l2.39-.96c.22-.08.47,0,.59.22l1.9,3.27c.12.21.07.47-.12.61l-1.86,1.41c.02.22.03.44.03.68z"/></svg>`
    static darkModeIcon = `<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="24px" viewBox="0 0 24 24" width="24px" fill="#424242"><rect fill="none" height="24" width="24"/><path d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z" opacity=".3"/><path d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"/></svg>`

    constructor() {
        super();
        this.onclick = this.toggle
    }

    connectedCallback() {
        this.title = DoxygenAwesomeDarkModeToggle.title
        this.update()
    }

    toggle() {
        switch (this.mode) {
            case "dark":
                this.mode = "light"
                break
            case "light":
                this.mode = "auto"
                break
            default:
                this.mode = "dark"
                break
        }
    }

    update() {
        switch (this.mode) {
            case "dark":
                document.documentElement.classList.add("dark-mode")
                document.documentElement.classList.remove("light-mode")
                this.innerHTML = DoxygenAwesomeDarkModeToggle.darkModeIcon
                break

            case "light":
                document.documentElement.classList.remove("dark-mode")
                document.documentElement.classList.add("light-mode")
                this.innerHTML = DoxygenAwesomeDarkModeToggle.lightModeIcon
                break

            default:
                document.documentElement.classList.remove("dark-mode")
                document.documentElement.classList.remove("light-mode")
                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                    this.innerHTML = DoxygenAwesomeDarkModeToggle.darkModeIcon
                } else {
                    this.innerHTML = DoxygenAwesomeDarkModeToggle.lightModeIcon
                }
                break
        }
    }

    set mode(mode) {
        localStorage.setItem("doxygen-awesome-mode", mode)
        this.update()
    }

    get mode() {
        return localStorage.getItem("doxygen-awesome-mode") || "auto"
    }
}

customElements.define("doxygen-awesome-dark-mode-toggle", DoxygenAwesomeDarkModeToggle);

// Initialize theme on page load
document.addEventListener('DOMContentLoaded', function() {
    // Create and insert the dark mode toggle if it doesn't exist
    if (!document.querySelector('doxygen-awesome-dark-mode-toggle')) {
        const toggle = document.createElement('doxygen-awesome-dark-mode-toggle');
        
        // Try to insert in the header area
        const header = document.querySelector('#titlearea');
        if (header) {
            header.appendChild(toggle);
        } else {
            // Fallback: insert at the top of the body
            document.body.insertBefore(toggle, document.body.firstChild);
        }
    }
    
    // Apply saved theme preference
    const savedMode = localStorage.getItem("doxygen-awesome-mode") || "auto";
    const toggle = document.querySelector('doxygen-awesome-dark-mode-toggle');
    if (toggle) {
        toggle.mode = savedMode;
    }
});

// Listen for system theme changes
if (window.matchMedia) {
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
        const toggle = document.querySelector('doxygen-awesome-dark-mode-toggle');
        if (toggle && toggle.mode === "auto") {
            toggle.update();
        }
    });
}

// Keyboard shortcut for theme toggle (Ctrl/Cmd + Shift + D)
document.addEventListener('keydown', function(e) {
    if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'D') {
        e.preventDefault();
        const toggle = document.querySelector('doxygen-awesome-dark-mode-toggle');
        if (toggle) {
            toggle.toggle();
        }
    }
});

// Export for use in other scripts
window.DoxygenAwesomeDarkModeToggle = DoxygenAwesomeDarkModeToggle;
