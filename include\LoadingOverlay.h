#ifndef LOADINGOVERLAY_H
#define LOADINGOVERLAY_H

#include "ElaIntegration.h"
#include <QPropertyAnimation>
#include <QTimer>

class LoadingOverlay : public QWidget
{
    Q_OBJECT

public:
    explicit LoadingOverlay(QWidget *parent = nullptr);
    ~LoadingOverlay();

    void showLoading(const QString& message = tr("Loading..."));
    void hideLoading();
    void setProgress(int value);
    void setMessage(const QString& message);

protected:
    void paintEvent(QPaintEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private slots:
    void updateAnimation();

private:
    void setupUI();
    void updatePosition();

    // UI Components
    ElaText* m_messageLabel;
    ElaProgress* m_progressBar;
    QWidget* m_contentWidget;
    
    // Animation
    QPropertyAnimation* m_fadeAnimation;
    QPropertyAnimation* m_rotationAnimation;
    QTimer* m_animationTimer;
    
    // State
    bool m_isVisible;
    int m_rotationAngle;
    QString m_currentMessage;
    
    // Styling
    static const int OVERLAY_RADIUS = 12;
    static const int CONTENT_PADDING = 30;
    static const int SPINNER_SIZE = 32;
};

#endif // LOADINGOVERLAY_H
