# Optimized PDF Viewer

A high-performance PDF viewer built with Qt6 and Poppler, featuring advanced annotation capabilities, optimized rendering, and modern FluentUI-style interface powered by ElaWidgetTools.

## Features

- **Fast PDF Rendering**: Optimized rendering engine using Poppler-Qt6
- **Modern FluentUI Interface**: Beautiful, modern interface powered by ElaWidgetTools
- **Advanced Annotations**: Comprehensive annotation system with multiple types
- **Annotation Management**: Create, edit, delete, and search annotations
- **Annotation Clipboard**: Copy and paste annotations between documents
- **Multi-tab Interface**: Open multiple PDF documents simultaneously with enhanced tab styling
- **Enhanced UI Components**: Modern buttons, sliders, progress bars, and dock widgets
- **Theme Support**: Light and dark theme support through ElaWidgetTools
- **Print Support**: Built-in printing capabilities
- **Cross-platform**: Runs on Windows, Linux, and macOS

## Screenshots

*Screenshots will be added here*

## Requirements

### System Requirements
- Qt6 (6.0 or later)
- Poppler with Qt6 bindings
- CMake 3.16 or later
- C++17 compatible compiler

### Dependencies
- **Qt6 Components**: Core, Gui, Widgets, Concurrent, PrintSupport
- **Poppler**: poppler-cpp, poppler-qt6
- **ElaWidgetTools**: Modern FluentUI-style widget library (included as submodule)
- **Build System**: CMake, pkg-config

## Installation

### Ubuntu/Debian
```bash
sudo apt update
sudo apt install qt6-base-dev qt6-tools-dev libpoppler-qt6-dev libpoppler-cpp-dev cmake pkg-config
```

### Windows
1. Install Qt6 from [Qt official website](https://www.qt.io/download)
2. Install vcpkg and Poppler:
```cmd
vcpkg install poppler[qt6]:x64-windows
```

### macOS
```bash
brew install qt6 poppler cmake pkg-config
```

## Building

### Clone the Repository
```bash
git clone <repository-url>
cd qt-pdf-render
# Initialize ElaWidgetTools submodule for enhanced UI
git submodule update --init --recursive
```

### Build with CMake
```bash
mkdir build
cd build
cmake ..
make -j$(nproc)
```

### Windows Build
```cmd
mkdir build
cd build
cmake .. -DCMAKE_TOOLCHAIN_FILE=path/to/vcpkg/scripts/buildsystems/vcpkg.cmake
cmake --build . --config Release
```

## ElaWidgetTools Integration

This project uses [ElaWidgetTools](https://github.com/Liniyous/ElaWidgetTools) to provide a modern FluentUI-style interface. The integration includes:

### Enhanced UI Components
- **Modern Buttons**: Styled push buttons with hover effects
- **Enhanced Input Fields**: Modern line edits and combo boxes
- **Styled Progress Bars**: Smooth progress indicators
- **Modern Sliders**: Enhanced slider controls
- **Dock Widgets**: Improved dock widget styling
- **Tab Widget**: Chrome-style tabs with modern appearance
- **Check Boxes**: Modern checkbox styling

### Theme Support
- Light and dark theme support
- Automatic theme detection
- Consistent styling across all components

### Fallback Support
The application gracefully falls back to standard Qt widgets if ElaWidgetTools is not available, ensuring compatibility.

## Usage

### Basic Usage
```bash
./optimized-pdf-viewer [pdf-file]
```

### Command Line Options
- Open a specific PDF file: `./optimized-pdf-viewer document.pdf`
- Multiple files: `./optimized-pdf-viewer file1.pdf file2.pdf`

### Keyboard Shortcuts
- `Ctrl+O`: Open file
- `Ctrl+W`: Close tab
- `Ctrl+Q`: Quit application
- `Ctrl+P`: Print document
- `Ctrl+F`: Find/Search
- `F11`: Toggle fullscreen

## Annotation Features

### Supported Annotation Types
- Text annotations
- Highlight annotations
- Note annotations
- Drawing annotations
- Shape annotations

### Annotation Operations
- **Create**: Click and drag to create annotations
- **Edit**: Double-click to edit annotation properties
- **Delete**: Select and press Delete key
- **Search**: Use Ctrl+F to search through annotations
- **Copy/Paste**: Use annotation clipboard for copying between documents

## Testing

### Running Tests
```bash
# From project root
./scripts/run_tests.sh    # Linux/macOS
./scripts/run_tests.bat   # Windows

# Or manually
cd build
ctest
```

### Test Coverage
The project includes comprehensive unit tests for:
- PDF rendering functionality
- Annotation management
- Document tab handling
- Integration tests

## Development

### Project Structure
```
qt-pdf-render/
├── src/                 # Source files (.cpp)
├── include/             # Header files (.h)
├── tests/               # Unit tests
├── scripts/             # Build and utility scripts
├── docs/                # Documentation
├── build/               # Build output (generated)
└── CMakeLists.txt       # Main CMake configuration
```

### Contributing
Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

### Code Style
- Follow Qt coding conventions
- Use clang-format for code formatting
- Include unit tests for new features
- Document public APIs

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Qt Framework for the excellent GUI toolkit
- Poppler library for PDF rendering capabilities
- Contributors and testers

## Support

For support, please:
1. Check the [documentation](docs/)
2. Search existing [issues](../../issues)
3. Create a new issue if needed

## Roadmap

- [ ] Enhanced annotation tools
- [ ] Plugin system
- [ ] Advanced search capabilities
- [ ] Performance optimizations
- [ ] Mobile support
