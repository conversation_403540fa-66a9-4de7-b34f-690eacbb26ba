#!/usr/bin/env python3
"""
Documentation Validation Script for Optimized PDF Viewer

This script validates generated documentation for:
- Broken links and references
- Missing images and assets
- HTML validation
- Accessibility compliance
- Performance optimization
- Content completeness
"""

import os
import sys
import argparse
import re
import urllib.parse
from pathlib import Path
from typing import List, Dict, Set, Tuple, Optional
import json
import time
from dataclasses import dataclass
from html.parser import HTMLParser

@dataclass
class ValidationIssue:
    """Represents a validation issue found in documentation"""
    severity: str  # 'error', 'warning', 'info'
    category: str  # 'link', 'image', 'html', 'accessibility', 'performance'
    file_path: str
    line_number: Optional[int]
    message: str
    suggestion: Optional[str] = None

class LinkChecker:
    """Validates links in HTML documentation"""
    
    def __init__(self, docs_root: Path):
        self.docs_root = docs_root
        self.existing_files = set()
        self.issues = []
        
        # Build index of existing files
        for file_path in docs_root.rglob('*'):
            if file_path.is_file():
                relative_path = file_path.relative_to(docs_root)
                self.existing_files.add(str(relative_path))
                self.existing_files.add(str(relative_path).replace('\\', '/'))
    
    def check_links(self, html_file: Path) -> List[ValidationIssue]:
        """Check all links in an HTML file"""
        issues = []
        
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            issues.append(ValidationIssue(
                severity='error',
                category='file',
                file_path=str(html_file),
                line_number=None,
                message=f"Could not read file: {e}"
            ))
            return issues
        
        # Find all links
        link_pattern = r'<a[^>]+href=["\']([^"\']+)["\'][^>]*>'
        img_pattern = r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>'
        
        for pattern, link_type in [(link_pattern, 'link'), (img_pattern, 'image')]:
            for match in re.finditer(pattern, content, re.IGNORECASE):
                url = match.group(1)
                line_num = content[:match.start()].count('\n') + 1
                
                issue = self._validate_link(url, html_file, line_num, link_type)
                if issue:
                    issues.append(issue)
        
        return issues
    
    def _validate_link(self, url: str, source_file: Path, line_num: int, link_type: str) -> Optional[ValidationIssue]:
        """Validate a single link"""
        # Skip external URLs and special protocols
        if any(url.startswith(proto) for proto in ['http://', 'https://', 'mailto:', 'tel:', 'javascript:']):
            return None
        
        # Skip anchors without file reference
        if url.startswith('#'):
            return None
        
        # Parse URL
        parsed = urllib.parse.urlparse(url)
        file_part = parsed.path
        fragment = parsed.fragment
        
        # Resolve relative path
        if file_part.startswith('/'):
            # Absolute path from docs root
            target_path = file_part.lstrip('/')
        else:
            # Relative path
            source_dir = source_file.parent.relative_to(self.docs_root)
            target_path = str(source_dir / file_part)
        
        # Normalize path
        target_path = target_path.replace('\\', '/')
        
        # Check if file exists
        if target_path not in self.existing_files:
            return ValidationIssue(
                severity='error',
                category=link_type,
                file_path=str(source_file.relative_to(self.docs_root)),
                line_number=line_num,
                message=f"Broken {link_type}: {url} -> {target_path}",
                suggestion=f"Check if the target file exists or fix the path"
            )
        
        # TODO: Check if fragment exists in target HTML file
        
        return None

class HTMLValidator:
    """Validates HTML structure and accessibility"""
    
    def __init__(self):
        self.issues = []
    
    def validate_file(self, html_file: Path) -> List[ValidationIssue]:
        """Validate HTML structure and accessibility"""
        issues = []
        
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            issues.append(ValidationIssue(
                severity='error',
                category='file',
                file_path=str(html_file),
                line_number=None,
                message=f"Could not read file: {e}"
            ))
            return issues
        
        # Basic HTML validation
        issues.extend(self._check_basic_html(content, html_file))
        
        # Accessibility checks
        issues.extend(self._check_accessibility(content, html_file))
        
        return issues
    
    def _check_basic_html(self, content: str, file_path: Path) -> List[ValidationIssue]:
        """Check basic HTML structure"""
        issues = []
        
        # Check for required elements
        if not re.search(r'<!DOCTYPE\s+html>', content, re.IGNORECASE):
            issues.append(ValidationIssue(
                severity='warning',
                category='html',
                file_path=str(file_path),
                line_number=1,
                message="Missing DOCTYPE declaration",
                suggestion="Add <!DOCTYPE html> at the beginning of the file"
            ))
        
        if not re.search(r'<html[^>]*>', content, re.IGNORECASE):
            issues.append(ValidationIssue(
                severity='error',
                category='html',
                file_path=str(file_path),
                line_number=None,
                message="Missing <html> element"
            ))
        
        if not re.search(r'<head[^>]*>.*</head>', content, re.IGNORECASE | re.DOTALL):
            issues.append(ValidationIssue(
                severity='error',
                category='html',
                file_path=str(file_path),
                line_number=None,
                message="Missing <head> section"
            ))
        
        if not re.search(r'<title[^>]*>.*</title>', content, re.IGNORECASE | re.DOTALL):
            issues.append(ValidationIssue(
                severity='warning',
                category='html',
                file_path=str(file_path),
                line_number=None,
                message="Missing <title> element",
                suggestion="Add a descriptive title for better SEO and accessibility"
            ))
        
        return issues
    
    def _check_accessibility(self, content: str, file_path: Path) -> List[ValidationIssue]:
        """Check accessibility compliance"""
        issues = []
        
        # Check for images without alt text
        img_pattern = r'<img[^>]*>'
        for match in re.finditer(img_pattern, content, re.IGNORECASE):
            img_tag = match.group(0)
            line_num = content[:match.start()].count('\n') + 1
            
            if 'alt=' not in img_tag.lower():
                issues.append(ValidationIssue(
                    severity='warning',
                    category='accessibility',
                    file_path=str(file_path),
                    line_number=line_num,
                    message="Image missing alt attribute",
                    suggestion="Add alt text for screen readers"
                ))
        
        # Check for proper heading hierarchy
        headings = re.findall(r'<h([1-6])[^>]*>', content, re.IGNORECASE)
        if headings:
            prev_level = 0
            for i, level_str in enumerate(headings):
                level = int(level_str)
                if level > prev_level + 1:
                    issues.append(ValidationIssue(
                        severity='warning',
                        category='accessibility',
                        file_path=str(file_path),
                        line_number=None,
                        message=f"Heading hierarchy skip: h{prev_level} to h{level}",
                        suggestion="Use proper heading hierarchy (h1, h2, h3, etc.)"
                    ))
                prev_level = level
        
        return issues

class PerformanceChecker:
    """Checks documentation performance aspects"""
    
    def check_file_sizes(self, docs_root: Path) -> List[ValidationIssue]:
        """Check for oversized files"""
        issues = []
        
        # Size limits (in bytes)
        limits = {
            '.html': 1024 * 1024,  # 1MB
            '.css': 512 * 1024,    # 512KB
            '.js': 512 * 1024,     # 512KB
            '.png': 2 * 1024 * 1024,  # 2MB
            '.jpg': 2 * 1024 * 1024,  # 2MB
            '.jpeg': 2 * 1024 * 1024, # 2MB
            '.svg': 1024 * 1024,   # 1MB
        }
        
        for file_path in docs_root.rglob('*'):
            if file_path.is_file():
                size = file_path.stat().st_size
                suffix = file_path.suffix.lower()
                
                if suffix in limits and size > limits[suffix]:
                    issues.append(ValidationIssue(
                        severity='warning',
                        category='performance',
                        file_path=str(file_path.relative_to(docs_root)),
                        line_number=None,
                        message=f"Large file: {size / 1024:.1f}KB (limit: {limits[suffix] / 1024:.1f}KB)",
                        suggestion="Consider optimizing or compressing the file"
                    ))
        
        return issues

class DocumentationValidator:
    """Main documentation validator"""
    
    def __init__(self, docs_root: str):
        self.docs_root = Path(docs_root).resolve()
        self.link_checker = LinkChecker(self.docs_root)
        self.html_validator = HTMLValidator()
        self.performance_checker = PerformanceChecker()
        self.all_issues = []
    
    def validate_all(self) -> Dict[str, List[ValidationIssue]]:
        """Run all validation checks"""
        print(f"Validating documentation in: {self.docs_root}")
        
        # Find all HTML files
        html_files = list(self.docs_root.rglob('*.html'))
        print(f"Found {len(html_files)} HTML files to validate")
        
        results = {
            'link_issues': [],
            'html_issues': [],
            'performance_issues': []
        }
        
        # Check links in all HTML files
        print("Checking links...")
        for html_file in html_files:
            issues = self.link_checker.check_links(html_file)
            results['link_issues'].extend(issues)
        
        # Validate HTML structure
        print("Validating HTML structure...")
        for html_file in html_files:
            issues = self.html_validator.validate_file(html_file)
            results['html_issues'].extend(issues)
        
        # Check performance
        print("Checking performance...")
        results['performance_issues'] = self.performance_checker.check_file_sizes(self.docs_root)
        
        # Combine all issues
        self.all_issues = []
        for category_issues in results.values():
            self.all_issues.extend(category_issues)
        
        return results
    
    def print_report(self, results: Dict[str, List[ValidationIssue]]):
        """Print validation report"""
        total_issues = sum(len(issues) for issues in results.values())
        
        if total_issues == 0:
            print("\n✅ All validation checks passed!")
            return
        
        print(f"\n📋 Validation Report ({total_issues} issues found)")
        print("=" * 60)
        
        # Group issues by severity
        by_severity = {'error': [], 'warning': [], 'info': []}
        for issues in results.values():
            for issue in issues:
                by_severity[issue.severity].append(issue)
        
        # Print summary
        print(f"Errors: {len(by_severity['error'])}")
        print(f"Warnings: {len(by_severity['warning'])}")
        print(f"Info: {len(by_severity['info'])}")
        print()
        
        # Print detailed issues
        for severity in ['error', 'warning', 'info']:
            issues = by_severity[severity]
            if not issues:
                continue
            
            icon = {'error': '❌', 'warning': '⚠️', 'info': 'ℹ️'}[severity]
            print(f"{icon} {severity.upper()} ({len(issues)} issues)")
            print("-" * 40)
            
            for issue in issues:
                location = issue.file_path
                if issue.line_number:
                    location += f":{issue.line_number}"
                
                print(f"  {location}")
                print(f"    {issue.message}")
                if issue.suggestion:
                    print(f"    💡 {issue.suggestion}")
                print()
    
    def save_report(self, results: Dict[str, List[ValidationIssue]], output_file: str):
        """Save validation report to JSON file"""
        report_data = {
            'timestamp': time.time(),
            'docs_root': str(self.docs_root),
            'summary': {
                'total_issues': len(self.all_issues),
                'errors': len([i for i in self.all_issues if i.severity == 'error']),
                'warnings': len([i for i in self.all_issues if i.severity == 'warning']),
                'info': len([i for i in self.all_issues if i.severity == 'info'])
            },
            'issues': []
        }
        
        for issue in self.all_issues:
            report_data['issues'].append({
                'severity': issue.severity,
                'category': issue.category,
                'file_path': issue.file_path,
                'line_number': issue.line_number,
                'message': issue.message,
                'suggestion': issue.suggestion
            })
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"📄 Detailed report saved to: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="Validate generated documentation")
    parser.add_argument("--docs-dir", required=True, help="Documentation directory to validate")
    parser.add_argument("--output", help="Output file for detailed report (JSON)")
    parser.add_argument("--fail-on-error", action="store_true", help="Exit with error code if validation fails")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.docs_dir):
        print(f"❌ Documentation directory not found: {args.docs_dir}")
        sys.exit(1)
    
    validator = DocumentationValidator(args.docs_dir)
    results = validator.validate_all()
    validator.print_report(results)
    
    if args.output:
        validator.save_report(results, args.output)
    
    # Exit with error code if there are errors and --fail-on-error is set
    if args.fail_on_error:
        error_count = len([i for i in validator.all_issues if i.severity == 'error'])
        if error_count > 0:
            print(f"\n❌ Validation failed with {error_count} errors")
            sys.exit(1)
    
    print("\n✅ Validation completed")

if __name__ == "__main__":
    main()
