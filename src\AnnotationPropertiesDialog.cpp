#include "AnnotationPropertiesDialog.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QTabWidget>
#include <QGroupBox>
#include <QLineEdit>
#include <QTextEdit>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QSlider>
#include <QLabel>
#include <QPushButton>
#include <QComboBox>
#include <QFontComboBox>
#include <QDateTimeEdit>
#include <QColorDialog>
#include <QDialogButtonBox>
#include <QPainter>
#include <QPixmap>
#include <QDebug>

AnnotationPropertiesDialog::AnnotationPropertiesDialog(Annotation* annotation, QWidget *parent)
    : QDialog(parent)
    , m_annotation(annotation)
{
    setWindowTitle(tr("Annotation Properties"));
    setModal(true);
    resize(400, 500);
    
    if (m_annotation) {
        loadAnnotationProperties();
    }
    
    setupUi();
    updateColorButton();
    updatePreview();
}

AnnotationPropertiesDialog::~AnnotationPropertiesDialog()
{
}

void AnnotationPropertiesDialog::setupUi()
{
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    
    // Create tab widget
    QTabWidget* tabWidget = new QTabWidget();
    
    // General tab
    QWidget* generalTab = new QWidget();
    setupGeneralTab();
    QVBoxLayout* generalLayout = new QVBoxLayout(generalTab);
    generalLayout->addWidget(m_generalGroup);
    generalLayout->addStretch();
    tabWidget->addTab(generalTab, tr("General"));
    
    // Appearance tab
    QWidget* appearanceTab = new QWidget();
    setupAppearanceTab();
    QVBoxLayout* appearanceLayout = new QVBoxLayout(appearanceTab);
    appearanceLayout->addWidget(m_appearanceGroup);
    if (m_fontGroup) {
        appearanceLayout->addWidget(m_fontGroup);
    }
    appearanceLayout->addWidget(m_previewLabel);
    appearanceLayout->addStretch();
    tabWidget->addTab(appearanceTab, tr("Appearance"));
    
    // Content tab (if applicable)
    if (m_annotation && (m_annotation->getType() == AnnotationType::Note || 
                        m_annotation->getType() == AnnotationType::Text)) {
        QWidget* contentTab = new QWidget();
        setupContentTab();
        QVBoxLayout* contentLayout = new QVBoxLayout(contentTab);
        contentLayout->addWidget(m_contentGroup);
        contentLayout->addStretch();
        tabWidget->addTab(contentTab, tr("Content"));
    }
    
    mainLayout->addWidget(tabWidget);
    
    // Button box
    QDialogButtonBox* buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel);
    connect(buttonBox, &QDialogButtonBox::accepted, this, &AnnotationPropertiesDialog::onAccepted);
    connect(buttonBox, &QDialogButtonBox::rejected, this, &AnnotationPropertiesDialog::onRejected);
    mainLayout->addWidget(buttonBox);
}

void AnnotationPropertiesDialog::setupGeneralTab()
{
    m_generalGroup = new QGroupBox(tr("General Information"));
    QGridLayout* layout = new QGridLayout(m_generalGroup);
    
    // ID (read-only)
    layout->addWidget(new QLabel(tr("ID:")), 0, 0);
    m_idEdit = new QLineEdit();
    m_idEdit->setReadOnly(true);
    if (m_annotation) {
        m_idEdit->setText(m_annotation->getId());
    }
    layout->addWidget(m_idEdit, 0, 1);
    
    // Author
    layout->addWidget(new QLabel(tr("Author:")), 1, 0);
    m_authorEdit = new QLineEdit();
    m_authorEdit->setText(m_author);
    connect(m_authorEdit, &QLineEdit::textChanged, this, &AnnotationPropertiesDialog::onAuthorChanged);
    layout->addWidget(m_authorEdit, 1, 1);
    
    // Creation date (read-only)
    layout->addWidget(new QLabel(tr("Created:")), 2, 0);
    m_createdEdit = new QDateTimeEdit();
    m_createdEdit->setReadOnly(true);
    if (m_annotation) {
        m_createdEdit->setDateTime(m_annotation->getCreationDate());
    }
    layout->addWidget(m_createdEdit, 2, 1);
    
    // Modification date (read-only)
    layout->addWidget(new QLabel(tr("Modified:")), 3, 0);
    m_modifiedEdit = new QDateTimeEdit();
    m_modifiedEdit->setReadOnly(true);
    if (m_annotation) {
        m_modifiedEdit->setDateTime(m_annotation->getModificationDate());
    }
    layout->addWidget(m_modifiedEdit, 3, 1);
}

void AnnotationPropertiesDialog::setupAppearanceTab()
{
    m_appearanceGroup = new QGroupBox(tr("Appearance"));
    QGridLayout* layout = new QGridLayout(m_appearanceGroup);
    
    // Color
    layout->addWidget(new QLabel(tr("Color:")), 0, 0);
    m_colorButton = new QPushButton();
    m_colorButton->setFixedSize(50, 25);
    connect(m_colorButton, &QPushButton::clicked, this, &AnnotationPropertiesDialog::onColorButtonClicked);
    layout->addWidget(m_colorButton, 0, 1);
    
    // Opacity
    layout->addWidget(new QLabel(tr("Opacity:")), 1, 0);
    QHBoxLayout* opacityLayout = new QHBoxLayout();
    m_opacitySlider = new QSlider(Qt::Horizontal);
    m_opacitySlider->setRange(10, 100);
    m_opacitySlider->setValue(static_cast<int>(m_opacity * 100));
    connect(m_opacitySlider, &QSlider::valueChanged, this, &AnnotationPropertiesDialog::onOpacityChanged);
    m_opacityLabel = new QLabel(QString("%1%").arg(static_cast<int>(m_opacity * 100)));
    opacityLayout->addWidget(m_opacitySlider);
    opacityLayout->addWidget(m_opacityLabel);
    layout->addLayout(opacityLayout, 1, 1);
    
    // Line width (for drawings and shapes)
    if (m_annotation && (m_annotation->getType() == AnnotationType::Drawing || 
                        m_annotation->getType() == AnnotationType::Rectangle ||
                        m_annotation->getType() == AnnotationType::Circle ||
                        m_annotation->getType() == AnnotationType::Arrow)) {
        layout->addWidget(new QLabel(tr("Line Width:")), 2, 0);
        m_lineWidthSpinBox = new QDoubleSpinBox();
        m_lineWidthSpinBox->setRange(0.5, 20.0);
        m_lineWidthSpinBox->setSingleStep(0.5);
        m_lineWidthSpinBox->setValue(m_lineWidth);
        connect(m_lineWidthSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
                this, &AnnotationPropertiesDialog::onLineWidthChanged);
        layout->addWidget(m_lineWidthSpinBox, 2, 1);
    }
    
    // Font controls (for text annotations)
    if (m_annotation && m_annotation->getType() == AnnotationType::Text) {
        m_fontGroup = new QGroupBox(tr("Font"));
        QGridLayout* fontLayout = new QGridLayout(m_fontGroup);
        
        // Font family
        fontLayout->addWidget(new QLabel(tr("Font:")), 0, 0);
        m_fontComboBox = new QFontComboBox();
        m_fontComboBox->setCurrentFont(m_font);
        connect(m_fontComboBox, &QFontComboBox::currentFontChanged, this, &AnnotationPropertiesDialog::onFontChanged);
        fontLayout->addWidget(m_fontComboBox, 0, 1);
        
        // Font size
        fontLayout->addWidget(new QLabel(tr("Size:")), 1, 0);
        m_fontSizeSpinBox = new QSpinBox();
        m_fontSizeSpinBox->setRange(6, 72);
        m_fontSizeSpinBox->setValue(static_cast<int>(m_font.pointSizeF()));
        connect(m_fontSizeSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &AnnotationPropertiesDialog::onFontChanged);
        fontLayout->addWidget(m_fontSizeSpinBox, 1, 1);
        
        // Bold and italic
        QHBoxLayout* styleLayout = new QHBoxLayout();
        m_boldButton = new QPushButton(tr("Bold"));
        m_boldButton->setCheckable(true);
        m_boldButton->setChecked(m_font.bold());
        connect(m_boldButton, &QPushButton::toggled, this, &AnnotationPropertiesDialog::onFontChanged);
        
        m_italicButton = new QPushButton(tr("Italic"));
        m_italicButton->setCheckable(true);
        m_italicButton->setChecked(m_font.italic());
        connect(m_italicButton, &QPushButton::toggled, this, &AnnotationPropertiesDialog::onFontChanged);
        
        styleLayout->addWidget(m_boldButton);
        styleLayout->addWidget(m_italicButton);
        styleLayout->addStretch();
        fontLayout->addLayout(styleLayout, 2, 0, 1, 2);
    }
    
    // Preview
    m_previewLabel = new QLabel();
    m_previewLabel->setFrameStyle(QFrame::StyledPanel);
    m_previewLabel->setMinimumHeight(100);
    m_previewLabel->setAlignment(Qt::AlignCenter);
}

void AnnotationPropertiesDialog::setupContentTab()
{
    m_contentGroup = new QGroupBox(tr("Content"));
    QVBoxLayout* layout = new QVBoxLayout(m_contentGroup);
    
    layout->addWidget(new QLabel(tr("Text Content:")));
    m_contentEdit = new QTextEdit();
    m_contentEdit->setPlainText(m_content);
    connect(m_contentEdit, &QTextEdit::textChanged, this, &AnnotationPropertiesDialog::onContentChanged);
    layout->addWidget(m_contentEdit);
}

void AnnotationPropertiesDialog::loadAnnotationProperties()
{
    if (!m_annotation) return;
    
    m_color = m_annotation->getColor();
    m_opacity = m_annotation->getOpacity();
    m_lineWidth = m_annotation->getLineWidth();
    m_content = m_annotation->getContent();
    m_author = m_annotation->getAuthor();
    
    // Load font for text annotations
    if (auto textAnnotation = dynamic_cast<TextAnnotation*>(m_annotation)) {
        m_font = textAnnotation->getFont();
    } else {
        m_font = QFont("Arial", 12);
    }
}

void AnnotationPropertiesDialog::updateColorButton()
{
    QPixmap pixmap(40, 20);
    pixmap.fill(m_color);
    
    QPainter painter(&pixmap);
    painter.setPen(Qt::black);
    painter.drawRect(pixmap.rect().adjusted(0, 0, -1, -1));
    
    m_colorButton->setIcon(QIcon(pixmap));
}

void AnnotationPropertiesDialog::updatePreview()
{
    // Create a simple preview of the annotation
    QPixmap preview(200, 80);
    preview.fill(Qt::white);
    
    QPainter painter(&preview);
    painter.setRenderHint(QPainter::Antialiasing);
    
    QColor previewColor = m_color;
    previewColor.setAlphaF(m_opacity);
    
    QRect previewRect(10, 10, 180, 60);
    
    if (m_annotation) {
        switch (m_annotation->getType()) {
        case AnnotationType::Highlight:
            painter.fillRect(previewRect, previewColor);
            painter.setPen(Qt::black);
            painter.drawText(previewRect, Qt::AlignCenter, tr("Highlight Preview"));
            break;
            
        case AnnotationType::Note:
            painter.setBrush(previewColor);
            painter.setPen(Qt::black);
            painter.drawRect(previewRect);
            painter.drawText(previewRect, Qt::AlignCenter, tr("Note"));
            break;
            
        case AnnotationType::Rectangle:
            painter.setBrush(Qt::NoBrush);
            painter.setPen(QPen(previewColor, m_lineWidth));
            painter.drawRect(previewRect);
            break;
            
        case AnnotationType::Circle:
            painter.setBrush(Qt::NoBrush);
            painter.setPen(QPen(previewColor, m_lineWidth));
            painter.drawEllipse(previewRect);
            break;
            
        case AnnotationType::Text:
            painter.setPen(previewColor);
            painter.setFont(m_font);
            painter.drawText(previewRect, Qt::AlignCenter | Qt::TextWordWrap, 
                           m_content.isEmpty() ? tr("Sample Text") : m_content);
            break;
            
        default:
            painter.setPen(QPen(previewColor, m_lineWidth));
            painter.drawLine(previewRect.topLeft(), previewRect.bottomRight());
            break;
        }
    }
    
    m_previewLabel->setPixmap(preview);
}

void AnnotationPropertiesDialog::onColorButtonClicked()
{
    QColorDialog colorDialog(m_color, this);
    if (colorDialog.exec() == QDialog::Accepted) {
        m_color = colorDialog.currentColor();
        updateColorButton();
        updatePreview();
        m_modified = true;
    }
}

void AnnotationPropertiesDialog::onOpacityChanged(int value)
{
    m_opacity = value / 100.0;
    m_opacityLabel->setText(QString("%1%").arg(value));
    updatePreview();
    m_modified = true;
}

void AnnotationPropertiesDialog::onLineWidthChanged(double value)
{
    m_lineWidth = value;
    updatePreview();
    m_modified = true;
}

void AnnotationPropertiesDialog::onContentChanged()
{
    if (m_contentEdit) {
        m_content = m_contentEdit->toPlainText();
        updatePreview();
        m_modified = true;
    }
}

void AnnotationPropertiesDialog::onAuthorChanged()
{
    if (m_authorEdit) {
        m_author = m_authorEdit->text();
        m_modified = true;
    }
}

void AnnotationPropertiesDialog::onFontChanged()
{
    if (m_fontComboBox && m_fontSizeSpinBox) {
        m_font = m_fontComboBox->currentFont();
        m_font.setPointSize(m_fontSizeSpinBox->value());

        if (m_boldButton) {
            m_font.setBold(m_boldButton->isChecked());
        }
        if (m_italicButton) {
            m_font.setItalic(m_italicButton->isChecked());
        }

        updatePreview();
        m_modified = true;
    }
}

void AnnotationPropertiesDialog::onAccepted()
{
    accept();
}

void AnnotationPropertiesDialog::onRejected()
{
    reject();
}
