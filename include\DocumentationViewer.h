#ifndef DOCUMENTATIONVIEWER_H
#define DOCUMENTATIONVIEWER_H

#include <QWidget>
#include <QTextBrowser>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QSplitter>
#include <QTreeWidget>
#include <QLineEdit>
#include <QPushButton>
#include "ElaToolButton.h"
#include <QLabel>
#include <QSlider>
#include <QComboBox>
#include <QProgressBar>
#include <QTextDocument>
#include <QTextCursor>
#include <QTextCharFormat>
#include <QSyntaxHighlighter>
#include <QRegularExpression>
#include <QUrl>
#include <QStringList>
#include <QSettings>
#include <QTimer>
#include <QMenu>
#include <QAction>
#include <QToolBar>
#include <QStatusBar>
#include <QListWidget>
#include <QStackedWidget>

class QSyntaxHighlighter;
class QTextDocument;
class DocumentationSyntaxHighlighter;

/**
 * @brief Advanced documentation viewer with search, bookmarks, and navigation
 *
 * The DocumentationViewer class provides a comprehensive documentation viewing
 * experience with features including:
 * - HTML-based documentation rendering
 * - Full-text search with highlighting
 * - Bookmarking system for frequently accessed sections
 * - Table of contents navigation
 * - Syntax highlighting for code examples
 * - Zoom controls and print functionality
 * - History-based navigation (back/forward)
 * - Modern UI integration
 *
 * @since 1.0.0
 */
class DocumentationViewer : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief Constructs a new DocumentationViewer
     * @param parent Parent widget
     */
    explicit DocumentationViewer(QWidget* parent = nullptr);
    
    /**
     * @brief Destructor with proper cleanup
     */
    ~DocumentationViewer();

    /**
     * @brief Loads documentation from a file or URL
     * @param source Path to documentation file or URL
     * @return true if loaded successfully, false otherwise
     */
    bool loadDocumentation(const QString& source);

    /**
     * @brief Sets the base URL for relative links
     * @param baseUrl Base URL for resolving relative paths
     */
    void setBaseUrl(const QUrl& baseUrl);

    /**
     * @brief Gets the current zoom level
     * @return Current zoom level as percentage (100 = normal)
     */
    int getZoomLevel() const;

    /**
     * @brief Sets the zoom level
     * @param zoomLevel Zoom level as percentage (50-300)
     */
    void setZoomLevel(int zoomLevel);

public slots:
    /**
     * @brief Searches for text in the documentation
     * @param searchTerm Text to search for
     * @param caseSensitive Whether search should be case sensitive
     * @param wholeWords Whether to match whole words only
     */
    void searchText(const QString& searchTerm, bool caseSensitive = false, bool wholeWords = false);

    /**
     * @brief Clears current search and highlighting
     */
    void clearSearch();

    /**
     * @brief Navigates to the next search result
     */
    void findNext();

    /**
     * @brief Navigates to the previous search result
     */
    void findPrevious();

    /**
     * @brief Adds current page to bookmarks
     * @param title Custom title for bookmark (optional)
     */
    void addBookmark(const QString& title = QString());

    /**
     * @brief Removes a bookmark
     * @param url URL of bookmark to remove
     */
    void removeBookmark(const QString& url);

    /**
     * @brief Navigates to a bookmarked page
     * @param url URL to navigate to
     */
    void navigateToBookmark(const QString& url);

    /**
     * @brief Navigates back in history
     */
    void goBack();

    /**
     * @brief Navigates forward in history
     */
    void goForward();

    /**
     * @brief Zooms in the documentation
     */
    void zoomIn();

    /**
     * @brief Zooms out the documentation
     */
    void zoomOut();

    /**
     * @brief Resets zoom to 100%
     */
    void resetZoom();

    /**
     * @brief Fits content to window width
     */
    void fitToWidth();

    /**
     * @brief Prints the current documentation page
     */
    void printPage();

    /**
     * @brief Shows print preview dialog
     */
    void showPrintPreview();

signals:
    /**
     * @brief Emitted when a link is clicked
     * @param url The clicked URL
     */
    void linkClicked(const QUrl& url);

    /**
     * @brief Emitted when search results change
     * @param resultCount Number of search results found
     * @param currentResult Current result index (1-based)
     */
    void searchResultsChanged(int resultCount, int currentResult);

    /**
     * @brief Emitted when bookmarks are updated
     */
    void bookmarksChanged();

    /**
     * @brief Emitted when navigation history changes
     * @param canGoBack Whether back navigation is available
     * @param canGoForward Whether forward navigation is available
     */
    void navigationChanged(bool canGoBack, bool canGoForward);

private slots:
    void onSearchTextChanged();
    void onSearchOptionsChanged();
    void onTocItemClicked(QTreeWidgetItem* item, int column);
    void onBookmarkItemClicked(QListWidgetItem* item);
    void showBookmarkContextMenu(const QPoint& pos);
    void showHistoryMenu();
    void navigateToHistoryIndex(int index);
    void showFullHistoryDialog();
    void onZoomSliderChanged(int value);
    void onZoomComboChanged(const QString& text);
    void updateNavigationButtons();
    void onLinkHovered(const QString& link);

private:
    void setupUi();
    void setupToolbar();
    void setupSearchPanel();
    void setupTableOfContents();
    void setupBookmarksPanel();
    void setupContentArea();
    void setupStatusBar();
    void setupKeyboardShortcuts();
    
    void loadTableOfContents();
    void updateSearchHighlighting();
    void saveBookmarks();
    void loadBookmarks();
    void addToHistory(const QString& url);
    void updateZoomControls();
    QString generateDefaultDocumentation();
    
    // UI Components
    QSplitter* m_mainSplitter;
    QSplitter* m_leftSplitter;
    QStackedWidget* m_leftPanel;
    
    // Toolbar
    QToolBar* m_toolbar;
    ElaToolButton* m_backButton;
    ElaToolButton* m_forwardButton;
    ElaToolButton* m_homeButton;
    ElaToolButton* m_printButton;

    // Search panel
    QWidget* m_searchPanel;
    QLineEdit* m_searchEdit;
    ElaToolButton* m_searchButton;
    ElaToolButton* m_clearSearchButton;
    ElaToolButton* m_findNextButton;
    ElaToolButton* m_findPrevButton;
    QLabel* m_searchResultsLabel;

    // Table of contents
    QTreeWidget* m_tocTree;

    // Bookmarks panel
    QListWidget* m_bookmarksList;
    ElaToolButton* m_addBookmarkButton;
    ElaToolButton* m_removeBookmarkButton;

    // Content area
    QTextBrowser* m_contentBrowser;

    // Zoom controls
    QSlider* m_zoomSlider;
    QComboBox* m_zoomCombo;
    QLabel* m_zoomLabel;
    
    // Status bar
    QStatusBar* m_statusBar;
    QLabel* m_statusLabel;
    QProgressBar* m_loadingProgress;
    
    // Data members
    QUrl m_baseUrl;
    QStringList m_history;
    int m_historyIndex;
    QStringList m_bookmarks;
    QStringList m_searchResults;
    int m_currentSearchResult;
    int m_zoomLevel;
    QSettings* m_settings;
    DocumentationSyntaxHighlighter* m_syntaxHighlighter;
    
    // Search state
    QString m_currentSearchTerm;
    bool m_caseSensitive;
    bool m_wholeWords;
    
    // Constants
    static const int MIN_ZOOM = 50;
    static const int MAX_ZOOM = 300;
    static const int DEFAULT_ZOOM = 100;
    static const int ZOOM_STEP = 25;
};

#endif // DOCUMENTATIONVIEWER_H
