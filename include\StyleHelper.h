#ifndef STYLEHELPER_H
#define STYLEHELPER_H

#include <QWidget>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QTextEdit>
#include <QComboBox>
#include <QFrame>
#include <QScrollArea>
#include <QGroupBox>
#include <QTabWidget>

/**
 * @brief Utility class for applying consistent styling to UI components
 * 
 * This class provides static methods to apply design system styles
 * to various Qt widgets, ensuring visual consistency throughout the application.
 */
class StyleHelper
{
public:
    // Text styling
    static void applyDisplayText(QLabel* label, const QString& variant = "display-large");
    static void applyHeadingText(QLabel* label, const QString& variant = "heading-large");
    static void applyBodyText(QLabel* label, const QString& variant = "body-medium");
    static void applyCaptionText(QLabel* label);
    
    // Button styling
    static void applyPrimaryButton(QPushButton* button);
    static void applySecondaryButton(QPushButton* button);
    static void applyIconButton(QPushButton* button, int iconSize = 20);
    static void applyToolbarButton(QPushButton* button);
    
    // Input styling
    static void applyInputField(QLineEdit* input);
    static void applyTextArea(QTextEdit* textArea);
    static void applyComboBox(QComboBox* comboBox);
    
    // Container styling
    static void applyCard(QWidget* widget);
    static void applyPanel(QWidget* widget);
    static void applySection(QWidget* widget);
    static void applySidebar(QWidget* widget);
    
    // Layout styling
    static void applyToolbar(QWidget* toolbar);
    static void applyStatusBar(QWidget* statusBar);
    static void applyTabWidget(QTabWidget* tabWidget);
    static void applyScrollArea(QScrollArea* scrollArea);
    
    // Spacing utilities
    static void applyStandardMargins(QWidget* widget);
    static void applyCompactMargins(QWidget* widget);
    static void applyLargeMargins(QWidget* widget);
    static void applyNoMargins(QWidget* widget);
    
    // Visual hierarchy utilities
    static void applySeparator(QFrame* frame, bool horizontal = true);
    static void applyGroupBox(QGroupBox* groupBox);
    static void applyElevation(QWidget* widget, int level = 1);
    
    // State styling
    static void applyHoverEffect(QWidget* widget);
    static void applyFocusEffect(QWidget* widget);
    static void applyDisabledState(QWidget* widget);
    static void applyActiveState(QWidget* widget);
    
    // Color utilities
    static void applySuccessState(QWidget* widget);
    static void applyWarningState(QWidget* widget);
    static void applyErrorState(QWidget* widget);
    static void applyInfoState(QWidget* widget);
    
    // Animation utilities
    static void applyFadeTransition(QWidget* widget);
    static void applySlideTransition(QWidget* widget);
    static void applyScaleTransition(QWidget* widget);
    
    // Responsive utilities
    static void applyResponsiveLayout(QWidget* widget, int breakpoint = 768);
    static void applyMobileLayout(QWidget* widget);
    static void applyDesktopLayout(QWidget* widget);
    
private:
    StyleHelper() = delete; // Static class
    
    // Helper methods
    static QString buildStyleSheet(const QStringList& rules);
    static void setWidgetProperty(QWidget* widget, const QString& property, const QVariant& value);
    static void applyStyleWithTransition(QWidget* widget, const QString& styleSheet, int duration = 250);
};

#endif // STYLEHELPER_H
