#ifndef DOCUMENTINFODIALOG_H
#define DOCUMENTINFODIALOG_H

#include "ElaIntegration.h"
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QPushButton>
#include <QGroupBox>
#include <QScrollArea>
#include <QTextEdit>

class PdfController;

class DocumentInfoDialog : public ElaContentDialog
{
    Q_OBJECT

public:
    explicit DocumentInfoDialog(PdfController* controller, const QString& filePath, QWidget *parent = nullptr);

private slots:
    void copyToClipboard();

private:
    void setupUi();
    void populateInfo();
    QString formatFileSize(qint64 bytes);
    QString formatDateTime(const QDateTime& dateTime);

    PdfController* m_controller;
    QString m_filePath;
    
    // UI components
    QVBoxLayout* m_mainLayout;
    ElaScroll* m_scrollArea;
    QWidget* m_contentWidget;
    QGridLayout* m_gridLayout;

    // Info labels using Ela components
    ElaLabel* m_titleLabel;
    ElaLabel* m_authorLabel;
    ElaLabel* m_subjectLabel;
    ElaLabel* m_keywordsLabel;
    ElaLabel* m_creatorLabel;
    ElaLabel* m_producerLabel;
    ElaLabel* m_creationDateLabel;
    ElaLabel* m_modificationDateLabel;
    ElaLabel* m_filePathLabel;
    ElaLabel* m_fileSizeLabel;
    ElaLabel* m_pageCountLabel;
    ElaLabel* m_pageSizeLabel;
    ElaLabel* m_versionLabel;
    ElaLabel* m_encryptedLabel;
    ElaLabel* m_linearizedLabel;
};

#endif // DOCUMENTINFODIALOG_H
