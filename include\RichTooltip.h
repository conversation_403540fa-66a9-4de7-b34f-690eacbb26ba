#ifndef RICHTOOLTIP_H
#define RICHTOOLTIP_H

#include "ElaIntegration.h"
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPropertyAnimation>
#include <QTimer>

class RichTooltip : public QWidget
{
    Q_OBJECT

public:
    explicit RichTooltip(QWidget *parent = nullptr);
    ~RichTooltip();

    // Show tooltip with rich content
    static void showTooltip(const QPoint& position, const QString& title, 
                           const QString& description, const QString& shortcut = QString(),
                           ElaIconType::IconName icon = ElaIconType::None);
    
    // Hide current tooltip
    static void hideTooltip();
    
    // Set tooltip content
    void setContent(const QString& title, const QString& description, 
                   const QString& shortcut = QString(), 
                   ElaIconType::IconName icon = ElaIconType::None);

protected:
    void paintEvent(QPaintEvent *event) override;
    void showEvent(QShowEvent *event) override;
    void hideEvent(QHideEvent *event) override;
    bool eventFilter(QObject *obj, QEvent *event) override;

private slots:
    void fadeIn();
    void fadeOut();

private:
    void setupUI();
    void updatePosition(const QPoint& position);
    void updateTheme();
    
    // UI Components
    QVBoxLayout* m_mainLayout;
    QHBoxLayout* m_headerLayout;
    ElaLabel* m_iconLabel;
    ElaText* m_titleLabel;
    ElaText* m_descriptionLabel;
    ElaText* m_shortcutLabel;
    
    // Animation
    QPropertyAnimation* m_fadeAnimation;
    QTimer* m_showTimer;
    QTimer* m_hideTimer;
    
    // State
    ElaThemeType::ThemeMode m_themeMode;
    QString m_currentTitle;
    QString m_currentDescription;
    QString m_currentShortcut;
    ElaIconType::IconName m_currentIcon;
    
    // Static instance for global tooltip
    static RichTooltip* s_instance;
    
    // Styling constants
    static const int TOOLTIP_RADIUS = 8;
    static const int TOOLTIP_PADDING = 12;
    static const int ICON_SIZE = 20;
    static const int MAX_WIDTH = 300;
    static const int SHOW_DELAY = 500;
    static const int HIDE_DELAY = 100;
};

#endif // RICHTOOLTIP_H
