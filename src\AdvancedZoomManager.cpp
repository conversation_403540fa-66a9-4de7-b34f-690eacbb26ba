/**
 * @file AdvancedZoomManager.cpp
 * @brief Implementation of advanced zoom management for PDF documents
 */

#include "AdvancedZoomManager.h"
#include "DocumentTab.h"
#include "PdfController.h"
#include <QApplication>
#include <QMouseEvent>
#include <QVBoxLayout>
#include <QFrame>
#include <QGraphicsDropShadowEffect>
#include <QScreen>
#include <QDebug>
#include <QtMath>
#include <QRegularExpression>

AdvancedZoomManager::AdvancedZoomManager(QObject* parent)
    : QObject(parent)
    , m_documentTab(nullptr)
    , m_pdfController(nullptr)
    , m_pageLabel(nullptr)
    , m_scrollArea(nullptr)
    , m_currentZoom(1.0)
    , m_zoomMode(ZoomMode::Manual)
    , m_zoomHistoryIndex(-1)
    , m_zoomAnimation(nullptr)
    , m_animationDuration(DEFAULT_ANIMATION_DURATION)
    , m_animationEasing(QEasingCurve::OutCubic)
    , m_magnifierEnabled(false)
    , m_magnifierWidget(nullptr)
    , m_magnifierLabel(nullptr)
    , m_magnifierZoom(DEFAULT_MAGNIFIER_ZOOM)
    , m_magnifierSize(150, 150)
    , m_magnifierTimer(new QTimer(this))
{
    // Setup zoom presets
    m_zoomPresets["Fit to Window"] = -1.0; // Special value for fit modes
    m_zoomPresets["Fit to Width"] = -2.0;
    m_zoomPresets["Actual Size"] = 1.0;
    m_zoomPresets["125%"] = 1.25;
    m_zoomPresets["150%"] = 1.5;
    m_zoomPresets["200%"] = 2.0;
    m_zoomPresets["400%"] = 4.0;

    // Setup magnifier timer
    m_magnifierTimer->setSingleShot(false);
    m_magnifierTimer->setInterval(MAGNIFIER_UPDATE_INTERVAL);
    connect(m_magnifierTimer, &QTimer::timeout, this, &AdvancedZoomManager::onMagnifierTimer);

    // Create zoom animation
    m_zoomAnimation = new QPropertyAnimation(this);
    m_zoomAnimation->setDuration(m_animationDuration);
    m_zoomAnimation->setEasingCurve(m_animationEasing);
    connect(m_zoomAnimation, &QPropertyAnimation::finished, this, &AdvancedZoomManager::onAnimationFinished);
}

AdvancedZoomManager::~AdvancedZoomManager()
{
    if (m_magnifierWidget) {
        m_magnifierWidget->deleteLater();
    }
}

void AdvancedZoomManager::setDocumentTab(DocumentTab* tab)
{
    m_documentTab = tab;
    if (tab) {
        m_currentZoom = tab->getZoomFactor();
    }
}

void AdvancedZoomManager::setPdfController(PdfController* controller)
{
    m_pdfController = controller;
}

void AdvancedZoomManager::setPageLabel(QLabel* pageLabel)
{
    if (m_pageLabel) {
        m_pageLabel->removeEventFilter(this);
    }
    
    m_pageLabel = pageLabel;
    
    if (m_pageLabel) {
        m_pageLabel->installEventFilter(this);
    }
}

void AdvancedZoomManager::setScrollArea(QScrollArea* scrollArea)
{
    m_scrollArea = scrollArea;
}

void AdvancedZoomManager::setZoomFactor(double factor, bool animated)
{
    factor = qBound(MIN_ZOOM, factor, MAX_ZOOM);
    
    if (qAbs(factor - m_currentZoom) < 0.001) {
        return; // No significant change
    }

    // Add to zoom history
    if (m_zoomHistoryIndex < 0 || qAbs(m_zoomHistory[m_zoomHistoryIndex] - m_currentZoom) > 0.01) {
        // Remove any forward history
        while (m_zoomHistory.size() > m_zoomHistoryIndex + 1) {
            m_zoomHistory.removeLast();
        }
        
        m_zoomHistory.append(m_currentZoom);
        m_zoomHistoryIndex = m_zoomHistory.size() - 1;
        
        // Limit history size
        if (m_zoomHistory.size() > 20) {
            m_zoomHistory.removeFirst();
            m_zoomHistoryIndex--;
        }
    }

    if (animated && m_documentTab) {
        animateZoomTo(factor);
    } else {
        m_currentZoom = factor;
        if (m_documentTab) {
            m_documentTab->setZoomFactor(factor);
        }
        emit zoomChanged(factor);
    }
}

double AdvancedZoomManager::getZoomFactor() const
{
    return m_currentZoom;
}

void AdvancedZoomManager::setZoomMode(ZoomMode mode)
{
    if (m_zoomMode != mode) {
        m_zoomMode = mode;
        emit zoomModeChanged(mode);
    }
}

ZoomMode AdvancedZoomManager::getZoomMode() const
{
    return m_zoomMode;
}

void AdvancedZoomManager::performSmartZoom(SmartZoomType type)
{
    if (!m_documentTab || !m_pdfController) {
        return;
    }

    double optimalZoom = calculateOptimalZoom(type);
    QString reason;

    switch (type) {
    case SmartZoomType::ContentAware:
        reason = tr("Optimized for content density");
        break;
    case SmartZoomType::TextOptimal:
        reason = tr("Optimized for text reading");
        break;
    case SmartZoomType::ImageOptimal:
        reason = tr("Optimized for image viewing");
        break;
    case SmartZoomType::Adaptive:
        reason = tr("Adaptive zoom based on content");
        break;
    }

    emit smartZoomSuggestion(optimalZoom, reason);
    setZoomFactor(optimalZoom, true);
}

double AdvancedZoomManager::calculateOptimalZoom(SmartZoomType type) const
{
    if (!m_documentTab || !m_pdfController) {
        return 1.0;
    }

    int currentPage = m_documentTab->getCurrentPage();
    
    switch (type) {
    case SmartZoomType::ContentAware: {
        double contentDensity = analyzeContentDensity(currentPage);
        // Higher content density suggests lower zoom for overview
        return qBound(0.5, 2.0 - contentDensity, 2.0);
    }
    case SmartZoomType::TextOptimal: {
        double textDensity = analyzeTextDensity(currentPage);
        // Optimize for comfortable text reading (typically 120-150% zoom)
        return qBound(1.0, 1.2 + textDensity * 0.3, 1.8);
    }
    case SmartZoomType::ImageOptimal: {
        double imageDensity = analyzeImageDensity(currentPage);
        // Images often benefit from higher zoom for detail
        return qBound(1.0, 1.5 + imageDensity * 0.5, 3.0);
    }
    case SmartZoomType::Adaptive: {
        double textDensity = analyzeTextDensity(currentPage);
        double imageDensity = analyzeImageDensity(currentPage);
        
        if (textDensity > imageDensity) {
            return calculateOptimalZoom(SmartZoomType::TextOptimal);
        } else {
            return calculateOptimalZoom(SmartZoomType::ImageOptimal);
        }
    }
    }
    
    return 1.0;
}

void AdvancedZoomManager::suggestZoomLevel()
{
    performSmartZoom(SmartZoomType::Adaptive);
}

void AdvancedZoomManager::enableMagnifier(bool enabled)
{
    m_magnifierEnabled = enabled;
    
    if (enabled) {
        if (!m_magnifierWidget) {
            createMagnifierWidget();
        }
    } else {
        hideMagnifier();
    }
}

bool AdvancedZoomManager::isMagnifierEnabled() const
{
    return m_magnifierEnabled;
}

void AdvancedZoomManager::setMagnifierZoom(double factor)
{
    m_magnifierZoom = qBound(1.5, factor, 5.0);
}

void AdvancedZoomManager::setMagnifierSize(const QSize& size)
{
    m_magnifierSize = size;
    if (m_magnifierWidget) {
        m_magnifierWidget->resize(size);
    }
}

void AdvancedZoomManager::zoomToRect(const QRectF& rect, bool animated)
{
    if (!m_scrollArea || rect.isEmpty()) {
        return;
    }

    QSize viewportSize = m_scrollArea->viewport()->size();
    double widthRatio = viewportSize.width() / rect.width();
    double heightRatio = viewportSize.height() / rect.height();
    
    double targetZoom = qMin(widthRatio, heightRatio) * m_currentZoom * 0.9; // 90% to add some margin
    setZoomFactor(targetZoom, animated);
}

void AdvancedZoomManager::zoomToPoint(const QPointF& point, double factor, bool animated)
{
    Q_UNUSED(point); // TODO: Implement point-centered zooming
    setZoomFactor(factor, animated);
}

void AdvancedZoomManager::zoomToSelection(const QRectF& selection)
{
    zoomToRect(selection, true);
}

void AdvancedZoomManager::addZoomPreset(double factor, const QString& name)
{
    m_zoomPresets[name] = factor;
}

void AdvancedZoomManager::applyZoomPreset(const QString& name)
{
    if (m_zoomPresets.contains(name)) {
        double factor = m_zoomPresets[name];
        
        if (factor == -1.0) {
            zoomToFit();
        } else if (factor == -2.0) {
            zoomToWidth();
        } else {
            setZoomFactor(factor, true);
        }
    }
}

QStringList AdvancedZoomManager::getZoomPresets() const
{
    return m_zoomPresets.keys();
}

void AdvancedZoomManager::clearZoomHistory()
{
    m_zoomHistory.clear();
    m_zoomHistoryIndex = -1;
}

bool AdvancedZoomManager::canZoomBack() const
{
    return m_zoomHistoryIndex > 0;
}

bool AdvancedZoomManager::canZoomForward() const
{
    return m_zoomHistoryIndex >= 0 && m_zoomHistoryIndex < m_zoomHistory.size() - 1;
}

void AdvancedZoomManager::setAnimationDuration(int milliseconds)
{
    m_animationDuration = milliseconds;
    if (m_zoomAnimation) {
        m_zoomAnimation->setDuration(milliseconds);
    }
}

void AdvancedZoomManager::setAnimationEasing(QEasingCurve::Type easing)
{
    m_animationEasing = easing;
    if (m_zoomAnimation) {
        m_zoomAnimation->setEasingCurve(easing);
    }
}

// Public slots
void AdvancedZoomManager::zoomIn(bool animated)
{
    double currentZoom = getZoomFactor();
    double newZoom;

    // Smart zoom increments based on current level
    if (currentZoom < 0.5) {
        newZoom = currentZoom * 1.5;
    } else if (currentZoom < 1.0) {
        newZoom = currentZoom * 1.3;
    } else if (currentZoom < 2.0) {
        newZoom = currentZoom * 1.25;
    } else {
        newZoom = currentZoom * 1.2;
    }

    setZoomFactor(newZoom, animated);
}

void AdvancedZoomManager::zoomOut(bool animated)
{
    double currentZoom = getZoomFactor();
    double newZoom;

    // Smart zoom decrements based on current level
    if (currentZoom <= 0.5) {
        newZoom = currentZoom / 1.5;
    } else if (currentZoom <= 1.0) {
        newZoom = currentZoom / 1.3;
    } else if (currentZoom <= 2.0) {
        newZoom = currentZoom / 1.25;
    } else {
        newZoom = currentZoom / 1.2;
    }

    setZoomFactor(newZoom, animated);
}

void AdvancedZoomManager::zoomToFit()
{
    setZoomMode(ZoomMode::FitToWindow);
    double fitZoom = calculateFitToWindowZoom();
    setZoomFactor(fitZoom, true);
}

void AdvancedZoomManager::zoomToWidth()
{
    setZoomMode(ZoomMode::FitToWidth);
    double fitZoom = calculateFitToWidthZoom();
    setZoomFactor(fitZoom, true);
}

void AdvancedZoomManager::zoomToHeight()
{
    setZoomMode(ZoomMode::FitToHeight);
    double fitZoom = calculateFitToHeightZoom();
    setZoomFactor(fitZoom, true);
}

void AdvancedZoomManager::zoomToActualSize()
{
    setZoomMode(ZoomMode::ActualSize);
    setZoomFactor(1.0, true);
}

void AdvancedZoomManager::zoomBack()
{
    if (canZoomBack()) {
        m_zoomHistoryIndex--;
        double targetZoom = m_zoomHistory[m_zoomHistoryIndex];
        m_currentZoom = targetZoom;
        if (m_documentTab) {
            m_documentTab->setZoomFactor(targetZoom);
        }
        emit zoomChanged(targetZoom);
    }
}

void AdvancedZoomManager::zoomForward()
{
    if (canZoomForward()) {
        m_zoomHistoryIndex++;
        double targetZoom = m_zoomHistory[m_zoomHistoryIndex];
        m_currentZoom = targetZoom;
        if (m_documentTab) {
            m_documentTab->setZoomFactor(targetZoom);
        }
        emit zoomChanged(targetZoom);
    }
}

bool AdvancedZoomManager::eventFilter(QObject* object, QEvent* event)
{
    if (object == m_pageLabel && m_magnifierEnabled) {
        if (event->type() == QEvent::MouseMove) {
            QMouseEvent* mouseEvent = static_cast<QMouseEvent*>(event);
            m_magnifierPosition = mouseEvent->position();
            updateMagnifier();
            return false; // Don't consume the event
        } else if (event->type() == QEvent::Leave) {
            hideMagnifier();
        }
    }

    return QObject::eventFilter(object, event);
}

// Private slots
void AdvancedZoomManager::onAnimationFinished()
{
    // Animation completed
}

void AdvancedZoomManager::onMagnifierTimer()
{
    updateMagnifier();
}

void AdvancedZoomManager::updateMagnifier()
{
    if (!m_magnifierEnabled || !m_magnifierWidget || !m_pageLabel) {
        return;
    }

    showMagnifier(m_magnifierPosition);
    updateMagnifierContent(m_magnifierPosition);
}

// Private methods - Content analysis
double AdvancedZoomManager::analyzeContentDensity(int pageNumber) const
{
    if (!m_pdfController) {
        return 0.5; // Default medium density
    }

    // Simple heuristic: analyze text length vs page size
    QString pageText = m_pdfController->getPageText(pageNumber);
    QSizeF pageSize = m_pdfController->getPageSize(pageNumber);

    if (pageSize.isEmpty() || pageText.isEmpty()) {
        return 0.3; // Low density for empty or image-only pages
    }

    // Calculate text density (characters per square inch)
    double pageArea = (pageSize.width() / 72.0) * (pageSize.height() / 72.0); // Convert to square inches
    double textDensity = pageText.length() / pageArea;

    // Normalize to 0-1 range (typical range is 100-2000 chars per sq inch)
    return qBound(0.0, textDensity / 1000.0, 1.0);
}

double AdvancedZoomManager::analyzeTextDensity(int pageNumber) const
{
    if (!m_pdfController) {
        return 0.5;
    }

    QString pageText = m_pdfController->getPageText(pageNumber);
    if (pageText.isEmpty()) {
        return 0.0;
    }

    // Count words and lines to estimate text layout
    QStringList words = pageText.split(QRegularExpression("\\s+"), Qt::SkipEmptyParts);
    QStringList lines = pageText.split('\n', Qt::SkipEmptyParts);

    if (lines.isEmpty()) {
        return 0.3;
    }

    double avgWordsPerLine = static_cast<double>(words.size()) / lines.size();

    // Normalize: 5-15 words per line is typical for readable text
    return qBound(0.0, (avgWordsPerLine - 5.0) / 10.0, 1.0);
}

double AdvancedZoomManager::analyzeImageDensity(int pageNumber) const
{
    Q_UNUSED(pageNumber);

    // Simplified heuristic: if text density is low, assume high image density
    double textDensity = analyzeTextDensity(pageNumber);
    return qBound(0.0, 1.0 - textDensity, 1.0);
}

QRectF AdvancedZoomManager::findContentBounds(int pageNumber) const
{
    if (!m_pdfController) {
        return QRectF();
    }

    QSizeF pageSize = m_pdfController->getPageSize(pageNumber);
    if (pageSize.isEmpty()) {
        return QRectF();
    }

    // Simple heuristic: assume content is in the center 80% of the page
    double margin = 0.1;
    return QRectF(
        pageSize.width() * margin,
        pageSize.height() * margin,
        pageSize.width() * (1.0 - 2 * margin),
        pageSize.height() * (1.0 - 2 * margin)
    );
}

// Zoom calculations
double AdvancedZoomManager::calculateFitToWindowZoom() const
{
    if (!m_scrollArea || !m_documentTab) {
        return 1.0;
    }

    QSizeF pageSize = m_pdfController->getPageSize(m_documentTab->getCurrentPage());
    if (pageSize.isEmpty()) {
        return 1.0;
    }

    QSize viewportSize = m_scrollArea->viewport()->size();
    double widthRatio = viewportSize.width() / pageSize.width();
    double heightRatio = viewportSize.height() / pageSize.height();

    return qMin(widthRatio, heightRatio) * 0.95; // 95% to add some margin
}

double AdvancedZoomManager::calculateFitToWidthZoom() const
{
    if (!m_scrollArea || !m_documentTab) {
        return 1.0;
    }

    QSizeF pageSize = m_pdfController->getPageSize(m_documentTab->getCurrentPage());
    if (pageSize.isEmpty()) {
        return 1.0;
    }

    QSize viewportSize = m_scrollArea->viewport()->size();
    return (viewportSize.width() / pageSize.width()) * 0.95; // 95% to add some margin
}

double AdvancedZoomManager::calculateFitToHeightZoom() const
{
    if (!m_scrollArea || !m_documentTab) {
        return 1.0;
    }

    QSizeF pageSize = m_pdfController->getPageSize(m_documentTab->getCurrentPage());
    if (pageSize.isEmpty()) {
        return 1.0;
    }

    QSize viewportSize = m_scrollArea->viewport()->size();
    return (viewportSize.height() / pageSize.height()) * 0.95; // 95% to add some margin
}

double AdvancedZoomManager::calculateSmartFitZoom() const
{
    // Analyze content and choose best fit mode
    if (!m_documentTab) {
        return 1.0;
    }

    int currentPage = m_documentTab->getCurrentPage();
    double textDensity = analyzeTextDensity(currentPage);

    if (textDensity > 0.6) {
        // High text density - fit to width for better reading
        return calculateFitToWidthZoom();
    } else {
        // Low text density or image-heavy - fit to window
        return calculateFitToWindowZoom();
    }
}

// Animation helpers
void AdvancedZoomManager::animateZoomTo(double targetZoom)
{
    if (!m_zoomAnimation || !m_documentTab) {
        return;
    }

    if (m_zoomAnimation->state() == QPropertyAnimation::Running) {
        m_zoomAnimation->stop();
    }

    m_zoomAnimation->setStartValue(m_currentZoom);
    m_zoomAnimation->setEndValue(targetZoom);

    // Custom property animation for zoom
    connect(m_zoomAnimation, &QPropertyAnimation::valueChanged, this, [this](const QVariant& value) {
        double zoom = value.toDouble();
        m_currentZoom = zoom;
        if (m_documentTab) {
            m_documentTab->setZoomFactor(zoom);
        }
        emit zoomChanged(zoom);
    });

    m_zoomAnimation->start();
}

void AdvancedZoomManager::updateZoomWithAnimation(double factor)
{
    animateZoomTo(factor);
}

// Magnifier helpers
void AdvancedZoomManager::createMagnifierWidget()
{
    if (m_magnifierWidget) {
        return;
    }

    // Create magnifier widget
    m_magnifierWidget = new QWidget(m_pageLabel ? m_pageLabel->parentWidget() : nullptr);
    m_magnifierWidget->setWindowFlags(Qt::ToolTip | Qt::FramelessWindowHint);
    m_magnifierWidget->setAttribute(Qt::WA_TranslucentBackground);
    m_magnifierWidget->resize(m_magnifierSize);

    // Create layout
    QVBoxLayout* layout = new QVBoxLayout(m_magnifierWidget);
    layout->setContentsMargins(5, 5, 5, 5);

    // Create frame
    QFrame* frame = new QFrame();
    frame->setFrameStyle(QFrame::Box | QFrame::Raised);
    frame->setLineWidth(2);
    frame->setStyleSheet("QFrame { background-color: white; border: 2px solid #333; border-radius: 8px; }");

    // Create label for magnified content
    m_magnifierLabel = new QLabel();
    m_magnifierLabel->setAlignment(Qt::AlignCenter);
    m_magnifierLabel->setScaledContents(true);

    QVBoxLayout* frameLayout = new QVBoxLayout(frame);
    frameLayout->setContentsMargins(2, 2, 2, 2);
    frameLayout->addWidget(m_magnifierLabel);

    layout->addWidget(frame);

    // Add drop shadow effect
    QGraphicsDropShadowEffect* shadow = new QGraphicsDropShadowEffect();
    shadow->setBlurRadius(10);
    shadow->setColor(QColor(0, 0, 0, 100));
    shadow->setOffset(3, 3);
    m_magnifierWidget->setGraphicsEffect(shadow);

    m_magnifierWidget->hide();
}

void AdvancedZoomManager::showMagnifier(const QPointF& position)
{
    if (!m_magnifierWidget || !m_pageLabel) {
        return;
    }

    // Position magnifier near cursor but avoid screen edges
    QPoint globalPos = m_pageLabel->mapToGlobal(position.toPoint());
    QScreen* screen = QApplication::screenAt(globalPos);
    if (!screen) {
        return;
    }

    QRect screenGeometry = screen->geometry();
    QPoint magnifierPos = globalPos + QPoint(20, 20); // Offset from cursor

    // Adjust position to keep magnifier on screen
    if (magnifierPos.x() + m_magnifierSize.width() > screenGeometry.right()) {
        magnifierPos.setX(globalPos.x() - m_magnifierSize.width() - 20);
    }
    if (magnifierPos.y() + m_magnifierSize.height() > screenGeometry.bottom()) {
        magnifierPos.setY(globalPos.y() - m_magnifierSize.height() - 20);
    }

    m_magnifierWidget->move(magnifierPos);
    m_magnifierWidget->show();
    m_magnifierWidget->raise();
}

void AdvancedZoomManager::hideMagnifier()
{
    if (m_magnifierWidget) {
        m_magnifierWidget->hide();
    }
}

void AdvancedZoomManager::updateMagnifierContent(const QPointF& position)
{
    if (!m_magnifierLabel || !m_pageLabel || !m_documentTab) {
        return;
    }

    // Get the current page pixmap
    QPixmap pagePixmap = m_pageLabel->pixmap();
    if (pagePixmap.isNull()) {
        return;
    }

    // Calculate the area to magnify
    int magnifyRadius = 30; // Radius of area to magnify
    QRect sourceRect(
        position.x() - magnifyRadius,
        position.y() - magnifyRadius,
        magnifyRadius * 2,
        magnifyRadius * 2
    );

    // Ensure source rect is within pixmap bounds
    sourceRect = sourceRect.intersected(pagePixmap.rect());

    if (sourceRect.isEmpty()) {
        return;
    }

    // Extract and scale the region
    QPixmap magnifiedPixmap = pagePixmap.copy(sourceRect);
    QSize targetSize = m_magnifierLabel->size();
    magnifiedPixmap = magnifiedPixmap.scaled(targetSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);

    m_magnifierLabel->setPixmap(magnifiedPixmap);
}
