/**
 * @file TextSelectionOverlay.h
 * @brief Text selection overlay for PDF documents
 * <AUTHOR> Viewer Team
 * @date 2024-12-01
 * @version 1.0.0
 *
 * This file contains the TextSelectionOverlay class which handles text selection
 * functionality including mouse-based text selection, copy to clipboard, and
 * visual selection highlighting.
 */

#ifndef TEXTSELECTIONOVERLAY_H
#define TEXTSELECTIONOVERLAY_H

#include <QWidget>
#include <QLabel>
#include <QRectF>
#include <QPointF>
#include <QList>
#include <QClipboard>
#include <QApplication>
#include <QMouseEvent>
#include <QPainter>
#include <QTimer>
#include <memory>

class PdfController;
class TestTextSelection;  // Forward declaration for friend access

/**
 * @brief Text selection data structure
 */
struct TextSelection {
    int pageNumber;
    QPointF startPoint;     // PDF coordinates
    QPointF endPoint;       // PDF coordinates
    QList<QRectF> rects;    // Selection rectangles in PDF coordinates
    QString text;           // Selected text content
    bool isValid() const { return !text.isEmpty() && !rects.isEmpty(); }
};

/**
 * @brief Overlay widget for handling text selection in PDF documents
 *
 * The TextSelectionOverlay provides text selection functionality including:
 * - Mouse-based text selection with visual feedback
 * - Copy selected text to clipboard
 * - Multi-line text selection support
 * - Integration with existing PDF rendering system
 *
 * @details
 * Key features include:
 * - **Visual Selection**: Real-time selection highlighting during mouse drag
 * - **Text Extraction**: Accurate text extraction from selected regions
 * - **Clipboard Integration**: Copy selected text to system clipboard
 * - **Multi-line Support**: Handle text selection across multiple lines
 * - **Coordinate Conversion**: Proper conversion between screen and PDF coordinates
 *
 * The overlay integrates with the existing DocumentTab and PdfController
 * architecture without disrupting annotation functionality.
 */
class TextSelectionOverlay : public QWidget
{
    Q_OBJECT
    
    friend class TestTextSelection;  // Allow test class to access private members

public:
    explicit TextSelectionOverlay(QWidget* parent = nullptr);
    ~TextSelectionOverlay();

    // Configuration
    void setPageLabel(QLabel* pageLabel);
    void setPdfController(PdfController* controller);
    void setCurrentPage(int pageNumber);
    void setZoomFactor(double zoomFactor);
    void setDpi(double dpi);

    // Selection management
    void clearSelection();
    bool hasSelection() const;
    QString getSelectedText() const;
    void copySelectionToClipboard();

    // Enable/disable text selection mode
    void setTextSelectionEnabled(bool enabled);
    bool isTextSelectionEnabled() const { return m_textSelectionEnabled; }

signals:
    void selectionChanged(const QString& selectedText);
    void selectionCleared();

protected:
    // Event handlers
    void paintEvent(QPaintEvent* event) override;
    void mousePressEvent(QMouseEvent* event) override;
    void mouseMoveEvent(QMouseEvent* event) override;
    void mouseReleaseEvent(QMouseEvent* event) override;
    void keyPressEvent(QKeyEvent* event) override;

private slots:
    void onSelectionTimer();

private:
    // Coordinate conversion
    QPointF pixelToPdfCoordinates(const QPointF& pixelPoint) const;
    QPointF pdfToPixelCoordinates(const QPointF& pdfPoint) const;
    QRectF pixelToPdfRect(const QRectF& pixelRect) const;
    QRectF pdfToPixelRect(const QRectF& pdfRect) const;

    // Text selection logic
    void startSelection(const QPointF& point);
    void updateSelection(const QPointF& point);
    void finishSelection();
    void extractSelectedText();
    QList<QRectF> getTextRectsInRegion(const QRectF& region) const;

    // Rendering
    void drawSelection(QPainter* painter);
    void drawSelectionRect(QPainter* painter, const QRectF& rect);

    // Member variables
    QLabel* m_pageLabel;
    PdfController* m_pdfController;
    int m_currentPageNumber;
    double m_zoomFactor;
    double m_dpi;

    // Selection state
    bool m_textSelectionEnabled;
    bool m_isSelecting;
    QPointF m_selectionStart;
    QPointF m_selectionEnd;
    TextSelection m_currentSelection;

    // UI state
    QTimer* m_selectionTimer;
    QClipboard* m_clipboard;

    // Constants
    static constexpr double DEFAULT_DPI = 72.0;
    static constexpr int SELECTION_TIMER_INTERVAL = 50; // ms
};

#endif // TEXTSELECTIONOVERLAY_H
