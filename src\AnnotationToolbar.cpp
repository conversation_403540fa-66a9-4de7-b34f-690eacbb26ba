#include "AnnotationToolbar.h"
#include <QToolButton>
#include <QButtonGroup>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QColorDialog>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QSlider>
#include <QLabel>
#include <QFrame>
#include <QIcon>
#include <QPixmap>
#include <QPainter>
#include <QApplication>
#include <QStyle>
#include <QShortcut>

AnnotationToolbar::AnnotationToolbar(QWidget *parent)
    : QWidget(parent)
{
    setObjectName("AnnotationToolbar");
    createToolButtons();
    createPropertyControls();
    setupKeyboardShortcuts();
    
    // Main layout
    QHBoxLayout* mainLayout = new QHBoxLayout(this);
    mainLayout->setContentsMargins(5, 5, 5, 5);
    mainLayout->setSpacing(5);
    
    // Tool buttons section
    QFrame* toolsFrame = new QFrame();
    toolsFrame->setFrameStyle(QFrame::StyledPanel);
    QHBoxLayout* toolsLayout = new QHBoxLayout(toolsFrame);
    toolsLayout->setContentsMargins(5, 5, 5, 5);
    toolsLayout->setSpacing(2);
    
    toolsLayout->addWidget(m_selectButton);
    toolsLayout->addWidget(new QFrame()); // Separator
    toolsLayout->addWidget(m_highlightButton);
    toolsLayout->addWidget(m_noteButton);
    toolsLayout->addWidget(m_drawingButton);
    toolsLayout->addWidget(m_rectangleButton);
    toolsLayout->addWidget(m_circleButton);
    toolsLayout->addWidget(m_arrowButton);
    toolsLayout->addWidget(m_textButton);
    
    // Properties section
    QFrame* propertiesFrame = new QFrame();
    propertiesFrame->setFrameStyle(QFrame::StyledPanel);
    QHBoxLayout* propertiesLayout = new QHBoxLayout(propertiesFrame);
    propertiesLayout->setContentsMargins(5, 5, 5, 5);
    propertiesLayout->setSpacing(5);
    
    propertiesLayout->addWidget(new QLabel("Color:"));
    propertiesLayout->addWidget(m_colorButton);
    propertiesLayout->addWidget(m_opacityLabel);
    propertiesLayout->addWidget(m_opacitySlider);
    propertiesLayout->addWidget(m_lineWidthLabel);
    propertiesLayout->addWidget(m_lineWidthSpinBox);
    
    // Actions section
    QFrame* actionsFrame = new QFrame();
    actionsFrame->setFrameStyle(QFrame::StyledPanel);
    QHBoxLayout* actionsLayout = new QHBoxLayout(actionsFrame);
    actionsLayout->setContentsMargins(5, 5, 5, 5);
    actionsLayout->setSpacing(2);
    
    actionsLayout->addWidget(m_deleteButton);
    actionsLayout->addWidget(m_copyButton);
    actionsLayout->addWidget(m_pasteButton);
    
    // Add all sections to main layout
    mainLayout->addWidget(toolsFrame);
    mainLayout->addWidget(propertiesFrame);
    mainLayout->addWidget(actionsFrame);
    mainLayout->addStretch();
    
    // Set initial state
    updateToolSelection();
    updateColorButton();
}

AnnotationToolbar::~AnnotationToolbar()
{
    delete m_colorDialog;
}

void AnnotationToolbar::createToolButtons()
{
    // Create button group for exclusive selection
    m_toolButtonGroup = new QButtonGroup(this);
    m_toolButtonGroup->setExclusive(true);
    
    // Select tool
    m_selectButton = new QToolButton();
    m_selectButton->setText("Select");
    m_selectButton->setToolTip("Select and move annotations");
    m_selectButton->setCheckable(true);
    m_selectButton->setChecked(true);
    m_toolButtonGroup->addButton(m_selectButton, static_cast<int>(AnnotationTool::Select));
    
    // Highlight tool
    m_highlightButton = new QToolButton();
    m_highlightButton->setText("Highlight");
    m_highlightButton->setToolTip("Highlight text");
    m_highlightButton->setCheckable(true);
    m_toolButtonGroup->addButton(m_highlightButton, static_cast<int>(AnnotationTool::Highlight));
    
    // Note tool
    m_noteButton = new QToolButton();
    m_noteButton->setText("Note");
    m_noteButton->setToolTip("Add sticky note");
    m_noteButton->setCheckable(true);
    m_toolButtonGroup->addButton(m_noteButton, static_cast<int>(AnnotationTool::Note));
    
    // Drawing tool
    m_drawingButton = new QToolButton();
    m_drawingButton->setText("Draw");
    m_drawingButton->setToolTip("Free-hand drawing");
    m_drawingButton->setCheckable(true);
    m_toolButtonGroup->addButton(m_drawingButton, static_cast<int>(AnnotationTool::Drawing));
    
    // Rectangle tool
    m_rectangleButton = new QToolButton();
    m_rectangleButton->setText("Rectangle");
    m_rectangleButton->setToolTip("Draw rectangle");
    m_rectangleButton->setCheckable(true);
    m_toolButtonGroup->addButton(m_rectangleButton, static_cast<int>(AnnotationTool::Rectangle));
    
    // Circle tool
    m_circleButton = new QToolButton();
    m_circleButton->setText("Circle");
    m_circleButton->setToolTip("Draw circle/ellipse");
    m_circleButton->setCheckable(true);
    m_toolButtonGroup->addButton(m_circleButton, static_cast<int>(AnnotationTool::Circle));
    
    // Arrow tool
    m_arrowButton = new QToolButton();
    m_arrowButton->setText("Arrow");
    m_arrowButton->setToolTip("Draw arrow");
    m_arrowButton->setCheckable(true);
    m_toolButtonGroup->addButton(m_arrowButton, static_cast<int>(AnnotationTool::Arrow));
    
    // Text tool
    m_textButton = new QToolButton();
    m_textButton->setText("Text");
    m_textButton->setToolTip("Add text box");
    m_textButton->setCheckable(true);
    m_toolButtonGroup->addButton(m_textButton, static_cast<int>(AnnotationTool::Text));
    
    // Connect signals
    connect(m_toolButtonGroup, QOverload<QAbstractButton*>::of(&QButtonGroup::buttonClicked),
            this, &AnnotationToolbar::onToolButtonClicked);
}

void AnnotationToolbar::createPropertyControls()
{
    // Color button
    m_colorButton = new QToolButton();
    m_colorButton->setFixedSize(30, 25);
    m_colorButton->setToolTip("Choose annotation color");
    connect(m_colorButton, &QToolButton::clicked,
            this, &AnnotationToolbar::onColorButtonClicked);
    
    // Opacity slider
    m_opacityLabel = new QLabel("Opacity:");
    m_opacitySlider = new QSlider(Qt::Horizontal);
    m_opacitySlider->setRange(10, 100);
    m_opacitySlider->setValue(static_cast<int>(m_currentOpacity * 100));
    m_opacitySlider->setFixedWidth(80);
    m_opacitySlider->setToolTip("Annotation opacity");
    connect(m_opacitySlider, &QSlider::valueChanged,
            this, &AnnotationToolbar::onOpacityChanged);
    
    // Line width spinbox
    m_lineWidthLabel = new QLabel("Width:");
    m_lineWidthSpinBox = new QDoubleSpinBox();
    m_lineWidthSpinBox->setRange(0.5, 10.0);
    m_lineWidthSpinBox->setSingleStep(0.5);
    m_lineWidthSpinBox->setValue(m_currentLineWidth);
    m_lineWidthSpinBox->setFixedWidth(60);
    m_lineWidthSpinBox->setToolTip("Line width for drawings and shapes");
    connect(m_lineWidthSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &AnnotationToolbar::onLineWidthChanged);
    
    // Action buttons
    m_deleteButton = new QToolButton();
    m_deleteButton->setText("Delete");
    m_deleteButton->setToolTip("Delete selected annotations");
    connect(m_deleteButton, &QToolButton::clicked,
            this, &AnnotationToolbar::onDeleteClicked);
    
    m_copyButton = new QToolButton();
    m_copyButton->setText("Copy");
    m_copyButton->setToolTip("Copy selected annotations");
    connect(m_copyButton, &QToolButton::clicked,
            this, &AnnotationToolbar::onCopyClicked);
    
    m_pasteButton = new QToolButton();
    m_pasteButton->setText("Paste");
    m_pasteButton->setToolTip("Paste annotations");
    connect(m_pasteButton, &QToolButton::clicked,
            this, &AnnotationToolbar::onPasteClicked);
}

void AnnotationToolbar::setCurrentTool(AnnotationTool tool)
{
    if (m_currentTool != tool) {
        m_currentTool = tool;
        updateToolSelection();
        emit toolChanged(tool);
    }
}

void AnnotationToolbar::setCurrentColor(const QColor& color)
{
    if (m_currentColor != color) {
        m_currentColor = color;
        updateColorButton();
        emit colorChanged(color);
    }
}

void AnnotationToolbar::setCurrentOpacity(qreal opacity)
{
    if (m_currentOpacity != opacity) {
        m_currentOpacity = opacity;
        m_opacitySlider->blockSignals(true);
        m_opacitySlider->setValue(static_cast<int>(opacity * 100));
        m_opacitySlider->blockSignals(false);
        emit opacityChanged(opacity);
    }
}

void AnnotationToolbar::setCurrentLineWidth(qreal width)
{
    if (m_currentLineWidth != width) {
        m_currentLineWidth = width;
        m_lineWidthSpinBox->blockSignals(true);
        m_lineWidthSpinBox->setValue(width);
        m_lineWidthSpinBox->blockSignals(false);
        emit lineWidthChanged(width);
    }
}

void AnnotationToolbar::setToolsEnabled(bool enabled)
{
    setEnabled(enabled);
}

void AnnotationToolbar::setAnnotationToolsEnabled(bool enabled)
{
    m_highlightButton->setEnabled(enabled);
    m_noteButton->setEnabled(enabled);
    m_drawingButton->setEnabled(enabled);
    m_rectangleButton->setEnabled(enabled);
    m_circleButton->setEnabled(enabled);
    m_arrowButton->setEnabled(enabled);
    m_textButton->setEnabled(enabled);
    
    if (!enabled && m_currentTool != AnnotationTool::Select) {
        setCurrentTool(AnnotationTool::Select);
    }
}

void AnnotationToolbar::onToolButtonClicked()
{
    int toolId = m_toolButtonGroup->checkedId();
    if (toolId >= 0) {
        setCurrentTool(static_cast<AnnotationTool>(toolId));
    }
}

void AnnotationToolbar::onColorButtonClicked()
{
    if (!m_colorDialog) {
        m_colorDialog = new QColorDialog(this);
        m_colorDialog->setOption(QColorDialog::ShowAlphaChannel, false);
    }
    
    m_colorDialog->setCurrentColor(m_currentColor);
    if (m_colorDialog->exec() == QDialog::Accepted) {
        setCurrentColor(m_colorDialog->currentColor());
    }
}

void AnnotationToolbar::onOpacityChanged(int value)
{
    setCurrentOpacity(value / 100.0);
}

void AnnotationToolbar::onLineWidthChanged(double value)
{
    setCurrentLineWidth(value);
}

void AnnotationToolbar::onDeleteClicked()
{
    emit deleteSelectedAnnotations();
}

void AnnotationToolbar::onCopyClicked()
{
    emit copySelectedAnnotations();
}

void AnnotationToolbar::onPasteClicked()
{
    emit pasteAnnotations();
}

void AnnotationToolbar::updateColorButton()
{
    // Create a colored pixmap for the button
    QPixmap pixmap(20, 15);
    pixmap.fill(m_currentColor);
    
    QPainter painter(&pixmap);
    painter.setPen(Qt::black);
    painter.drawRect(pixmap.rect().adjusted(0, 0, -1, -1));
    
    m_colorButton->setIcon(QIcon(pixmap));
}

void AnnotationToolbar::updateToolSelection()
{
    QAbstractButton* button = m_toolButtonGroup->button(static_cast<int>(m_currentTool));
    if (button) {
        button->setChecked(true);
    }
}

void AnnotationToolbar::setupKeyboardShortcuts()
{
    // Create shortcuts for annotation tools
    QShortcut* selectShortcut = new QShortcut(QKeySequence("S"), this);
    connect(selectShortcut, &QShortcut::activated, [this]() {
        setCurrentTool(AnnotationTool::Select);
    });

    QShortcut* highlightShortcut = new QShortcut(QKeySequence("H"), this);
    connect(highlightShortcut, &QShortcut::activated, [this]() {
        setCurrentTool(AnnotationTool::Highlight);
    });

    QShortcut* noteShortcut = new QShortcut(QKeySequence("N"), this);
    connect(noteShortcut, &QShortcut::activated, [this]() {
        setCurrentTool(AnnotationTool::Note);
    });

    QShortcut* drawingShortcut = new QShortcut(QKeySequence("D"), this);
    connect(drawingShortcut, &QShortcut::activated, [this]() {
        setCurrentTool(AnnotationTool::Drawing);
    });

    QShortcut* rectangleShortcut = new QShortcut(QKeySequence("R"), this);
    connect(rectangleShortcut, &QShortcut::activated, [this]() {
        setCurrentTool(AnnotationTool::Rectangle);
    });

    QShortcut* circleShortcut = new QShortcut(QKeySequence("C"), this);
    connect(circleShortcut, &QShortcut::activated, [this]() {
        setCurrentTool(AnnotationTool::Circle);
    });

    QShortcut* arrowShortcut = new QShortcut(QKeySequence("A"), this);
    connect(arrowShortcut, &QShortcut::activated, [this]() {
        setCurrentTool(AnnotationTool::Arrow);
    });

    QShortcut* textShortcut = new QShortcut(QKeySequence("T"), this);
    connect(textShortcut, &QShortcut::activated, [this]() {
        setCurrentTool(AnnotationTool::Text);
    });

    // Copy/Paste shortcuts
    QShortcut* copyShortcut = new QShortcut(QKeySequence::Copy, this);
    connect(copyShortcut, &QShortcut::activated, this, &AnnotationToolbar::copySelectedAnnotations);

    QShortcut* pasteShortcut = new QShortcut(QKeySequence::Paste, this);
    connect(pasteShortcut, &QShortcut::activated, this, &AnnotationToolbar::pasteAnnotations);
}
