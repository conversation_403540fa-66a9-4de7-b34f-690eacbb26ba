#ifndef ANNOTATIONOVERLAY_H
#define ANNOTATIONOVERLAY_H

#include <QWidget>
#include <QPointF>
#include <QRectF>
#include <QPainterPath>
#include <QColor>
#include "AnnotationToolbar.h"

class QLabel;
class AnnotationManager;
class Annotation;
class DrawingAnnotation;

class AnnotationOverlay : public QWidget
{
    Q_OBJECT

public:
    explicit AnnotationOverlay(QWidget *parent = nullptr);
    ~AnnotationOverlay();

    // Setup
    void setPageLabel(QLabel* pageLabel) { m_pageLabel = pageLabel; }
    void setAnnotationManager(AnnotationManager* manager) { m_annotationManager = manager; }
    void setCurrentTool(AnnotationTool tool) { m_currentTool = tool; updateCursor(); }
    void setCurrentPage(int pageNumber) { m_currentPageNumber = pageNumber; }
    void setZoomFactor(double zoomFactor) { m_zoomFactor = zoomFactor; }
    void setDpi(double dpi) { m_dpi = dpi; }
    
    // Tool properties
    void setAnnotationColor(const QColor& color) { m_annotationColor = color; }
    void setAnnotationOpacity(qreal opacity) { m_annotationOpacity = opacity; }
    void setAnnotationLineWidth(qreal width) { m_annotationLineWidth = width; }
    
    // Coordinate conversion
    QPointF pixelToPdfCoordinates(const QPointF& pixelPoint) const;
    QPointF pdfToPixelCoordinates(const QPointF& pdfPoint) const;
    QRectF pixelToPdfRect(const QRectF& pixelRect) const;
    QRectF pdfToPixelRect(const QRectF& pdfRect) const;

signals:
    void annotationCreated(Annotation* annotation);
    void annotationSelected(Annotation* annotation);
    void annotationDeselected();
    void requestPageRefresh();

protected:
    // Event handlers
    void paintEvent(QPaintEvent* event) override;
    void mousePressEvent(QMouseEvent* event) override;
    void mouseMoveEvent(QMouseEvent* event) override;
    void mouseReleaseEvent(QMouseEvent* event) override;
    void keyPressEvent(QKeyEvent* event) override;
    void contextMenuEvent(QContextMenuEvent* event) override;

private:
    enum class ResizeHandle {
        None,
        TopLeft,
        TopRight,
        BottomLeft,
        BottomRight,
        Top,
        Bottom,
        Left,
        Right
    };

    void updateCursor();
    void startAnnotationCreation(const QPointF& position);
    void updateAnnotationCreation(const QPointF& position);
    void finishAnnotationCreation(const QPointF& position);
    void cancelAnnotationCreation();
    
    void handleSelectionTool(const QPointF& position, bool isPress, bool isMove, bool isRelease);
    void handleHighlightTool(const QPointF& position, bool isPress, bool isMove, bool isRelease);
    void handleNoteTool(const QPointF& position, bool isPress, bool isMove, bool isRelease);
    void handleDrawingTool(const QPointF& position, bool isPress, bool isMove, bool isRelease);
    void handleShapeTool(const QPointF& position, bool isPress, bool isMove, bool isRelease);
    void handleTextTool(const QPointF& position, bool isPress, bool isMove, bool isRelease);
    
    void selectAnnotationAt(const QPointF& position);
    void deselectAllAnnotations();
    void moveSelectedAnnotation(const QPointF& offset);

    // Resize handle methods
    ResizeHandle getResizeHandleAt(const QPointF& position) const;
    void startResize(ResizeHandle handle, const QPointF& position);
    void updateResize(const QPointF& position);
    void finishResize();
    QCursor getResizeCursor(ResizeHandle handle) const;

    // Context menu methods
    void showContextMenu(const QPointF& position);
    void createAnnotationContextMenu(Annotation* annotation, const QPointF& position);
    void createGeneralContextMenu(const QPointF& position);

    void drawCreationPreview(QPainter* painter);
    void drawSelectionHandles(QPainter* painter);

private:
    // References
    QLabel* m_pageLabel = nullptr;
    AnnotationManager* m_annotationManager = nullptr;
    
    // Current state
    AnnotationTool m_currentTool = AnnotationTool::Select;
    int m_currentPageNumber = 0;
    double m_zoomFactor = 1.0;
    double m_dpi = 150.0;
    
    // Tool properties
    QColor m_annotationColor = Qt::yellow;
    qreal m_annotationOpacity = 0.5;
    qreal m_annotationLineWidth = 2.0;
    
    // Mouse interaction state
    bool m_isCreating = false;
    bool m_isDragging = false;
    bool m_isMovingAnnotation = false;
    bool m_isResizing = false;
    QPointF m_startPosition;
    QPointF m_currentPosition;
    QPointF m_lastPosition;
    
    // Creation state
    Annotation* m_creatingAnnotation = nullptr;
    DrawingAnnotation* m_currentDrawing = nullptr;
    QPainterPath m_previewPath;
    QRectF m_previewRect;
    
    // Selection state
    Annotation* m_selectedAnnotation = nullptr;
    QPointF m_selectionOffset;

    // Resize state
    ResizeHandle m_currentResizeHandle = ResizeHandle::None;
    QRectF m_originalBounds;
    QPointF m_resizeStartPosition;

    // Drawing state for free-hand drawing
    QList<QPointF> m_drawingPoints;
};

#endif // ANNOTATIONOVERLAY_H
