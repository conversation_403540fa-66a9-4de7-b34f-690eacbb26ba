#!/usr/bin/env python3
"""
Create a simple test PDF without external dependencies.
This creates a minimal PDF with basic structure.
"""

def create_minimal_pdf(filename="test.pdf"):
    """Create a minimal PDF file with basic content."""

    # Minimal PDF content with corrected structure
    pdf_content = """%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R 4 0 R]
/Count 2
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 5 0 R
/Resources <<
/Font <<
/F1 6 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 7 0 R
/Resources <<
/Font <<
/F1 6 0 R
>>
>>
>>
endobj

5 0 obj
<<
/Length 185
>>
stream
BT
/F1 12 Tf
50 750 Td
(Test PDF Document - Page 1) Tj
0 -20 Td
(This is a simple test PDF for unit testing.) Tj
0 -20 Td
(It contains searchable text and multiple pages.) Tj
0 -20 Td
(Keywords: annotation highlight test search) Tj
ET
endstream
endobj

6 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Times-Roman
>>
endobj

7 0 obj
<<
/Length 170
>>
stream
BT
/F1 12 Tf
50 750 Td
(Test PDF Document - Page 2) Tj
0 -20 Td
(This is the second page of the test document.) Tj
0 -20 Td
(More keywords: PDF viewer document page zoom) Tj
0 -20 Td
(Additional content for testing functionality.) Tj
ET
endstream
endobj

xref
0 8
0000000000 65535 f
0000000010 00000 n
0000000053 00000 n
0000000125 00000 n
0000000271 00000 n
0000000417 00000 n
0000000705 00000 n
0000000775 00000 n
trailer
<<
/Size 8
/Root 1 0 R
>>
startxref
1048
%%EOF"""

    try:
        with open(filename, 'w', encoding='latin-1') as f:
            f.write(pdf_content)
        print(f"Created minimal test PDF: {filename}")
        return filename
    except Exception as e:
        print(f"Failed to create PDF: {e}")
        return None

if __name__ == "__main__":
    import os
    
    # Create in tests directory
    tests_dir = "tests"
    if os.path.exists(tests_dir):
        pdf_path = os.path.join(tests_dir, "test.pdf")
        create_minimal_pdf(pdf_path)
        
        # Also create in project root
        create_minimal_pdf("test.pdf")
    else:
        create_minimal_pdf("test.pdf")
