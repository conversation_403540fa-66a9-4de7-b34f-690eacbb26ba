@echo off
echo Building and running Qt PDF Viewer tests...
echo.

REM Create build directory for tests if it doesn't exist
if not exist "build" (
    echo Creating build directory...
    mkdir build
)

cd build

REM Configure with tests enabled
echo Configuring CMake with tests enabled...
cmake .. -DBUILD_TESTS=ON
if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

REM Build the project and tests
echo Building project and tests...
cmake --build . --config Debug
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

REM Run the tests
echo.
echo Running tests...
echo ================
ctest --output-on-failure --verbose
if %ERRORLEVEL% neq 0 (
    echo Some tests failed!
    pause
    exit /b 1
)

echo.
echo All tests completed successfully!
pause
