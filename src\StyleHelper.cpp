#include "StyleHelper.h"
#include "DesignSystem.h"
#include <QPropertyAnimation>
#include <QGraphicsEffect>
#include <QGraphicsDropShadowEffect>

// Text styling methods
void StyleHelper::applyDisplayText(QLabel* label, const QString& variant)
{
    if (!label) return;
    
    QString styleSheet = DesignSystem::getTextStyleSheet(variant) + 
                        DesignSystem::getColorStyleSheet(DesignSystem::Colors::TextPrimary);
    label->setStyleSheet(styleSheet);
    label->setWordWrap(true);
}

void StyleHelper::applyHeadingText(QLabel* label, const QString& variant)
{
    if (!label) return;
    
    QString styleSheet = DesignSystem::getTextStyleSheet(variant) + 
                        DesignSystem::getColorStyleSheet(DesignSystem::Colors::TextPrimary);
    label->setStyleSheet(styleSheet);
    label->setWordWrap(true);
}

void StyleHelper::applyBodyText(QLabel* label, const QString& variant)
{
    if (!label) return;
    
    QString styleSheet = DesignSystem::getTextStyleSheet(variant) + 
                        DesignSystem::getColorStyleSheet(DesignSystem::Colors::TextPrimary);
    label->setStyleSheet(styleSheet);
    label->setWordWrap(true);
}

void StyleHelper::applyCaptionText(QLabel* label)
{
    if (!label) return;
    
    QString styleSheet = DesignSystem::getTextStyleSheet("caption");
    label->setStyleSheet(styleSheet);
    label->setWordWrap(true);
}

// Button styling methods
void StyleHelper::applyPrimaryButton(QPushButton* button)
{
    if (!button) return;
    
    QString styleSheet = DesignSystem::getButtonStyleSheet("primary");
    button->setStyleSheet(styleSheet);
    button->setCursor(Qt::PointingHandCursor);
    button->setMinimumHeight(DesignSystem::ComponentSizes::InputMedium);
}

void StyleHelper::applySecondaryButton(QPushButton* button)
{
    if (!button) return;
    
    QString styleSheet = DesignSystem::getButtonStyleSheet("secondary");
    button->setStyleSheet(styleSheet);
    button->setCursor(Qt::PointingHandCursor);
    button->setMinimumHeight(DesignSystem::ComponentSizes::InputMedium);
}

void StyleHelper::applyIconButton(QPushButton* button, int iconSize)
{
    if (!button) return;
    
    QString styleSheet = QString(
        "QPushButton {"
        "    background-color: transparent;"
        "    border: none;"
        "    border-radius: %1px;"
        "    padding: %2px;"
        "    min-width: %3px;"
        "    min-height: %3px;"
        "}"
        "QPushButton:hover {"
        "    background-color: %4;"
        "}"
        "QPushButton:pressed {"
        "    background-color: %5;"
        "}"
    ).arg(DesignSystem::BorderRadius::Medium)
     .arg(DesignSystem::Spacing::XSmall)
     .arg(iconSize + DesignSystem::Spacing::Small)
     .arg(DesignSystem::Colors::BackgroundHover.name())
     .arg(DesignSystem::Colors::BackgroundTertiary.name());
    
    button->setStyleSheet(styleSheet);
    button->setCursor(Qt::PointingHandCursor);
    button->setIconSize(QSize(iconSize, iconSize));
}

void StyleHelper::applyToolbarButton(QPushButton* button)
{
    if (!button) return;
    
    applyIconButton(button, DesignSystem::ComponentSizes::IconMedium);
    
    // Additional toolbar-specific styling
    QString additionalStyle = QString(
        "QPushButton {"
        "    margin: %1px;"
        "}"
    ).arg(DesignSystem::Spacing::XSmall / 2);
    
    button->setStyleSheet(button->styleSheet() + additionalStyle);
}

// Input styling methods
void StyleHelper::applyInputField(QLineEdit* input)
{
    if (!input) return;
    
    QString styleSheet = DesignSystem::getInputStyleSheet();
    input->setStyleSheet(styleSheet);
}

void StyleHelper::applyTextArea(QTextEdit* textArea)
{
    if (!textArea) return;
    
    QString styleSheet = DesignSystem::getInputStyleSheet();
    textArea->setStyleSheet(styleSheet);
}

void StyleHelper::applyComboBox(QComboBox* comboBox)
{
    if (!comboBox) return;
    
    QString styleSheet = DesignSystem::getInputStyleSheet() + QString(
        "QComboBox::drop-down {"
        "    border: none;"
        "    width: %1px;"
        "}"
        "QComboBox::down-arrow {"
        "    width: %2px;"
        "    height: %2px;"
        "}"
    ).arg(DesignSystem::Spacing::Large)
     .arg(DesignSystem::ComponentSizes::IconSmall);
    
    comboBox->setStyleSheet(styleSheet);
}

// Container styling methods
void StyleHelper::applyCard(QWidget* widget)
{
    if (!widget) return;
    
    QString styleSheet = DesignSystem::getCardStyleSheet();
    widget->setStyleSheet(styleSheet);
    applyElevation(widget, 1);
}

void StyleHelper::applyPanel(QWidget* widget)
{
    if (!widget) return;
    
    QString styleSheet = QString(
        "QWidget {"
        "    background-color: %1;"
        "    border: 1px solid %2;"
        "    padding: %3px;"
        "}"
    ).arg(DesignSystem::Colors::BackgroundSecondary.name())
     .arg(DesignSystem::Colors::BorderPrimary.name())
     .arg(DesignSystem::Spacing::Medium);
    
    widget->setStyleSheet(styleSheet);
}

void StyleHelper::applySection(QWidget* widget)
{
    if (!widget) return;
    
    QString styleSheet = QString(
        "QWidget {"
        "    background-color: %1;"
        "    padding: %2px;"
        "    margin-bottom: %3px;"
        "}"
    ).arg(DesignSystem::Colors::BackgroundPrimary.name())
     .arg(DesignSystem::Spacing::SectionPadding)
     .arg(DesignSystem::Spacing::Medium);
    
    widget->setStyleSheet(styleSheet);
}

void StyleHelper::applySidebar(QWidget* widget)
{
    if (!widget) return;
    
    QString styleSheet = QString(
        "QWidget {"
        "    background-color: %1;"
        "    border-right: 1px solid %2;"
        "    padding: %3px;"
        "}"
    ).arg(DesignSystem::Colors::BackgroundSecondary.name())
     .arg(DesignSystem::Colors::BorderPrimary.name())
     .arg(DesignSystem::Spacing::Medium);
    
    widget->setStyleSheet(styleSheet);
}

// Layout styling methods
void StyleHelper::applyToolbar(QWidget* toolbar)
{
    if (!toolbar) return;
    
    QString styleSheet = QString(
        "QWidget {"
        "    background-color: %1;"
        "    border-bottom: 1px solid %2;"
        "    padding: %3px %4px;"
        "    min-height: %5px;"
        "}"
    ).arg(DesignSystem::Colors::BackgroundPrimary.name())
     .arg(DesignSystem::Colors::BorderPrimary.name())
     .arg(DesignSystem::Spacing::Small)
     .arg(DesignSystem::Spacing::Medium)
     .arg(DesignSystem::ComponentSizes::InputLarge);
    
    toolbar->setStyleSheet(styleSheet);
}

void StyleHelper::applyStatusBar(QWidget* statusBar)
{
    if (!statusBar) return;
    
    QString styleSheet = QString(
        "QWidget {"
        "    background-color: %1;"
        "    border-top: 1px solid %2;"
        "    padding: %3px %4px;"
        "    color: %5;"
        "    font-family: %6;"
        "    font-size: %7px;"
        "}"
    ).arg(DesignSystem::Colors::BackgroundSecondary.name())
     .arg(DesignSystem::Colors::BorderPrimary.name())
     .arg(DesignSystem::Spacing::XSmall)
     .arg(DesignSystem::Spacing::Medium)
     .arg(DesignSystem::Colors::TextSecondary.name())
     .arg(DesignSystem::Typography::PrimaryFont)
     .arg(DesignSystem::Typography::BodySmall);
    
    statusBar->setStyleSheet(styleSheet);
}

void StyleHelper::applyTabWidget(QTabWidget* tabWidget)
{
    if (!tabWidget) return;
    
    QString styleSheet = QString(
        "QTabWidget::pane {"
        "    border: 1px solid %1;"
        "    background-color: %2;"
        "}"
        "QTabBar::tab {"
        "    background-color: %3;"
        "    color: %4;"
        "    padding: %5px %6px;"
        "    margin-right: %7px;"
        "    border-top-left-radius: %8px;"
        "    border-top-right-radius: %8px;"
        "    font-family: %9;"
        "    font-size: %10px;"
        "}"
        "QTabBar::tab:selected {"
        "    background-color: %2;"
        "    color: %11;"
        "    border-bottom: 2px solid %12;"
        "}"
        "QTabBar::tab:hover {"
        "    background-color: %13;"
        "}"
    ).arg(DesignSystem::Colors::BorderPrimary.name())
     .arg(DesignSystem::Colors::BackgroundPrimary.name())
     .arg(DesignSystem::Colors::BackgroundSecondary.name())
     .arg(DesignSystem::Colors::TextSecondary.name())
     .arg(DesignSystem::Spacing::Small)
     .arg(DesignSystem::Spacing::Medium)
     .arg(DesignSystem::Spacing::XSmall)
     .arg(DesignSystem::BorderRadius::Medium)
     .arg(DesignSystem::Typography::PrimaryFont)
     .arg(DesignSystem::Typography::BodyMedium)
     .arg(DesignSystem::Colors::TextPrimary.name())
     .arg(DesignSystem::Colors::Primary.name())
     .arg(DesignSystem::Colors::BackgroundHover.name());
    
    tabWidget->setStyleSheet(styleSheet);
}

void StyleHelper::applyScrollArea(QScrollArea* scrollArea)
{
    if (!scrollArea) return;
    
    QString styleSheet = QString(
        "QScrollArea {"
        "    border: none;"
        "    background-color: %1;"
        "}"
        "QScrollBar:vertical {"
        "    background-color: %2;"
        "    width: %3px;"
        "    border-radius: %4px;"
        "}"
        "QScrollBar::handle:vertical {"
        "    background-color: %5;"
        "    border-radius: %4px;"
        "    min-height: %6px;"
        "}"
        "QScrollBar::handle:vertical:hover {"
        "    background-color: %7;"
        "}"
    ).arg(DesignSystem::Colors::BackgroundPrimary.name())
     .arg(DesignSystem::Colors::BackgroundTertiary.name())
     .arg(DesignSystem::Spacing::Small)
     .arg(DesignSystem::BorderRadius::Small)
     .arg(DesignSystem::Colors::BorderSecondary.name())
     .arg(DesignSystem::Spacing::Large)
     .arg(DesignSystem::Colors::TextTertiary.name());
    
    scrollArea->setStyleSheet(styleSheet);
}

// Spacing utilities
void StyleHelper::applyStandardMargins(QWidget* widget)
{
    if (!widget) return;
    widget->setContentsMargins(DesignSystem::Spacing::Medium, DesignSystem::Spacing::Medium,
                              DesignSystem::Spacing::Medium, DesignSystem::Spacing::Medium);
}

void StyleHelper::applyCompactMargins(QWidget* widget)
{
    if (!widget) return;
    widget->setContentsMargins(DesignSystem::Spacing::Small, DesignSystem::Spacing::Small,
                              DesignSystem::Spacing::Small, DesignSystem::Spacing::Small);
}

void StyleHelper::applyLargeMargins(QWidget* widget)
{
    if (!widget) return;
    widget->setContentsMargins(DesignSystem::Spacing::Large, DesignSystem::Spacing::Large,
                              DesignSystem::Spacing::Large, DesignSystem::Spacing::Large);
}

void StyleHelper::applyNoMargins(QWidget* widget)
{
    if (!widget) return;
    widget->setContentsMargins(0, 0, 0, 0);
}

// Visual hierarchy utilities
void StyleHelper::applySeparator(QFrame* frame, bool horizontal)
{
    if (!frame) return;

    frame->setFrameShape(horizontal ? QFrame::HLine : QFrame::VLine);
    frame->setFrameShadow(QFrame::Plain);

    QString styleSheet = QString(
        "QFrame {"
        "    color: %1;"
        "    background-color: %1;"
        "    border: none;"
        "    max-height: 1px;"
        "    margin: %2px 0px;"
        "}"
    ).arg(DesignSystem::Colors::BorderPrimary.name())
     .arg(DesignSystem::Spacing::Small);

    frame->setStyleSheet(styleSheet);
}

void StyleHelper::applyGroupBox(QGroupBox* groupBox)
{
    if (!groupBox) return;

    QString styleSheet = QString(
        "QGroupBox {"
        "    font-family: %1;"
        "    font-size: %2px;"
        "    font-weight: %3;"
        "    color: %4;"
        "    border: 1px solid %5;"
        "    border-radius: %6px;"
        "    margin-top: %7px;"
        "    padding-top: %8px;"
        "}"
        "QGroupBox::title {"
        "    subcontrol-origin: margin;"
        "    left: %9px;"
        "    padding: 0 %10px 0 %10px;"
        "    background-color: %11;"
        "}"
    ).arg(DesignSystem::Typography::PrimaryFont)
     .arg(DesignSystem::Typography::BodyMedium)
     .arg(DesignSystem::Typography::WeightMedium)
     .arg(DesignSystem::Colors::TextPrimary.name())
     .arg(DesignSystem::Colors::BorderPrimary.name())
     .arg(DesignSystem::BorderRadius::Medium)
     .arg(DesignSystem::Spacing::Small)
     .arg(DesignSystem::Spacing::Medium)
     .arg(DesignSystem::Spacing::Small)
     .arg(DesignSystem::Spacing::XSmall)
     .arg(DesignSystem::Colors::BackgroundPrimary.name());

    groupBox->setStyleSheet(styleSheet);
}

void StyleHelper::applyElevation(QWidget* widget, int level)
{
    if (!widget) return;

    QGraphicsDropShadowEffect* shadow = new QGraphicsDropShadowEffect();
    shadow->setBlurRadius(level * 4);
    shadow->setOffset(0, level * 2);
    shadow->setColor(QColor(0, 0, 0, 20 + level * 10));

    widget->setGraphicsEffect(shadow);
}

// State styling
void StyleHelper::applyHoverEffect(QWidget* widget)
{
    if (!widget) return;

    widget->setAttribute(Qt::WA_Hover, true);

    QString currentStyle = widget->styleSheet();
    QString hoverStyle = currentStyle + QString(
        "QWidget:hover {"
        "    background-color: %1;"
        "}"
    ).arg(DesignSystem::Colors::BackgroundHover.name());

    widget->setStyleSheet(hoverStyle);
}

void StyleHelper::applyFocusEffect(QWidget* widget)
{
    if (!widget) return;

    QString currentStyle = widget->styleSheet();
    QString focusStyle = currentStyle + QString(
        "QWidget:focus {"
        "    border: 2px solid %1;"
        "    outline: none;"
        "}"
    ).arg(DesignSystem::Colors::Primary.name());

    widget->setStyleSheet(focusStyle);
}

void StyleHelper::applyDisabledState(QWidget* widget)
{
    if (!widget) return;

    widget->setEnabled(false);

    QString styleSheet = QString(
        "QWidget:disabled {"
        "    color: %1;"
        "    background-color: %2;"
        "    border-color: %3;"
        "}"
    ).arg(DesignSystem::Colors::TextDisabled.name())
     .arg(DesignSystem::Colors::BackgroundTertiary.name())
     .arg(DesignSystem::Colors::BorderSecondary.name());

    widget->setStyleSheet(widget->styleSheet() + styleSheet);
}

void StyleHelper::applyActiveState(QWidget* widget)
{
    if (!widget) return;

    QString styleSheet = QString(
        "QWidget {"
        "    border: 2px solid %1;"
        "    background-color: %2;"
        "}"
    ).arg(DesignSystem::Colors::Primary.name())
     .arg(DesignSystem::Colors::PrimaryLight.name());

    widget->setStyleSheet(widget->styleSheet() + styleSheet);
}

// Color utilities
void StyleHelper::applySuccessState(QWidget* widget)
{
    if (!widget) return;

    QString styleSheet = QString(
        "QWidget {"
        "    border-left: 4px solid %1;"
        "    background-color: rgba(%2, %3, %4, 0.1);"
        "}"
    ).arg(DesignSystem::Colors::Success.name())
     .arg(DesignSystem::Colors::Success.red())
     .arg(DesignSystem::Colors::Success.green())
     .arg(DesignSystem::Colors::Success.blue());

    widget->setStyleSheet(widget->styleSheet() + styleSheet);
}

void StyleHelper::applyWarningState(QWidget* widget)
{
    if (!widget) return;

    QString styleSheet = QString(
        "QWidget {"
        "    border-left: 4px solid %1;"
        "    background-color: rgba(%2, %3, %4, 0.1);"
        "}"
    ).arg(DesignSystem::Colors::Warning.name())
     .arg(DesignSystem::Colors::Warning.red())
     .arg(DesignSystem::Colors::Warning.green())
     .arg(DesignSystem::Colors::Warning.blue());

    widget->setStyleSheet(widget->styleSheet() + styleSheet);
}

void StyleHelper::applyErrorState(QWidget* widget)
{
    if (!widget) return;

    QString styleSheet = QString(
        "QWidget {"
        "    border-left: 4px solid %1;"
        "    background-color: rgba(%2, %3, %4, 0.1);"
        "}"
    ).arg(DesignSystem::Colors::Error.name())
     .arg(DesignSystem::Colors::Error.red())
     .arg(DesignSystem::Colors::Error.green())
     .arg(DesignSystem::Colors::Error.blue());

    widget->setStyleSheet(widget->styleSheet() + styleSheet);
}

void StyleHelper::applyInfoState(QWidget* widget)
{
    if (!widget) return;

    QString styleSheet = QString(
        "QWidget {"
        "    border-left: 4px solid %1;"
        "    background-color: rgba(%2, %3, %4, 0.1);"
        "}"
    ).arg(DesignSystem::Colors::Info.name())
     .arg(DesignSystem::Colors::Info.red())
     .arg(DesignSystem::Colors::Info.green())
     .arg(DesignSystem::Colors::Info.blue());

    widget->setStyleSheet(widget->styleSheet() + styleSheet);
}

// Animation utilities
void StyleHelper::applyFadeTransition(QWidget* widget)
{
    if (!widget) return;

    QPropertyAnimation* animation = new QPropertyAnimation(widget, "windowOpacity");
    animation->setDuration(DesignSystem::Animations::DurationNormal);
    animation->setStartValue(0.0);
    animation->setEndValue(1.0);
    animation->start(QAbstractAnimation::DeleteWhenStopped);
}

void StyleHelper::applySlideTransition(QWidget* widget)
{
    if (!widget) return;

    QPropertyAnimation* animation = new QPropertyAnimation(widget, "geometry");
    animation->setDuration(DesignSystem::Animations::DurationNormal);

    QRect startGeometry = widget->geometry();
    startGeometry.moveLeft(-startGeometry.width());
    QRect endGeometry = widget->geometry();

    animation->setStartValue(startGeometry);
    animation->setEndValue(endGeometry);
    animation->start(QAbstractAnimation::DeleteWhenStopped);
}

void StyleHelper::applyScaleTransition(QWidget* widget)
{
    if (!widget) return;

    QPropertyAnimation* animation = new QPropertyAnimation(widget, "geometry");
    animation->setDuration(DesignSystem::Animations::DurationNormal);

    QRect endGeometry = widget->geometry();
    QRect startGeometry = endGeometry;
    startGeometry.setSize(endGeometry.size() * 0.8);
    startGeometry.moveCenter(endGeometry.center());

    animation->setStartValue(startGeometry);
    animation->setEndValue(endGeometry);
    animation->start(QAbstractAnimation::DeleteWhenStopped);
}

// Responsive utilities
void StyleHelper::applyResponsiveLayout(QWidget* widget, int breakpoint)
{
    if (!widget) return;

    // This would typically be connected to window resize events
    // For now, we'll apply based on current widget size
    if (widget->width() < breakpoint) {
        applyMobileLayout(widget);
    } else {
        applyDesktopLayout(widget);
    }
}

void StyleHelper::applyMobileLayout(QWidget* widget)
{
    if (!widget) return;

    applyCompactMargins(widget);

    QString styleSheet = QString(
        "QWidget {"
        "    font-size: %1px;"
        "    padding: %2px;"
        "}"
    ).arg(DesignSystem::Typography::BodySmall)
     .arg(DesignSystem::Spacing::Small);

    widget->setStyleSheet(widget->styleSheet() + styleSheet);
}

void StyleHelper::applyDesktopLayout(QWidget* widget)
{
    if (!widget) return;

    applyStandardMargins(widget);

    QString styleSheet = QString(
        "QWidget {"
        "    font-size: %1px;"
        "    padding: %2px;"
        "}"
    ).arg(DesignSystem::Typography::BodyMedium)
     .arg(DesignSystem::Spacing::Medium);

    widget->setStyleSheet(widget->styleSheet() + styleSheet);
}

// Helper methods
QString StyleHelper::buildStyleSheet(const QStringList& rules)
{
    return rules.join(" ");
}

void StyleHelper::setWidgetProperty(QWidget* widget, const QString& property, const QVariant& value)
{
    if (!widget) return;
    widget->setProperty(property.toUtf8().constData(), value);
}

void StyleHelper::applyStyleWithTransition(QWidget* widget, const QString& styleSheet, int duration)
{
    if (!widget) return;

    // Store original style
    QString originalStyle = widget->styleSheet();

    // Apply new style
    widget->setStyleSheet(styleSheet);

    // Create fade transition
    QPropertyAnimation* animation = new QPropertyAnimation(widget, "windowOpacity");
    animation->setDuration(duration);
    animation->setStartValue(0.8);
    animation->setEndValue(1.0);
    animation->start(QAbstractAnimation::DeleteWhenStopped);
}
