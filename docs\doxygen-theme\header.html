<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen $doxygenversion"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<!--BEGIN PROJECT_NAME--><title>$projectname: $title</title><!--END PROJECT_NAME-->
<!--BEGIN !PROJECT_NAME--><title>$title</title><!--END !PROJECT_NAME-->
<link href="$relpath^tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="$relpath^jquery.js"></script>
<script type="text/javascript" src="$relpath^dynsections.js"></script>
$treeview
$search
$mathjax
<link href="$relpath^$stylesheet" rel="stylesheet" type="text/css" />
$extrastylesheet
<!-- Custom favicon -->
<link rel="icon" type="image/png" sizes="32x32" href="$relpath^favicon-32x32.png">
<link rel="icon" type="image/png" sizes="16x16" href="$relpath^favicon-16x16.png">
<link rel="apple-touch-icon" sizes="180x180" href="$relpath^apple-touch-icon.png">
<link rel="manifest" href="$relpath^site.webmanifest">

<!-- Open Graph meta tags for social sharing -->
<meta property="og:title" content="$projectname - $title" />
<meta property="og:description" content="High-performance PDF viewer with advanced annotation capabilities" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://your-domain.com/docs/" />
<meta property="og:image" content="https://your-domain.com/docs/images/og-image.png" />

<!-- Twitter Card meta tags -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="$projectname - $title" />
<meta name="twitter:description" content="High-performance PDF viewer with advanced annotation capabilities" />
<meta name="twitter:image" content="https://your-domain.com/docs/images/twitter-card.png" />

<!-- Additional meta tags for SEO -->
<meta name="description" content="API documentation for Optimized PDF Viewer - A high-performance Qt6-based PDF viewer with advanced annotation capabilities, modern UI, and comprehensive features." />
<meta name="keywords" content="PDF viewer, Qt6, C++, annotations, document viewer, API documentation, Doxygen" />
<meta name="author" content="PDF Viewer Team" />
<meta name="robots" content="index, follow" />

<!-- Theme detection and dark mode support -->
<script>
// Detect user's preferred color scheme
if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
    document.documentElement.setAttribute('data-theme', 'dark');
}

// Listen for changes in color scheme preference
if (window.matchMedia) {
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
        document.documentElement.setAttribute('data-theme', e.matches ? 'dark' : 'light');
    });
}

// Check for saved theme preference or default to system preference
const savedTheme = localStorage.getItem('theme');
if (savedTheme) {
    document.documentElement.setAttribute('data-theme', savedTheme);
} else if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
    document.documentElement.setAttribute('data-theme', 'dark');
} else {
    document.documentElement.setAttribute('data-theme', 'light');
}
</script>

<!-- Custom analytics (replace with your analytics code) -->
<!-- 
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>
-->

<!-- Custom CSS for enhanced styling -->
<style>
/* Loading animation */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.loading-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0067c0;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Skip to content link for accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #0067c0;
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 0 0 4px 4px;
    z-index: 10000;
    transition: top 0.3s ease;
}

.skip-link:focus {
    top: 0;
}

/* Print-specific styles */
@media print {
    .loading-overlay,
    .skip-link {
        display: none !important;
    }
}
</style>
</head>
<body>
<!-- Loading overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-spinner"></div>
</div>

<!-- Skip to content link for accessibility -->
<a href="#main-content" class="skip-link">Skip to main content</a>

<div id="top"><!-- do not remove this div, it is closed by doxygen! -->

<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">$projectname
   <!--BEGIN PROJECT_NUMBER-->&#160;<span id="projectnumber">$projectnumber</span><!--END PROJECT_NUMBER-->
   </div>
   <!--BEGIN PROJECT_BRIEF--><div id="projectbrief">$projectbrief</div><!--END PROJECT_BRIEF-->
  </td>
  <!--BEGIN PROJECT_LOGO-->
   <td style="padding-left: 0.5em;">
    <div id="projectlogo"><img alt="Logo" src="$relpath^$projectlogo"/></div>
   </td>
  <!--END PROJECT_LOGO-->
  <!--BEGIN DISABLE_INDEX-->
   <!--BEGIN SEARCHENGINE-->
   <td>$searchbox</td>
   <!--END SEARCHENGINE-->
  <!--END DISABLE_INDEX-->
 </tr>
 </tbody>
</table>
</div>

<!-- Theme toggle button -->
<div style="position: absolute; top: 10px; right: 10px;">
    <button id="themeToggle" onclick="toggleTheme()" style="
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.3s ease;
    " onmouseover="this.style.background='rgba(255, 255, 255, 0.3)'" 
       onmouseout="this.style.background='rgba(255, 255, 255, 0.2)'">
        🌓 Theme
    </button>
</div>

<script>
// Theme toggle functionality
function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
}

// Hide loading overlay when page is loaded
window.addEventListener('load', function() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.classList.add('hidden');
        setTimeout(() => {
            loadingOverlay.style.display = 'none';
        }, 300);
    }
});

// Enhanced keyboard navigation
document.addEventListener('keydown', function(e) {
    // Alt + T for theme toggle
    if (e.altKey && e.key === 't') {
        e.preventDefault();
        toggleTheme();
    }
    
    // Alt + S for search focus
    if (e.altKey && e.key === 's') {
        e.preventDefault();
        const searchField = document.getElementById('MSearchField');
        if (searchField) {
            searchField.focus();
        }
    }
});

// Add ARIA labels for better accessibility
document.addEventListener('DOMContentLoaded', function() {
    // Add ARIA labels to navigation elements
    const navElements = document.querySelectorAll('.tabs a, .tabs2 a, .tabs3 a');
    navElements.forEach(function(element) {
        if (!element.getAttribute('aria-label')) {
            element.setAttribute('aria-label', 'Navigate to ' + element.textContent.trim());
        }
    });
    
    // Add role attributes to main content areas
    const contents = document.querySelector('.contents');
    if (contents) {
        contents.setAttribute('role', 'main');
        contents.setAttribute('id', 'main-content');
    }
    
    // Enhance search accessibility
    const searchBox = document.getElementById('MSearchBox');
    if (searchBox) {
        searchBox.setAttribute('role', 'search');
        searchBox.setAttribute('aria-label', 'Search documentation');
    }
});
</script>

<!-- do not remove this div, it is closed by doxygen! -->
