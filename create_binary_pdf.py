#!/usr/bin/env python3
"""
Create a simple test PDF using binary data approach.
This creates a very basic PDF that should work with most PDF readers.
"""

def create_simple_binary_pdf(filename="test.pdf"):
    """Create a simple PDF using binary approach."""
    
    # Very simple PDF with minimal structure
    pdf_data = b"""%PDF-1.3
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test PDF) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Courier
>>
endobj

xref
0 6
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000251 00000 n 
0000000345 00000 n 
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
422
%%EOF
"""

    try:
        with open(filename, 'wb') as f:
            f.write(pdf_data)
        print(f"Created binary test PDF: {filename}")
        return filename
    except Exception as e:
        print(f"Failed to create binary PDF: {e}")
        return None

if __name__ == "__main__":
    import os
    
    # Create in tests directory
    tests_dir = "tests"
    if os.path.exists(tests_dir):
        pdf_path = os.path.join(tests_dir, "test.pdf")
        create_simple_binary_pdf(pdf_path)
        
        # Also create in project root
        create_simple_binary_pdf("test.pdf")
    else:
        create_simple_binary_pdf("test.pdf")
