#ifndef USERPROFILE_H
#define USERPROFILE_H

#include <QObject>
#include <QSettings>
#include <QColor>
#include <QJsonObject>
#include <QJsonDocument>

struct ThemeProfile {
    QString name;
    QString description;
    QColor accentColor;
    int transparency;
    bool blurEffect;
    bool shadowEffect;
    bool animations;
    QString backgroundImage;
    QDateTime created;
    QDateTime modified;
    
    ThemeProfile() 
        : accentColor(0, 103, 192)
        , transparency(0)
        , blurEffect(false)
        , shadowEffect(true)
        , animations(true)
        , created(QDateTime::currentDateTime())
        , modified(QDateTime::currentDateTime()) {}
        
    QJsonObject toJson() const;
    void fromJson(const QJsonObject& json);
};

class UserProfile : public QObject
{
    Q_OBJECT

public:
    explicit UserProfile(QObject *parent = nullptr);
    ~UserProfile();

    // Profile management
    void createProfile(const QString& name, const QString& description = QString());
    void deleteProfile(const QString& name);
    void switchToProfile(const QString& name);
    QStringList getProfileNames() const;
    QString getCurrentProfileName() const;
    
    // Theme management
    void saveThemeProfile(const QString& name, const ThemeProfile& theme);
    ThemeProfile loadThemeProfile(const QString& name) const;
    void deleteThemeProfile(const QString& name);
    QStringList getThemeProfileNames() const;
    void applyThemeProfile(const QString& name);
    
    // Current theme settings
    void setAccentColor(const QColor& color);
    QColor getAccentColor() const;
    
    void setTransparency(int transparency);
    int getTransparency() const;
    
    void setBlurEffect(bool enabled);
    bool getBlurEffect() const;
    
    void setShadowEffect(bool enabled);
    bool getShadowEffect() const;
    
    void setAnimations(bool enabled);
    bool getAnimations() const;
    
    void setBackgroundImage(const QString& imagePath);
    QString getBackgroundImage() const;
    
    // Settings persistence
    void saveSettings();
    void loadSettings();
    void resetToDefaults();
    
    // Export/Import
    bool exportProfile(const QString& profileName, const QString& filePath);
    bool importProfile(const QString& filePath);

signals:
    void profileChanged(const QString& profileName);
    void themeChanged();
    void accentColorChanged(const QColor& color);
    void transparencyChanged(int transparency);
    void effectsChanged();

private:
    void initializeDefaultProfiles();
    QString getProfilePath(const QString& profileName) const;
    QString getThemeProfilePath(const QString& themeName) const;
    
    // Settings storage
    QSettings* m_settings;
    QString m_currentProfile;
    QString m_profilesPath;
    QString m_themesPath;
    
    // Current theme settings
    ThemeProfile m_currentTheme;
    
    // Default profiles
    static const QString DEFAULT_PROFILE;
    static const QString WORK_PROFILE;
    static const QString PRESENTATION_PROFILE;
};

#endif // USERPROFILE_H
