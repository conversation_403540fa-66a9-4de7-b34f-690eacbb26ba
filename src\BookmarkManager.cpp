#include "BookmarkManager.h"
#include <QUuid>
#include <QFileInfo>
#include <QFile>
#include <QTextStream>
#include <QDebug>
#include <QDir>
#include <algorithm>

const QString BookmarkManager::SETTINGS_GROUP = "EnhancedBookmarks";
const QString BookmarkManager::BOOKMARKS_KEY = "bookmarks";

// EnhancedBookmark implementation
QJsonObject EnhancedBookmark::toJson() const
{
    QJsonObject obj;
    obj["id"] = id;
    obj["name"] = name;
    obj["description"] = description;
    obj["filePath"] = filePath;
    obj["fileName"] = fileName;
    obj["pageNumber"] = pageNumber;
    obj["zoomFactor"] = zoomFactor;
    obj["rotation"] = rotation;
    obj["created"] = created.toString(Qt::ISODate);
    obj["lastAccessed"] = lastAccessed.toString(Qt::ISODate);
    obj["accessCount"] = accessCount;
    obj["tags"] = QJsonArray::fromStringList(tags);
    obj["color"] = color.name();
    obj["notes"] = notes;
    obj["isFavorite"] = isFavorite;
    obj["category"] = category;
    
    // Viewport rect
    QJsonObject viewportObj;
    viewportObj["x"] = viewportRect.x();
    viewportObj["y"] = viewportRect.y();
    viewportObj["width"] = viewportRect.width();
    viewportObj["height"] = viewportRect.height();
    obj["viewportRect"] = viewportObj;
    
    return obj;
}

EnhancedBookmark EnhancedBookmark::fromJson(const QJsonObject& json)
{
    EnhancedBookmark bookmark;
    bookmark.id = json["id"].toString();
    bookmark.name = json["name"].toString();
    bookmark.description = json["description"].toString();
    bookmark.filePath = json["filePath"].toString();
    bookmark.fileName = json["fileName"].toString();
    bookmark.pageNumber = json["pageNumber"].toInt();
    bookmark.zoomFactor = json["zoomFactor"].toDouble();
    bookmark.rotation = json["rotation"].toInt();
    bookmark.created = QDateTime::fromString(json["created"].toString(), Qt::ISODate);
    bookmark.lastAccessed = QDateTime::fromString(json["lastAccessed"].toString(), Qt::ISODate);
    bookmark.accessCount = json["accessCount"].toInt();
    
    // Tags
    QJsonArray tagsArray = json["tags"].toArray();
    for (const auto& value : tagsArray) {
        bookmark.tags.append(value.toString());
    }
    
    bookmark.color = QColor(json["color"].toString());
    bookmark.notes = json["notes"].toString();
    bookmark.isFavorite = json["isFavorite"].toBool();
    bookmark.category = json["category"].toString();
    
    // Viewport rect
    QJsonObject viewportObj = json["viewportRect"].toObject();
    bookmark.viewportRect = QRectF(
        viewportObj["x"].toDouble(),
        viewportObj["y"].toDouble(),
        viewportObj["width"].toDouble(),
        viewportObj["height"].toDouble()
    );
    
    return bookmark;
}

void EnhancedBookmark::generateId()
{
    id = QUuid::createUuid().toString(QUuid::WithoutBraces);
}

void EnhancedBookmark::updateAccess()
{
    lastAccessed = QDateTime::currentDateTime();
    accessCount++;
}

// BookmarkManager implementation
BookmarkManager::BookmarkManager(QObject *parent)
    : QObject(parent)
    , m_settings(new QSettings(this))
    , m_autoSaveEnabled(true)
    , m_maxBookmarks(DEFAULT_MAX_BOOKMARKS)
{
    // Register the custom type
    qRegisterMetaType<EnhancedBookmark>("EnhancedBookmark");
    
    // Load existing bookmarks
    loadFromSettings();
}

BookmarkManager::~BookmarkManager()
{
    if (m_autoSaveEnabled) {
        saveToSettings();
    }
}

QString BookmarkManager::addBookmark(const EnhancedBookmark& bookmark)
{
    if (!isValidBookmark(bookmark)) {
        qWarning() << "Invalid bookmark provided to addBookmark";
        return QString();
    }
    
    EnhancedBookmark newBookmark = bookmark;
    
    // Generate ID if not provided
    if (newBookmark.id.isEmpty()) {
        newBookmark.generateId();
    }
    
    // Ensure unique ID
    while (bookmarkExists(newBookmark.id)) {
        newBookmark.generateId();
    }
    
    // Set file name if not provided
    if (newBookmark.fileName.isEmpty()) {
        newBookmark.fileName = QFileInfo(newBookmark.filePath).fileName();
    }
    
    // Set creation time if not provided
    if (!newBookmark.created.isValid()) {
        newBookmark.created = QDateTime::currentDateTime();
    }
    
    // Set last accessed time
    newBookmark.lastAccessed = QDateTime::currentDateTime();
    
    // Add to list
    m_bookmarks.prepend(newBookmark);
    
    // Trim if necessary
    trimBookmarks();
    
    emit bookmarkAdded(newBookmark);
    
    if (m_autoSaveEnabled) {
        saveToSettings();
    }
    
    return newBookmark.id;
}

bool BookmarkManager::updateBookmark(const QString& id, const EnhancedBookmark& bookmark)
{
    int index = findBookmarkIndex(id);
    if (index == -1) {
        return false;
    }
    
    if (!isValidBookmark(bookmark)) {
        return false;
    }
    
    EnhancedBookmark updatedBookmark = bookmark;
    updatedBookmark.id = id; // Preserve original ID
    
    m_bookmarks[index] = updatedBookmark;
    
    emit bookmarkUpdated(updatedBookmark);
    
    if (m_autoSaveEnabled) {
        saveToSettings();
    }
    
    return true;
}

bool BookmarkManager::removeBookmark(const QString& id)
{
    int index = findBookmarkIndex(id);
    if (index == -1) {
        return false;
    }
    
    m_bookmarks.removeAt(index);
    
    emit bookmarkRemoved(id);
    
    if (m_autoSaveEnabled) {
        saveToSettings();
    }
    
    return true;
}

bool BookmarkManager::removeBookmarksForDocument(const QString& filePath)
{
    bool removed = false;
    
    auto it = std::remove_if(m_bookmarks.begin(), m_bookmarks.end(),
                            [&filePath, &removed](const EnhancedBookmark& bookmark) {
                                if (bookmark.filePath == filePath) {
                                    removed = true;
                                    return true;
                                }
                                return false;
                            });
    
    if (it != m_bookmarks.end()) {
        m_bookmarks.erase(it, m_bookmarks.end());
        
        if (m_autoSaveEnabled) {
            saveToSettings();
        }
    }
    
    return removed;
}

void BookmarkManager::clearAllBookmarks()
{
    m_bookmarks.clear();
    emit bookmarksCleared();
    
    if (m_autoSaveEnabled) {
        saveToSettings();
    }
}

EnhancedBookmark BookmarkManager::getBookmark(const QString& id) const
{
    int index = findBookmarkIndex(id);
    if (index != -1) {
        return m_bookmarks[index];
    }
    return EnhancedBookmark();
}

QList<EnhancedBookmark> BookmarkManager::getAllBookmarks() const
{
    return m_bookmarks;
}

QList<EnhancedBookmark> BookmarkManager::getBookmarksForDocument(const QString& filePath) const
{
    QList<EnhancedBookmark> result;
    for (const auto& bookmark : m_bookmarks) {
        if (bookmark.filePath == filePath) {
            result.append(bookmark);
        }
    }
    return result;
}

QList<EnhancedBookmark> BookmarkManager::getFavoriteBookmarks() const
{
    QList<EnhancedBookmark> result;
    for (const auto& bookmark : m_bookmarks) {
        if (bookmark.isFavorite) {
            result.append(bookmark);
        }
    }
    return result;
}

QList<EnhancedBookmark> BookmarkManager::getRecentBookmarks(int maxCount) const
{
    QList<EnhancedBookmark> sorted = sortBookmarks(m_bookmarks, SortOrder::LastAccessed, false);
    return sorted.mid(0, qMin(maxCount, sorted.size()));
}

QList<EnhancedBookmark> BookmarkManager::getMostAccessedBookmarks(int maxCount) const
{
    QList<EnhancedBookmark> sorted = sortBookmarks(m_bookmarks, SortOrder::AccessCount, false);
    return sorted.mid(0, qMin(maxCount, sorted.size()));
}

QList<EnhancedBookmark> BookmarkManager::searchBookmarks(const QString& query) const
{
    QList<EnhancedBookmark> result;
    QString lowerQuery = query.toLower();
    
    for (const auto& bookmark : m_bookmarks) {
        if (bookmark.name.toLower().contains(lowerQuery) ||
            bookmark.description.toLower().contains(lowerQuery) ||
            bookmark.fileName.toLower().contains(lowerQuery) ||
            bookmark.notes.toLower().contains(lowerQuery) ||
            bookmark.category.toLower().contains(lowerQuery) ||
            bookmark.tags.join(" ").toLower().contains(lowerQuery)) {
            result.append(bookmark);
        }
    }
    
    return result;
}

QList<EnhancedBookmark> BookmarkManager::filterBookmarks(FilterType filter, const QString& value) const
{
    QList<EnhancedBookmark> result;
    
    switch (filter) {
    case FilterType::All:
        return m_bookmarks;
        
    case FilterType::Favorites:
        return getFavoriteBookmarks();
        
    case FilterType::RecentlyAccessed:
        return getRecentBookmarks(20);
        
    case FilterType::MostAccessed:
        return getMostAccessedBookmarks(20);
        
    case FilterType::ByCategory:
        return getBookmarksByCategory(value);
        
    case FilterType::ByTag:
        return getBookmarksByTag(value);
        
    case FilterType::ByDocument:
        return getBookmarksForDocument(value);
    }
    
    return result;
}

QList<EnhancedBookmark> BookmarkManager::getBookmarksByCategory(const QString& category) const
{
    QList<EnhancedBookmark> result;
    for (const auto& bookmark : m_bookmarks) {
        if (bookmark.category == category) {
            result.append(bookmark);
        }
    }
    return result;
}

QList<EnhancedBookmark> BookmarkManager::getBookmarksByTag(const QString& tag) const
{
    QList<EnhancedBookmark> result;
    for (const auto& bookmark : m_bookmarks) {
        if (bookmark.tags.contains(tag)) {
            result.append(bookmark);
        }
    }
    return result;
}
