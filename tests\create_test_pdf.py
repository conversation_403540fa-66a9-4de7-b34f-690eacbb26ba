#!/usr/bin/env python3
"""
Script to create a simple test PDF file for unit testing.
Requires reportlab: pip install reportlab
"""

try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.lib import colors
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet
    import os
except ImportError:
    print("reportlab not available. Please install with: pip install reportlab")
    print("Alternatively, provide your own test.pdf file in the project directory.")
    exit(1)

def create_test_pdf(filename="test.pdf"):
    """Create a simple test PDF with multiple pages and text content."""
    
    # Create the PDF
    doc = SimpleDocTemplate(filename, pagesize=A4)
    styles = getSampleStyleSheet()
    story = []
    
    # Page 1
    title = Paragraph("Test PDF Document", styles['Title'])
    story.append(title)
    story.append(Spacer(1, 12))
    
    content1 = Paragraph("""
    This is a test PDF document created for unit testing the Qt PDF Viewer application.
    It contains multiple pages with various text content that can be used to test
    search functionality, annotation features, and page rendering.
    """, styles['Normal'])
    story.append(content1)
    story.append(Spacer(1, 12))
    
    content2 = Paragraph("""
    This paragraph contains some searchable text that can be used to test the search
    functionality. Keywords like "annotation", "highlight", "test", and "search" 
    should be findable by the search feature.
    """, styles['Normal'])
    story.append(content2)
    story.append(Spacer(1, 12))
    
    # Add page break
    from reportlab.platypus import PageBreak
    story.append(PageBreak())
    
    # Page 2
    title2 = Paragraph("Second Page", styles['Heading1'])
    story.append(title2)
    story.append(Spacer(1, 12))
    
    content3 = Paragraph("""
    This is the second page of the test document. It provides additional content
    for testing multi-page functionality, page navigation, and cross-page operations
    like annotation management and search across multiple pages.
    """, styles['Normal'])
    story.append(content3)
    story.append(Spacer(1, 12))
    
    content4 = Paragraph("""
    Additional test content with more keywords: PDF, viewer, document, page, zoom,
    rotation, thumbnail, outline, bookmark. This text can be used to verify that
    text extraction and search functionality works correctly across different pages.
    """, styles['Normal'])
    story.append(content4)
    
    # Add page break
    story.append(PageBreak())
    
    # Page 3
    title3 = Paragraph("Third Page", styles['Heading1'])
    story.append(title3)
    story.append(Spacer(1, 12))
    
    content5 = Paragraph("""
    This is the third and final page of the test document. It provides content
    for testing the complete functionality of the PDF viewer including:
    - Page rendering and caching
    - Annotation overlay and management
    - Search and navigation features
    - Memory management and performance
    """, styles['Normal'])
    story.append(content5)
    
    # Build the PDF
    doc.build(story)
    print(f"Created test PDF: {filename}")
    return filename

def create_simple_test_pdf(filename="simple_test.pdf"):
    """Create a very simple test PDF using low-level canvas."""
    
    c = canvas.Canvas(filename, pagesize=letter)
    width, height = letter
    
    # Page 1
    c.drawString(100, height - 100, "Simple Test PDF")
    c.drawString(100, height - 130, "This is page 1 of a simple test document.")
    c.drawString(100, height - 160, "It contains basic text for testing PDF functionality.")
    c.drawString(100, height - 190, "Keywords: test, annotation, highlight, search")
    
    # Add some shapes for visual testing
    c.setStrokeColor(colors.red)
    c.setFillColor(colors.yellow)
    c.rect(100, height - 300, 200, 50, fill=1)
    
    c.setFillColor(colors.black)
    c.drawString(110, height - 280, "Yellow rectangle for visual testing")
    
    # Page 2
    c.showPage()
    c.drawString(100, height - 100, "Page 2")
    c.drawString(100, height - 130, "This is the second page with more test content.")
    c.drawString(100, height - 160, "Additional keywords: PDF, viewer, document, page")
    
    # Add a circle
    c.setStrokeColor(colors.blue)
    c.setFillColor(colors.lightblue)
    c.circle(200, height - 250, 30, fill=1)
    
    c.setFillColor(colors.black)
    c.drawString(100, height - 300, "Blue circle for annotation testing")
    
    # Save the PDF
    c.save()
    print(f"Created simple test PDF: {filename}")
    return filename

if __name__ == "__main__":
    # Create test PDFs in the current directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Try to create a full-featured test PDF
    try:
        test_pdf = create_test_pdf(os.path.join(current_dir, "test.pdf"))
        print(f"Successfully created: {test_pdf}")
    except Exception as e:
        print(f"Failed to create full test PDF: {e}")
        
        # Fall back to simple PDF
        try:
            simple_pdf = create_simple_test_pdf(os.path.join(current_dir, "test.pdf"))
            print(f"Created simple test PDF instead: {simple_pdf}")
        except Exception as e2:
            print(f"Failed to create simple test PDF: {e2}")
            print("Please provide a test.pdf file manually for testing.")
    
    # Also create a copy in the project root for convenience
    try:
        root_dir = os.path.dirname(current_dir)
        if os.path.exists(os.path.join(current_dir, "test.pdf")):
            import shutil
            shutil.copy(os.path.join(current_dir, "test.pdf"), 
                       os.path.join(root_dir, "test.pdf"))
            print(f"Copied test.pdf to project root: {root_dir}")
    except Exception as e:
        print(f"Could not copy to project root: {e}")
