#include "AnnotationManager.h"
#include <QPainter>
#include <QJsonDocument>
#include <QFile>
#include <QFileInfo>
#include <QDir>
#include <QUndoCommand>
#include <QDebug>
#include <algorithm>

AnnotationManager::AnnotationManager(QObject *parent)
    : QObject(parent)
    , m_undoStack(new QUndoStack(this))
{
}

AnnotationManager::~AnnotationManager()
{
    clearAnnotations();
}

void AnnotationManager::addAnnotation(std::unique_ptr<Annotation> annotation)
{
    if (!annotation) return;

    Annotation* annotationPtr = annotation.release(); // Transfer ownership
    connectAnnotationSignals(annotationPtr);

    m_annotationIndex[annotationPtr->getId()] = annotationPtr;
    m_annotations.append(annotationPtr);

    emit annotationAdded(annotationPtr);
}

void AnnotationManager::removeAnnotation(const QString& annotationId)
{
    auto it = std::find_if(m_annotations.begin(), m_annotations.end(),
        [&annotationId](Annotation* annotation) {
            return annotation->getId() == annotationId;
        });

    if (it != m_annotations.end()) {
        Annotation* annotationPtr = *it;
        disconnectAnnotationSignals(annotationPtr);

        // Remove from selection if selected
        m_selectedAnnotations.removeAll(annotationPtr);

        // Remove from index
        m_annotationIndex.remove(annotationId);

        // Remove from list and delete the annotation
        m_annotations.erase(it);
        delete annotationPtr;

        emit annotationRemoved(annotationId);
    }
}

void AnnotationManager::removeAnnotation(Annotation* annotation)
{
    if (annotation) {
        removeAnnotation(annotation->getId());
    }
}

void AnnotationManager::clearAnnotations()
{
    for (auto& annotation : m_annotations) {
        disconnectAnnotationSignals(annotation);
        delete annotation;
    }

    m_annotations.clear();
    m_annotationIndex.clear();
    m_selectedAnnotations.clear();

    emit annotationsCleared();
}

void AnnotationManager::clearAnnotationsForPage(int pageNumber)
{
    auto it = m_annotations.begin();
    while (it != m_annotations.end()) {
        if ((*it)->getPageNumber() == pageNumber) {
            Annotation* annotationPtr = *it;
            disconnectAnnotationSignals(annotationPtr);

            // Remove from selection if selected
            m_selectedAnnotations.removeAll(annotationPtr);

            // Remove from index
            m_annotationIndex.remove(annotationPtr->getId());

            QString annotationId = annotationPtr->getId();
            it = m_annotations.erase(it);
            delete annotationPtr;

            emit annotationRemoved(annotationId);
        } else {
            ++it;
        }
    }
}

QList<Annotation*> AnnotationManager::getAnnotations() const
{
    return m_annotations;
}

QList<Annotation*> AnnotationManager::getAnnotationsForPage(int pageNumber) const
{
    QList<Annotation*> result;
    for (const auto& annotation : m_annotations) {
        if (annotation->getPageNumber() == pageNumber) {
            result.append(annotation);
        }
    }
    return result;
}

Annotation* AnnotationManager::getAnnotation(const QString& annotationId) const
{
    return m_annotationIndex.value(annotationId, nullptr);
}

Annotation* AnnotationManager::getAnnotationAt(const QPointF& point, int pageNumber) const
{
    // Search in reverse order to get topmost annotation
    for (auto it = m_annotations.rbegin(); it != m_annotations.rend(); ++it) {
        Annotation* annotation = *it;
        if (annotation->getPageNumber() == pageNumber && annotation->contains(point)) {
            return annotation;
        }
    }
    return nullptr;
}

void AnnotationManager::selectAnnotation(Annotation* annotation)
{
    if (!annotation || m_selectedAnnotations.contains(annotation)) return;

    annotation->setSelected(true);
    m_selectedAnnotations.append(annotation);
    emit annotationSelected(annotation);
}

void AnnotationManager::selectAnnotation(const QString& annotationId)
{
    Annotation* annotation = getAnnotation(annotationId);
    if (annotation) {
        selectAnnotation(annotation);
    }
}

void AnnotationManager::deselectAll()
{
    for (Annotation* annotation : m_selectedAnnotations) {
        annotation->setSelected(false);
        emit annotationDeselected(annotation);
    }
    m_selectedAnnotations.clear();
}

QList<Annotation*> AnnotationManager::getSelectedAnnotations() const
{
    return m_selectedAnnotations;
}

Annotation* AnnotationManager::getSelectedAnnotation() const
{
    return m_selectedAnnotations.isEmpty() ? nullptr : m_selectedAnnotations.first();
}

HighlightAnnotation* AnnotationManager::createHighlight(int pageNumber, const QList<QRectF>& quads)
{
    auto highlight = AnnotationFactory::createHighlight(quads);
    highlight->setPageNumber(pageNumber);
    
    HighlightAnnotation* result = highlight.get();
    addAnnotation(std::move(highlight));
    return result;
}

NoteAnnotation* AnnotationManager::createNote(int pageNumber, const QPointF& position)
{
    auto note = AnnotationFactory::createNote(position);
    note->setPageNumber(pageNumber);
    
    NoteAnnotation* result = note.get();
    addAnnotation(std::move(note));
    return result;
}

DrawingAnnotation* AnnotationManager::createDrawing(int pageNumber)
{
    auto drawing = AnnotationFactory::createDrawing();
    drawing->setPageNumber(pageNumber);
    
    DrawingAnnotation* result = drawing.get();
    addAnnotation(std::move(drawing));
    return result;
}

ShapeAnnotation* AnnotationManager::createShape(int pageNumber, ShapeAnnotation::ShapeType shapeType, const QRectF& bounds)
{
    auto shape = AnnotationFactory::createShape(shapeType, bounds);
    shape->setPageNumber(pageNumber);
    
    ShapeAnnotation* result = shape.get();
    addAnnotation(std::move(shape));
    return result;
}

TextAnnotation* AnnotationManager::createText(int pageNumber, const QRectF& bounds, const QString& text)
{
    auto textAnnotation = AnnotationFactory::createText(bounds, text);
    textAnnotation->setPageNumber(pageNumber);
    
    TextAnnotation* result = textAnnotation.get();
    addAnnotation(std::move(textAnnotation));
    return result;
}

void AnnotationManager::renderAnnotations(QPainter* painter, int pageNumber, double dpi, double zoomFactor) const
{
    if (!painter) return;
    
    for (const auto& annotation : m_annotations) {
        if (annotation->getPageNumber() == pageNumber) {
            annotation->render(painter, dpi, zoomFactor);
        }
    }
}

bool AnnotationManager::saveAnnotations(const QString& filePath) const
{
    QJsonObject json = toJson();
    QJsonDocument doc(json);
    
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "Failed to open annotation file for writing:" << filePath;
        return false;
    }
    
    file.write(doc.toJson());
    return true;
}

bool AnnotationManager::loadAnnotations(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        // File doesn't exist or can't be opened - not necessarily an error
        return true;
    }
    
    QByteArray data = file.readAll();
    QJsonDocument doc = QJsonDocument::fromJson(data);
    
    if (doc.isNull() || !doc.isObject()) {
        qWarning() << "Invalid annotation file format:" << filePath;
        return false;
    }
    
    fromJson(doc.object());
    return true;
}

QJsonObject AnnotationManager::toJson() const
{
    QJsonObject json;
    json["version"] = "1.0";
    
    QJsonArray annotationsArray;
    for (const auto& annotation : m_annotations) {
        annotationsArray.append(annotation->toJson());
    }
    json["annotations"] = annotationsArray;
    
    return json;
}

void AnnotationManager::fromJson(const QJsonObject& json)
{
    clearAnnotations();
    
    QJsonArray annotationsArray = json["annotations"].toArray();
    for (const QJsonValue& value : annotationsArray) {
        QJsonObject annotationObj = value.toObject();
        auto annotation = createAnnotationFromJson(annotationObj);
        if (annotation) {
            addAnnotation(std::move(annotation));
        }
    }
}

int AnnotationManager::getAnnotationCount() const
{
    return m_annotations.size();
}

int AnnotationManager::getAnnotationCountForPage(int pageNumber) const
{
    int count = 0;
    for (const auto& annotation : m_annotations) {
        if (annotation->getPageNumber() == pageNumber) {
            count++;
        }
    }
    return count;
}

QHash<int, int> AnnotationManager::getAnnotationCountsByPage() const
{
    QHash<int, int> counts;
    for (const auto& annotation : m_annotations) {
        int pageNumber = annotation->getPageNumber();
        counts[pageNumber] = counts.value(pageNumber, 0) + 1;
    }
    return counts;
}

QString AnnotationManager::generateAnnotationFilePath(const QString& pdfFilePath) const
{
    QFileInfo pdfInfo(pdfFilePath);
    QString baseName = pdfInfo.completeBaseName();
    QString dir = pdfInfo.absolutePath();
    
    // Create annotations subdirectory if it doesn't exist
    QString annotationsDir = QDir(dir).filePath("annotations");
    QDir().mkpath(annotationsDir);
    
    return QDir(annotationsDir).filePath(baseName + ".annotations.json");
}

void AnnotationManager::pushCommand(QUndoCommand* command)
{
    if (m_undoStack) {
        m_undoStack->push(command);
    }
}

void AnnotationManager::undo()
{
    if (m_undoStack) {
        m_undoStack->undo();
    }
}

void AnnotationManager::redo()
{
    if (m_undoStack) {
        m_undoStack->redo();
    }
}

bool AnnotationManager::canUndo() const
{
    return m_undoStack && m_undoStack->canUndo();
}

bool AnnotationManager::canRedo() const
{
    return m_undoStack && m_undoStack->canRedo();
}

QList<Annotation*> AnnotationManager::getAllAnnotations() const
{
    return m_annotations;
}

void AnnotationManager::connectAnnotationSignals(Annotation* annotation)
{
    if (!annotation) return;

    connect(annotation, &Annotation::annotationChanged,
            this, &AnnotationManager::onAnnotationChanged);
    connect(annotation, &Annotation::annotationSelected,
            this, &AnnotationManager::onAnnotationSelected);
}

void AnnotationManager::disconnectAnnotationSignals(Annotation* annotation)
{
    if (!annotation) return;

    disconnect(annotation, &Annotation::annotationChanged,
               this, &AnnotationManager::onAnnotationChanged);
    disconnect(annotation, &Annotation::annotationSelected,
               this, &AnnotationManager::onAnnotationSelected);
}

std::unique_ptr<Annotation> AnnotationManager::createAnnotationFromJson(const QJsonObject& json)
{
    AnnotationType type = static_cast<AnnotationType>(json["type"].toInt());
    auto annotation = AnnotationFactory::createAnnotation(type);
    if (annotation) {
        annotation->fromJson(json);
    }
    return annotation;
}

void AnnotationManager::onAnnotationChanged()
{
    Annotation* annotation = qobject_cast<Annotation*>(sender());
    if (annotation) {
        emit annotationChanged(annotation);
    }
}

void AnnotationManager::onAnnotationSelected(bool selected)
{
    Annotation* annotation = qobject_cast<Annotation*>(sender());
    if (annotation) {
        if (selected) {
            selectAnnotation(annotation);
        } else {
            m_selectedAnnotations.removeAll(annotation);
            emit annotationDeselected(annotation);
        }
    }
}

// AnnotationFactory implementation
std::unique_ptr<Annotation> AnnotationFactory::createAnnotation(AnnotationType type)
{
    switch (type) {
    case AnnotationType::Highlight:
        return std::make_unique<HighlightAnnotation>();
    case AnnotationType::Note:
        return std::make_unique<NoteAnnotation>();
    case AnnotationType::Drawing:
        return std::make_unique<DrawingAnnotation>();
    case AnnotationType::Rectangle:
        return std::make_unique<ShapeAnnotation>(ShapeAnnotation::ShapeType::Rectangle);
    case AnnotationType::Circle:
        return std::make_unique<ShapeAnnotation>(ShapeAnnotation::ShapeType::Circle);
    case AnnotationType::Arrow:
        return std::make_unique<ShapeAnnotation>(ShapeAnnotation::ShapeType::Arrow);
    case AnnotationType::Text:
        return std::make_unique<TextAnnotation>();
    }
    return nullptr;
}

std::unique_ptr<Annotation> AnnotationFactory::createFromJson(const QJsonObject& json)
{
    AnnotationType type = static_cast<AnnotationType>(json["type"].toInt());
    auto annotation = createAnnotation(type);
    if (annotation) {
        annotation->fromJson(json);
    }
    return annotation;
}

std::unique_ptr<HighlightAnnotation> AnnotationFactory::createHighlight(const QList<QRectF>& quads)
{
    auto highlight = std::make_unique<HighlightAnnotation>();
    highlight->setQuads(quads);
    return highlight;
}

std::unique_ptr<NoteAnnotation> AnnotationFactory::createNote(const QPointF& position)
{
    auto note = std::make_unique<NoteAnnotation>();
    note->setPosition(position);
    return note;
}

std::unique_ptr<DrawingAnnotation> AnnotationFactory::createDrawing()
{
    return std::make_unique<DrawingAnnotation>();
}

std::unique_ptr<ShapeAnnotation> AnnotationFactory::createShape(ShapeAnnotation::ShapeType shapeType, const QRectF& bounds)
{
    auto shape = std::make_unique<ShapeAnnotation>(shapeType);
    shape->setRect(bounds);
    return shape;
}

std::unique_ptr<TextAnnotation> AnnotationFactory::createText(const QRectF& bounds, const QString& text)
{
    auto textAnnotation = std::make_unique<TextAnnotation>();
    textAnnotation->setRect(bounds);
    textAnnotation->setText(text);
    return textAnnotation;
}
