#include "AnnotationSearchDialog.h"
#include "AnnotationManager.h"
#include "AnnotationCommand.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLineEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QDateEdit>
#include <QListWidget>
#include <QPushButton>
#include <QLabel>
#include <QColorDialog>
#include <QFileDialog>
#include <QTextStream>
#include <QMessageBox>
#include <QSplitter>
#include <QHeaderView>
#include <QPixmap>
#include <QPainter>

AnnotationSearchDialog::AnnotationSearchDialog(AnnotationManager* manager, QWidget *parent)
    : QDialog(parent)
    , m_annotationManager(manager)
    , m_selectedColor(Qt::yellow)
{
    setWindowTitle(tr("Search Annotations"));
    setModal(false);
    resize(600, 500);
    
    setupUi();
    
    // Perform initial search to show all annotations
    performSearch();
}

AnnotationSearchDialog::~AnnotationSearchDialog()
{
}

void AnnotationSearchDialog::setupUi()
{
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    
    // Create splitter for search criteria and results
    QSplitter* splitter = new QSplitter(Qt::Vertical);
    
    // Search criteria section
    setupSearchCriteria();
    QWidget* criteriaWidget = new QWidget();
    QVBoxLayout* criteriaLayout = new QVBoxLayout(criteriaWidget);
    
    // Text search
    QGroupBox* textGroup = new QGroupBox(tr("Text Search"));
    QVBoxLayout* textLayout = new QVBoxLayout(textGroup);
    
    m_searchEdit = new QLineEdit();
    m_searchEdit->setPlaceholderText(tr("Search in annotation content..."));
    connect(m_searchEdit, &QLineEdit::textChanged, this, &AnnotationSearchDialog::onSearchTextChanged);
    textLayout->addWidget(m_searchEdit);
    
    QHBoxLayout* textOptionsLayout = new QHBoxLayout();
    m_caseSensitiveCheckBox = new QCheckBox(tr("Case sensitive"));
    m_wholeWordsCheckBox = new QCheckBox(tr("Whole words only"));
    textOptionsLayout->addWidget(m_caseSensitiveCheckBox);
    textOptionsLayout->addWidget(m_wholeWordsCheckBox);
    textOptionsLayout->addStretch();
    textLayout->addLayout(textOptionsLayout);
    
    criteriaLayout->addWidget(textGroup);
    
    // Filters
    QGroupBox* filtersGroup = new QGroupBox(tr("Filters"));
    QGridLayout* filtersLayout = new QGridLayout(filtersGroup);
    
    // Type filter
    m_typeFilterCheckBox = new QCheckBox(tr("Type:"));
    m_typeComboBox = new QComboBox();
    m_typeComboBox->addItem(tr("Highlight"), static_cast<int>(AnnotationType::Highlight));
    m_typeComboBox->addItem(tr("Note"), static_cast<int>(AnnotationType::Note));
    m_typeComboBox->addItem(tr("Drawing"), static_cast<int>(AnnotationType::Drawing));
    m_typeComboBox->addItem(tr("Rectangle"), static_cast<int>(AnnotationType::Rectangle));
    m_typeComboBox->addItem(tr("Circle"), static_cast<int>(AnnotationType::Circle));
    m_typeComboBox->addItem(tr("Arrow"), static_cast<int>(AnnotationType::Arrow));
    m_typeComboBox->addItem(tr("Text"), static_cast<int>(AnnotationType::Text));
    m_typeComboBox->setEnabled(false);
    connect(m_typeFilterCheckBox, &QCheckBox::toggled, m_typeComboBox, &QComboBox::setEnabled);
    filtersLayout->addWidget(m_typeFilterCheckBox, 0, 0);
    filtersLayout->addWidget(m_typeComboBox, 0, 1);
    
    // Author filter
    m_authorFilterCheckBox = new QCheckBox(tr("Author:"));
    m_authorEdit = new QLineEdit();
    m_authorEdit->setEnabled(false);
    connect(m_authorFilterCheckBox, &QCheckBox::toggled, m_authorEdit, &QLineEdit::setEnabled);
    filtersLayout->addWidget(m_authorFilterCheckBox, 1, 0);
    filtersLayout->addWidget(m_authorEdit, 1, 1);
    
    // Color filter
    m_colorFilterCheckBox = new QCheckBox(tr("Color:"));
    m_colorButton = new QPushButton();
    m_colorButton->setFixedSize(50, 25);
    m_colorButton->setEnabled(false);
    updateColorButton();
    connect(m_colorFilterCheckBox, &QCheckBox::toggled, m_colorButton, &QPushButton::setEnabled);
    connect(m_colorButton, &QPushButton::clicked, this, [this]() {
        QColorDialog dialog(m_selectedColor, this);
        if (dialog.exec() == QDialog::Accepted) {
            m_selectedColor = dialog.currentColor();
            updateColorButton();
        }
    });
    filtersLayout->addWidget(m_colorFilterCheckBox, 2, 0);
    filtersLayout->addWidget(m_colorButton, 2, 1);
    
    // Date filter
    m_dateFilterCheckBox = new QCheckBox(tr("Created:"));
    QHBoxLayout* dateLayout = new QHBoxLayout();
    m_createdAfterEdit = new QDateEdit();
    m_createdAfterEdit->setDate(QDate::currentDate().addDays(-30));
    m_createdAfterEdit->setEnabled(false);
    m_createdBeforeEdit = new QDateEdit();
    m_createdBeforeEdit->setDate(QDate::currentDate());
    m_createdBeforeEdit->setEnabled(false);
    connect(m_dateFilterCheckBox, &QCheckBox::toggled, m_createdAfterEdit, &QDateEdit::setEnabled);
    connect(m_dateFilterCheckBox, &QCheckBox::toggled, m_createdBeforeEdit, &QDateEdit::setEnabled);
    dateLayout->addWidget(new QLabel(tr("From:")));
    dateLayout->addWidget(m_createdAfterEdit);
    dateLayout->addWidget(new QLabel(tr("To:")));
    dateLayout->addWidget(m_createdBeforeEdit);
    filtersLayout->addWidget(m_dateFilterCheckBox, 3, 0);
    filtersLayout->addLayout(dateLayout, 3, 1);
    
    // Page filter
    m_pageFilterCheckBox = new QCheckBox(tr("Page:"));
    m_pageComboBox = new QComboBox();
    m_pageComboBox->addItem(tr("All pages"), -1);
    if (m_annotationManager) {
        // Add page numbers based on annotations
        QSet<int> pages;
        for (Annotation* annotation : m_annotationManager->getAllAnnotations()) {
            pages.insert(annotation->getPageNumber());
        }
        QList<int> sortedPages = pages.values();
        std::sort(sortedPages.begin(), sortedPages.end());
        for (int page : sortedPages) {
            m_pageComboBox->addItem(tr("Page %1").arg(page + 1), page);
        }
    }
    m_pageComboBox->setEnabled(false);
    connect(m_pageFilterCheckBox, &QCheckBox::toggled, m_pageComboBox, &QComboBox::setEnabled);
    filtersLayout->addWidget(m_pageFilterCheckBox, 4, 0);
    filtersLayout->addWidget(m_pageComboBox, 4, 1);
    
    criteriaLayout->addWidget(filtersGroup);
    
    // Search buttons
    QHBoxLayout* searchButtonsLayout = new QHBoxLayout();
    m_searchButton = new QPushButton(tr("Search"));
    m_clearButton = new QPushButton(tr("Clear"));
    connect(m_searchButton, &QPushButton::clicked, this, &AnnotationSearchDialog::performSearch);
    connect(m_clearButton, &QPushButton::clicked, this, &AnnotationSearchDialog::clearSearch);
    searchButtonsLayout->addWidget(m_searchButton);
    searchButtonsLayout->addWidget(m_clearButton);
    searchButtonsLayout->addStretch();
    criteriaLayout->addLayout(searchButtonsLayout);
    
    splitter->addWidget(criteriaWidget);
    
    // Results section
    setupResultsList();
    splitter->addWidget(m_resultsList);
    
    // Set splitter proportions
    splitter->setStretchFactor(0, 0);
    splitter->setStretchFactor(1, 1);
    
    mainLayout->addWidget(splitter);
    
    // Bottom buttons
    setupButtons();
    QHBoxLayout* buttonsLayout = new QHBoxLayout();
    buttonsLayout->addWidget(m_resultsCountLabel);
    buttonsLayout->addStretch();
    buttonsLayout->addWidget(m_navigateButton);
    buttonsLayout->addWidget(m_selectButton);
    buttonsLayout->addWidget(m_exportButton);
    buttonsLayout->addWidget(m_closeButton);
    mainLayout->addLayout(buttonsLayout);
}

void AnnotationSearchDialog::setupSearchCriteria()
{
    // Already implemented in setupUi()
}

void AnnotationSearchDialog::setupResultsList()
{
    m_resultsList = new QListWidget();
    connect(m_resultsList, &QListWidget::itemClicked, this, &AnnotationSearchDialog::onResultItemClicked);
    connect(m_resultsList, &QListWidget::itemDoubleClicked, this, &AnnotationSearchDialog::onResultItemDoubleClicked);
}

void AnnotationSearchDialog::setupButtons()
{
    m_resultsCountLabel = new QLabel();
    
    m_navigateButton = new QPushButton(tr("Navigate"));
    m_navigateButton->setEnabled(false);
    connect(m_navigateButton, &QPushButton::clicked, this, &AnnotationSearchDialog::onNavigateToAnnotation);
    
    m_selectButton = new QPushButton(tr("Select"));
    m_selectButton->setEnabled(false);
    connect(m_selectButton, &QPushButton::clicked, this, &AnnotationSearchDialog::onSelectAnnotation);
    
    m_exportButton = new QPushButton(tr("Export..."));
    connect(m_exportButton, &QPushButton::clicked, this, &AnnotationSearchDialog::onExportResults);
    
    m_closeButton = new QPushButton(tr("Close"));
    connect(m_closeButton, &QPushButton::clicked, this, &QDialog::accept);
}

void AnnotationSearchDialog::updateColorButton()
{
    QPixmap pixmap(40, 20);
    pixmap.fill(m_selectedColor);
    
    QPainter painter(&pixmap);
    painter.setPen(Qt::black);
    painter.drawRect(pixmap.rect().adjusted(0, 0, -1, -1));
    
    m_colorButton->setIcon(QIcon(pixmap));
}

void AnnotationSearchDialog::performSearch()
{
    if (!m_annotationManager) return;

    m_searchResults.clear();
    AnnotationSearchCriteria criteria = getCriteria();

    QList<Annotation*> allAnnotations = m_annotationManager->getAllAnnotations();
    for (Annotation* annotation : allAnnotations) {
        if (matchesCriteria(annotation, criteria)) {
            m_searchResults.append(annotation);
        }
    }

    updateResultsList();
    updateResultsCount();
}

void AnnotationSearchDialog::clearSearch()
{
    m_searchEdit->clear();
    m_typeFilterCheckBox->setChecked(false);
    m_authorFilterCheckBox->setChecked(false);
    m_colorFilterCheckBox->setChecked(false);
    m_dateFilterCheckBox->setChecked(false);
    m_pageFilterCheckBox->setChecked(false);
    m_caseSensitiveCheckBox->setChecked(false);
    m_wholeWordsCheckBox->setChecked(false);

    performSearch(); // Show all annotations
}

void AnnotationSearchDialog::onSearchTextChanged()
{
    // Auto-search as user types (with small delay would be better)
    performSearch();
}

void AnnotationSearchDialog::onResultItemClicked(QListWidgetItem* item)
{
    m_navigateButton->setEnabled(item != nullptr);
    m_selectButton->setEnabled(item != nullptr);
}

void AnnotationSearchDialog::onResultItemDoubleClicked(QListWidgetItem* item)
{
    Q_UNUSED(item);
    onNavigateToAnnotation();
}

void AnnotationSearchDialog::onNavigateToAnnotation()
{
    Annotation* annotation = getCurrentSelectedAnnotation();
    if (annotation) {
        emit navigateToAnnotation(annotation);
    }
}

void AnnotationSearchDialog::onSelectAnnotation()
{
    Annotation* annotation = getCurrentSelectedAnnotation();
    if (annotation) {
        emit annotationSelected(annotation);
    }
}

void AnnotationSearchDialog::onExportResults()
{
    if (m_searchResults.isEmpty()) {
        QMessageBox::information(this, tr("Export Results"), tr("No search results to export."));
        return;
    }

    QString fileName = QFileDialog::getSaveFileName(this, tr("Export Search Results"),
                                                   QString("annotation_search_results.txt"),
                                                   tr("Text Files (*.txt);;CSV Files (*.csv)"));
    if (fileName.isEmpty()) return;

    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::critical(this, tr("Export Error"), tr("Could not open file for writing."));
        return;
    }

    QTextStream out(&file);
    out << tr("Annotation Search Results") << "\n";
    out << tr("Generated: %1").arg(QDateTime::currentDateTime().toString()) << "\n";
    out << tr("Total Results: %1").arg(m_searchResults.size()) << "\n";
    out << QString(50, '=') << "\n\n";

    for (int i = 0; i < m_searchResults.size(); ++i) {
        Annotation* annotation = m_searchResults[i];
        out << tr("%1. %2").arg(i + 1).arg(getAnnotationTypeName(annotation->getType())) << "\n";
        out << tr("   Page: %1").arg(annotation->getPageNumber() + 1) << "\n";
        out << tr("   Author: %1").arg(annotation->getAuthor()) << "\n";
        out << tr("   Created: %1").arg(annotation->getCreationDate().toString()) << "\n";

        if (!annotation->getContent().isEmpty()) {
            out << tr("   Content: %1").arg(annotation->getContent()) << "\n";
        }

        out << "\n";
    }

    QMessageBox::information(this, tr("Export Complete"),
                           tr("Search results exported to %1").arg(fileName));
}

Annotation* AnnotationSearchDialog::getCurrentSelectedAnnotation() const
{
    QListWidgetItem* currentItem = m_resultsList->currentItem();
    if (!currentItem) return nullptr;

    int index = m_resultsList->row(currentItem);
    if (index >= 0 && index < m_searchResults.size()) {
        return m_searchResults[index];
    }

    return nullptr;
}

void AnnotationSearchDialog::updateResultsList()
{
    m_resultsList->clear();

    for (Annotation* annotation : m_searchResults) {
        QString displayText = formatAnnotationForDisplay(annotation);
        QListWidgetItem* item = new QListWidgetItem(displayText);
        item->setIcon(getAnnotationIcon(annotation->getType()));
        m_resultsList->addItem(item);
    }
}

void AnnotationSearchDialog::updateResultsCount()
{
    m_resultsCountLabel->setText(tr("%1 result(s) found").arg(m_searchResults.size()));
}

AnnotationSearchCriteria AnnotationSearchDialog::getCriteria() const
{
    AnnotationSearchCriteria criteria;

    criteria.searchText = m_searchEdit->text();
    criteria.caseSensitive = m_caseSensitiveCheckBox->isChecked();
    criteria.wholeWords = m_wholeWordsCheckBox->isChecked();

    criteria.typeFilter = m_typeFilterCheckBox->isChecked();
    if (criteria.typeFilter) {
        criteria.annotationType = static_cast<AnnotationType>(m_typeComboBox->currentData().toInt());
    }

    criteria.authorFilter = m_authorFilterCheckBox->isChecked();
    if (criteria.authorFilter) {
        criteria.author = m_authorEdit->text();
    }

    criteria.colorFilter = m_colorFilterCheckBox->isChecked();
    if (criteria.colorFilter) {
        criteria.color = m_selectedColor;
    }

    criteria.dateFilter = m_dateFilterCheckBox->isChecked();
    if (criteria.dateFilter) {
        criteria.createdAfter = QDateTime(m_createdAfterEdit->date(), QTime(0, 0, 0));
        criteria.createdBefore = QDateTime(m_createdBeforeEdit->date().addDays(1), QTime(0, 0, 0)); // Include the whole day
    }

    criteria.pageFilter = m_pageFilterCheckBox->isChecked();
    if (criteria.pageFilter) {
        criteria.pageNumber = m_pageComboBox->currentData().toInt();
    }

    return criteria;
}

bool AnnotationSearchDialog::matchesCriteria(Annotation* annotation, const AnnotationSearchCriteria& criteria) const
{
    if (!annotation) return false;

    // Text search
    if (!criteria.searchText.isEmpty()) {
        QString content = annotation->getContent();
        QString searchText = criteria.searchText;

        if (!criteria.caseSensitive) {
            content = content.toLower();
            searchText = searchText.toLower();
        }

        if (criteria.wholeWords) {
            QStringList words = searchText.split(QRegularExpression("\\s+"), Qt::SkipEmptyParts);
            bool allWordsFound = true;
            for (const QString& word : words) {
                QRegularExpression wordRegex(QString("\\b%1\\b").arg(QRegularExpression::escape(word)));
                if (!criteria.caseSensitive) {
                    wordRegex.setPatternOptions(QRegularExpression::CaseInsensitiveOption);
                }
                if (!content.contains(wordRegex)) {
                    allWordsFound = false;
                    break;
                }
            }
            if (!allWordsFound) return false;
        } else {
            if (!content.contains(searchText)) return false;
        }
    }

    // Type filter
    if (criteria.typeFilter && annotation->getType() != criteria.annotationType) {
        return false;
    }

    // Author filter
    if (criteria.authorFilter && !criteria.author.isEmpty()) {
        QString author = annotation->getAuthor();
        if (!author.contains(criteria.author, criteria.caseSensitive ? Qt::CaseSensitive : Qt::CaseInsensitive)) {
            return false;
        }
    }

    // Color filter
    if (criteria.colorFilter) {
        QColor annotationColor = annotation->getColor();
        if (annotationColor != criteria.color) {
            return false;
        }
    }

    // Date filter
    if (criteria.dateFilter) {
        QDateTime creationDate = annotation->getCreationDate();
        if (creationDate < criteria.createdAfter || creationDate >= criteria.createdBefore) {
            return false;
        }
    }

    // Page filter
    if (criteria.pageFilter && criteria.pageNumber >= 0) {
        if (annotation->getPageNumber() != criteria.pageNumber) {
            return false;
        }
    }

    return true;
}

QString AnnotationSearchDialog::formatAnnotationForDisplay(Annotation* annotation) const
{
    if (!annotation) return QString();

    QString typeName = getAnnotationTypeName(annotation->getType());
    QString content = annotation->getContent();
    QString author = annotation->getAuthor();
    int page = annotation->getPageNumber() + 1;

    QString displayText = QString("%1 (Page %2)").arg(typeName).arg(page);

    if (!author.isEmpty()) {
        displayText += QString(" - %1").arg(author);
    }

    if (!content.isEmpty()) {
        QString truncatedContent = content.length() > 50 ?
                                  content.left(47) + "..." : content;
        displayText += QString(": %1").arg(truncatedContent);
    }

    return displayText;
}

QIcon AnnotationSearchDialog::getAnnotationIcon(AnnotationType type) const
{
    // Create simple colored icons for different annotation types
    QPixmap pixmap(16, 16);
    QColor iconColor;

    switch (type) {
    case AnnotationType::Highlight:
        iconColor = Qt::yellow;
        break;
    case AnnotationType::Note:
        iconColor = Qt::cyan;
        break;
    case AnnotationType::Drawing:
        iconColor = Qt::red;
        break;
    case AnnotationType::Rectangle:
        iconColor = Qt::blue;
        break;
    case AnnotationType::Circle:
        iconColor = Qt::green;
        break;
    case AnnotationType::Arrow:
        iconColor = Qt::magenta;
        break;
    case AnnotationType::Text:
        iconColor = Qt::black;
        break;
    default:
        iconColor = Qt::gray;
        break;
    }

    pixmap.fill(iconColor);

    QPainter painter(&pixmap);
    painter.setPen(Qt::black);
    painter.drawRect(pixmap.rect().adjusted(0, 0, -1, -1));

    return QIcon(pixmap);
}
