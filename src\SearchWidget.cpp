#include "SearchWidget.h"
#include <QToolButton>
#include <QProgressBar>
#include <QApplication>
#include <QStyle>
#include <QKeyEvent>
#include <QDebug>

SearchWidget::SearchWidget(QWidget *parent)
    : QWidget(parent)
    , m_searchHistory(nullptr)
    , m_searchInProgress(false)
    , m_currentResultIndex(-1)
    , m_totalResults(0)
    , m_autoSearchEnabled(true)
    , m_autoSearchDelay(500)
    , m_autoSearchTimer(new QTimer(this))
{
    setupUi();
    connectSignals();
    applySearchStyling();
    
    // Configure auto-search timer
    m_autoSearchTimer->setSingleShot(true);
    connect(m_autoSearchTimer, &QTimer::timeout, this, &SearchWidget::onAutoSearchTimeout);
}

SearchWidget::~SearchWidget() = default;

void SearchWidget::setupUi()
{
    m_mainLayout = new QHBoxLayout(this);
    m_mainLayout->setContentsMargins(4, 4, 4, 4);
    m_mainLayout->setSpacing(4);
    
    setupSearchField();
    setupNavigationButtons();
    setupOptionsCheckboxes();
    setupHistoryButton();
    setupProgressIndicator();
    
    setMaximumHeight(40);
}

void SearchWidget::setupSearchField()
{
    // Search input field
    m_searchEdit = new QLineEdit(this);
    m_searchEdit->setPlaceholderText(tr("Search in document..."));
    m_searchEdit->setMinimumWidth(200);
    m_searchEdit->setMaximumWidth(300);
    
    // Setup completer
    m_completerModel = new QStringListModel(this);
    m_completer = new QCompleter(m_completerModel, this);
    m_completer->setCaseSensitivity(Qt::CaseInsensitive);
    m_completer->setFilterMode(Qt::MatchContains);
    m_searchEdit->setCompleter(m_completer);
    
    // Search button
    m_searchButton = new QPushButton(tr("Search"), this);
    m_searchButton->setDefault(true);
    m_searchButton->setEnabled(false);
    
    // Clear button
    m_clearButton = new QPushButton(tr("Clear"), this);
    m_clearButton->setEnabled(false);
    
    m_mainLayout->addWidget(m_searchEdit);
    m_mainLayout->addWidget(m_searchButton);
    m_mainLayout->addWidget(m_clearButton);
}

void SearchWidget::setupNavigationButtons()
{
    // Previous result button
    m_findPrevButton = new QPushButton(tr("◀"), this);
    m_findPrevButton->setToolTip(tr("Previous result (Shift+F3)"));
    m_findPrevButton->setMaximumWidth(32);
    m_findPrevButton->setEnabled(false);
    
    // Next result button
    m_findNextButton = new QPushButton(tr("▶"), this);
    m_findNextButton->setToolTip(tr("Next result (F3)"));
    m_findNextButton->setMaximumWidth(32);
    m_findNextButton->setEnabled(false);
    
    // Results label
    m_resultsLabel = new QLabel(this);
    m_resultsLabel->setMinimumWidth(100);
    m_resultsLabel->setAlignment(Qt::AlignCenter);
    m_resultsLabel->setStyleSheet("color: #666666; font-size: 11px;");
    
    m_mainLayout->addWidget(m_findPrevButton);
    m_mainLayout->addWidget(m_findNextButton);
    m_mainLayout->addWidget(m_resultsLabel);
}

void SearchWidget::setupOptionsCheckboxes()
{
    // Case sensitive option
    m_caseSensitiveCheckBox = new QCheckBox(tr("Case sensitive"), this);
    m_caseSensitiveCheckBox->setToolTip(tr("Match case when searching"));
    
    // Whole words option
    m_wholeWordsCheckBox = new QCheckBox(tr("Whole words"), this);
    m_wholeWordsCheckBox->setToolTip(tr("Match whole words only"));
    
    // Highlight all option
    m_highlightAllCheckBox = new QCheckBox(tr("Highlight all"), this);
    m_highlightAllCheckBox->setToolTip(tr("Highlight all search results"));
    m_highlightAllCheckBox->setChecked(true);
    
    m_mainLayout->addWidget(m_caseSensitiveCheckBox);
    m_mainLayout->addWidget(m_wholeWordsCheckBox);
    m_mainLayout->addWidget(m_highlightAllCheckBox);
}

void SearchWidget::setupHistoryButton()
{
    m_historyButton = new QToolButton(this);
    m_historyButton->setText(tr("History"));
    m_historyButton->setToolTip(tr("Search history"));
    m_historyButton->setPopupMode(QToolButton::InstantPopup);
    
    m_historyMenu = new QMenu(this);
    m_historyButton->setMenu(m_historyMenu);
    
    m_mainLayout->addWidget(m_historyButton);
}

void SearchWidget::setupProgressIndicator()
{
    m_progressBar = new QProgressBar(this);
    m_progressBar->setMaximumHeight(4);
    m_progressBar->setTextVisible(false);
    m_progressBar->setVisible(false);
    m_progressBar->setRange(0, 0); // Indeterminate progress
    
    m_mainLayout->addWidget(m_progressBar);
}

void SearchWidget::connectSignals()
{
    // Search field signals
    connect(m_searchEdit, &QLineEdit::textChanged, this, &SearchWidget::onSearchTextChanged);
    connect(m_searchEdit, &QLineEdit::textEdited, this, &SearchWidget::onSearchTextEdited);
    connect(m_searchEdit, &QLineEdit::returnPressed, this, &SearchWidget::performSearch);
    
    // Button signals
    connect(m_searchButton, &QPushButton::clicked, this, &SearchWidget::performSearch);
    connect(m_clearButton, &QPushButton::clicked, this, &SearchWidget::clearSearch);
    connect(m_findPrevButton, &QPushButton::clicked, this, &SearchWidget::findPrevious);
    connect(m_findNextButton, &QPushButton::clicked, this, &SearchWidget::findNext);
    
    // Options signals
    connect(m_caseSensitiveCheckBox, &QCheckBox::toggled, this, &SearchWidget::onSearchOptionsChanged);
    connect(m_wholeWordsCheckBox, &QCheckBox::toggled, this, &SearchWidget::onSearchOptionsChanged);
    connect(m_highlightAllCheckBox, &QCheckBox::toggled, this, &SearchWidget::onSearchOptionsChanged);
    
    // History signals
    connect(m_historyMenu, &QMenu::triggered, this, &SearchWidget::onHistoryMenuTriggered);
    connect(m_completer, QOverload<const QString&>::of(&QCompleter::activated),
            this, &SearchWidget::onCompleterActivated);
}

QString SearchWidget::getSearchTerm() const
{
    return m_searchEdit->text().trimmed();
}

void SearchWidget::setSearchTerm(const QString& term)
{
    m_searchEdit->setText(term);
}

bool SearchWidget::isCaseSensitive() const
{
    return m_caseSensitiveCheckBox->isChecked();
}

void SearchWidget::setCaseSensitive(bool enabled)
{
    m_caseSensitiveCheckBox->setChecked(enabled);
}

bool SearchWidget::isWholeWords() const
{
    return m_wholeWordsCheckBox->isChecked();
}

void SearchWidget::setWholeWords(bool enabled)
{
    m_wholeWordsCheckBox->setChecked(enabled);
}

bool SearchWidget::isHighlightAll() const
{
    return m_highlightAllCheckBox->isChecked();
}

void SearchWidget::setHighlightAll(bool enabled)
{
    m_highlightAllCheckBox->setChecked(enabled);
}

void SearchWidget::setSearchHistory(SearchHistory* history)
{
    m_searchHistory = history;
    if (m_searchHistory) {
        connect(m_searchHistory, &SearchHistory::historyChanged, 
                this, &SearchWidget::updateHistoryCompleter);
        updateHistoryCompleter();
    }
}

SearchHistory* SearchWidget::getSearchHistory() const
{
    return m_searchHistory;
}

void SearchWidget::setSearchInProgress(bool inProgress)
{
    m_searchInProgress = inProgress;
    m_progressBar->setVisible(inProgress);
    m_searchButton->setEnabled(!inProgress && !getSearchTerm().isEmpty());
    
    if (inProgress) {
        m_searchEdit->setStyleSheet(m_searchingStyleSheet);
    } else {
        m_searchEdit->setStyleSheet(m_normalStyleSheet);
    }
}

bool SearchWidget::isSearchInProgress() const
{
    return m_searchInProgress;
}

void SearchWidget::setSearchResults(int currentIndex, int totalResults)
{
    m_currentResultIndex = currentIndex;
    m_totalResults = totalResults;
    updateNavigationButtons();
    
    if (totalResults > 0) {
        m_resultsLabel->setText(tr("%1 of %2").arg(currentIndex + 1).arg(totalResults));
        m_searchEdit->setStyleSheet(m_foundStyleSheet);
    } else if (!getSearchTerm().isEmpty()) {
        m_resultsLabel->setText(tr("No results"));
        m_searchEdit->setStyleSheet(m_notFoundStyleSheet);
    } else {
        m_resultsLabel->clear();
        m_searchEdit->setStyleSheet(m_normalStyleSheet);
    }
}

void SearchWidget::clearSearchResults()
{
    m_currentResultIndex = -1;
    m_totalResults = 0;
    m_resultsLabel->clear();
    updateNavigationButtons();
    m_searchEdit->setStyleSheet(m_normalStyleSheet);
}

void SearchWidget::focusSearchField()
{
    m_searchEdit->setFocus();
    m_searchEdit->selectAll();
}

void SearchWidget::setAutoSearchEnabled(bool enabled)
{
    m_autoSearchEnabled = enabled;
}

bool SearchWidget::isAutoSearchEnabled() const
{
    return m_autoSearchEnabled;
}

void SearchWidget::setAutoSearchDelay(int milliseconds)
{
    m_autoSearchDelay = qMax(100, milliseconds);
}

int SearchWidget::getAutoSearchDelay() const
{
    return m_autoSearchDelay;
}

void SearchWidget::performSearch()
{
    QString term = getSearchTerm();
    if (term.isEmpty()) {
        clearSearch();
        return;
    }
    
    // Add to history if we have one
    if (m_searchHistory) {
        m_searchHistory->addSearch(term, isCaseSensitive(), isWholeWords());
    }
    
    emit searchRequested(term, isCaseSensitive(), isWholeWords());
}

void SearchWidget::clearSearch()
{
    m_searchEdit->clear();
    clearSearchResults();
    emit searchCleared();
}

void SearchWidget::onSearchTextChanged()
{
    QString term = getSearchTerm();
    bool hasText = !term.isEmpty();
    
    m_searchButton->setEnabled(hasText && !m_searchInProgress);
    m_clearButton->setEnabled(hasText);
    
    if (!hasText) {
        clearSearchResults();
        emit searchCleared();
    }
}

void SearchWidget::onSearchTextEdited()
{
    if (m_autoSearchEnabled) {
        m_autoSearchTimer->stop();
        m_autoSearchTimer->start(m_autoSearchDelay);
    }
}

void SearchWidget::onSearchOptionsChanged()
{
    emit searchOptionsChanged();
    
    // Auto-search if we have text and auto-search is enabled
    if (m_autoSearchEnabled && !getSearchTerm().isEmpty()) {
        m_autoSearchTimer->stop();
        m_autoSearchTimer->start(m_autoSearchDelay);
    }
}

void SearchWidget::onAutoSearchTimeout()
{
    QString currentTerm = getSearchTerm();
    if (!currentTerm.isEmpty() && currentTerm != m_lastSearchTerm) {
        m_lastSearchTerm = currentTerm;
        performSearch();
    }
}

void SearchWidget::updateNavigationButtons()
{
    bool hasResults = m_totalResults > 0;
    m_findPrevButton->setEnabled(hasResults);
    m_findNextButton->setEnabled(hasResults);
}

void SearchWidget::updateHistoryCompleter()
{
    if (!m_searchHistory) {
        return;
    }

    QStringList recentTerms = m_searchHistory->getRecentSearchTerms(20);
    m_completerModel->setStringList(recentTerms);
}

void SearchWidget::createHistoryMenu()
{
    m_historyMenu->clear();

    if (!m_searchHistory) {
        m_historyMenu->addAction(tr("No history available"))->setEnabled(false);
        return;
    }

    QList<SearchHistoryEntry> recentHistory = m_searchHistory->getRecentHistory(10);

    if (recentHistory.isEmpty()) {
        m_historyMenu->addAction(tr("No search history"))->setEnabled(false);
        return;
    }

    // Add recent searches
    for (const auto& entry : recentHistory) {
        QString text = formatHistoryEntry(entry);
        QAction* action = m_historyMenu->addAction(text);
        action->setData(QVariant::fromValue(entry));
    }

    m_historyMenu->addSeparator();

    // Add clear history action
    QAction* clearAction = m_historyMenu->addAction(tr("Clear History"));
    connect(clearAction, &QAction::triggered, [this]() {
        if (m_searchHistory) {
            m_searchHistory->clearHistory();
        }
    });
}

QString SearchWidget::formatHistoryEntry(const SearchHistoryEntry& entry) const
{
    QString text = entry.searchTerm;

    QStringList options;
    if (entry.caseSensitive) {
        options << tr("Case");
    }
    if (entry.wholeWords) {
        options << tr("Whole");
    }

    if (!options.isEmpty()) {
        text += QString(" [%1]").arg(options.join(", "));
    }

    if (entry.resultCount > 0) {
        text += QString(" (%1 results)").arg(entry.resultCount);
    }

    return text;
}

void SearchWidget::onHistoryMenuTriggered(QAction* action)
{
    if (!action->data().isValid()) {
        return;
    }

    SearchHistoryEntry entry = action->data().value<SearchHistoryEntry>();
    selectHistoryItem(entry);
}

void SearchWidget::selectHistoryItem(const SearchHistoryEntry& entry)
{
    setSearchTerm(entry.searchTerm);
    setCaseSensitive(entry.caseSensitive);
    setWholeWords(entry.wholeWords);

    emit historyItemSelected(entry.searchTerm, entry.caseSensitive, entry.wholeWords);

    // Perform search immediately
    performSearch();
}

void SearchWidget::onCompleterActivated(const QString& text)
{
    setSearchTerm(text);
    performSearch();
}

void SearchWidget::showSearchHistory()
{
    createHistoryMenu();
    m_historyButton->showMenu();
}

void SearchWidget::applySearchStyling()
{
    // Normal state
    m_normalStyleSheet =
        "QLineEdit { "
        "    border: 1px solid #ced4da; "
        "    border-radius: 4px; "
        "    padding: 4px 8px; "
        "    background: white; "
        "}";

    // Searching state
    m_searchingStyleSheet =
        "QLineEdit { "
        "    border: 2px solid #007bff; "
        "    border-radius: 4px; "
        "    padding: 4px 8px; "
        "    background: #f8f9ff; "
        "}";

    // Found results state
    m_foundStyleSheet =
        "QLineEdit { "
        "    border: 2px solid #28a745; "
        "    border-radius: 4px; "
        "    padding: 4px 8px; "
        "    background: #f8fff8; "
        "}";

    // No results state
    m_notFoundStyleSheet =
        "QLineEdit { "
        "    border: 2px solid #dc3545; "
        "    border-radius: 4px; "
        "    padding: 4px 8px; "
        "    background: #fff8f8; "
        "}";

    m_searchEdit->setStyleSheet(m_normalStyleSheet);
}

void SearchWidget::updateCompleter()
{
    if (m_completer && m_searchHistory) {
        QStringList history = m_searchHistory->getRecentSearchTerms();
        QStringListModel* model = qobject_cast<QStringListModel*>(m_completer->model());
        if (model) {
            model->setStringList(history);
        }
    }
}

void SearchWidget::setVisible(bool visible)
{
    QWidget::setVisible(visible);
    if (visible) {
        focusSearchField();
    }
}
