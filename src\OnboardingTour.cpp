#include "OnboardingTour.h"
#include <QPainter>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QApplication>
#include <QScreen>
#include <QGraphicsDropShadowEffect>
#include <QPropertyAnimation>
#include <QParallelAnimationGroup>

OnboardingTour::OnboardingTour(QWidget *parent)
    : QWidget(parent)
    , m_tooltipWidget(nullptr)
    , m_tooltipLayout(nullptr)
    , m_title<PERSON>abel(nullptr)
    , m_description<PERSON>abel(nullptr)
    , m_stepCounterLabel(nullptr)
    , m_buttonLayout(nullptr)
    , m_previousButton(nullptr)
    , m_nextButton(nullptr)
    , m_skipButton(nullptr)
    , m_overlayWidget(nullptr)
    , m_highlightWidget(nullptr)
    , m_fadeAnimation(nullptr)
    , m_moveAnimation(nullptr)
    , m_currentStep(-1)
    , m_welcomeTitle("Welcome!")
    , m_welcomeMessage("Let's take a quick tour of the application.")
    , m_completionTitle("Tour Complete!")
    , m_completionMessage("You're all set! Enjoy using the application.")
    , m_themeMode(ElaThemeType::Light)
    , m_isActive(false)
{
    setupUI();
    updateTheme();
    
    // Hide initially
    hide();
}

OnboardingTour::~OnboardingTour()
{
    if (m_overlayWidget) {
        m_overlayWidget->deleteLater();
    }
    if (m_highlightWidget) {
        m_highlightWidget->deleteLater();
    }
}

void OnboardingTour::addStep(const TourStep& step)
{
    m_steps.append(step);
}

void OnboardingTour::startTour()
{
    if (m_steps.isEmpty()) {
        return;
    }
    
    m_isActive = true;
    m_currentStep = 0;
    
    // Create overlay
    if (!m_overlayWidget) {
        m_overlayWidget = new QWidget(parentWidget());
        m_overlayWidget->setStyleSheet("background-color: rgba(0, 0, 0, 0.5);");
        m_overlayWidget->installEventFilter(this);
    }
    
    // Show overlay
    if (parentWidget()) {
        m_overlayWidget->resize(parentWidget()->size());
        m_overlayWidget->show();
        m_overlayWidget->raise();
    }
    
    // Show tour widget
    show();
    raise();
    
    showCurrentStep();
    emit tourStarted();
}

void OnboardingTour::stopTour()
{
    if (!m_isActive) {
        return;
    }
    
    m_isActive = false;
    hideCurrentStep();
    
    if (m_overlayWidget) {
        m_overlayWidget->hide();
    }
    
    hide();
    emit tourCompleted();
}

void OnboardingTour::nextStep()
{
    if (!m_isActive || m_currentStep >= m_steps.size() - 1) {
        stopTour();
        return;
    }
    
    hideCurrentStep();
    m_currentStep++;
    showCurrentStep();
}

void OnboardingTour::previousStep()
{
    if (!m_isActive || m_currentStep <= 0) {
        return;
    }
    
    hideCurrentStep();
    m_currentStep--;
    showCurrentStep();
}

void OnboardingTour::skipTour()
{
    if (!m_isActive) {
        return;
    }
    
    m_isActive = false;
    hideCurrentStep();
    
    if (m_overlayWidget) {
        m_overlayWidget->hide();
    }
    
    hide();
    emit tourSkipped();
}

void OnboardingTour::setWelcomeMessage(const QString& title, const QString& message)
{
    m_welcomeTitle = title;
    m_welcomeMessage = message;
}

void OnboardingTour::setCompletionMessage(const QString& title, const QString& message)
{
    m_completionTitle = title;
    m_completionMessage = message;
}

void OnboardingTour::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Draw tooltip background
    QColor bgColor = m_themeMode == ElaThemeType::Light ? 
                     QColor(255, 255, 255) : QColor(45, 45, 45);
    
    painter.setBrush(bgColor);
    painter.setPen(QPen(QColor(200, 200, 200), 1));
    painter.drawRoundedRect(rect().adjusted(1, 1, -1, -1), TOOLTIP_RADIUS, TOOLTIP_RADIUS);
}

void OnboardingTour::showEvent(QShowEvent *event)
{
    QWidget::showEvent(event);
    
    if (m_fadeAnimation) {
        m_fadeAnimation->setStartValue(0.0);
        m_fadeAnimation->setEndValue(1.0);
        m_fadeAnimation->start();
    }
}

void OnboardingTour::hideEvent(QHideEvent *event)
{
    QWidget::hideEvent(event);
}

bool OnboardingTour::eventFilter(QObject *obj, QEvent *event)
{
    // Handle overlay events
    if (obj == m_overlayWidget && event->type() == QEvent::Resize) {
        if (parentWidget()) {
            m_overlayWidget->resize(parentWidget()->size());
        }
    }
    
    return QWidget::eventFilter(obj, event);
}

void OnboardingTour::onNextClicked()
{
    nextStep();
}

void OnboardingTour::onPreviousClicked()
{
    previousStep();
}

void OnboardingTour::onSkipClicked()
{
    skipTour();
}

void OnboardingTour::animateToStep()
{
    if (!m_isActive || m_currentStep < 0 || m_currentStep >= m_steps.size()) {
        return;
    }
    
    positionTooltip();
    
    // Animate tooltip appearance
    if (m_fadeAnimation) {
        m_fadeAnimation->setStartValue(0.0);
        m_fadeAnimation->setEndValue(1.0);
        m_fadeAnimation->start();
    }
}

void OnboardingTour::setupUI()
{
    setFixedSize(320, 200);
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint);
    setAttribute(Qt::WA_TranslucentBackground);
    
    // Create main layout
    m_tooltipLayout = new QVBoxLayout(this);
    m_tooltipLayout->setContentsMargins(TOOLTIP_PADDING, TOOLTIP_PADDING, 
                                       TOOLTIP_PADDING, TOOLTIP_PADDING);
    m_tooltipLayout->setSpacing(12);
    
    // Title label
    m_titleLabel = new ElaText(this);
    m_titleLabel->setTextStyle(ElaTextType::Title);
    m_tooltipLayout->addWidget(m_titleLabel);
    
    // Description label
    m_descriptionLabel = new ElaText(this);
    m_descriptionLabel->setTextStyle(ElaTextType::Body);
    m_descriptionLabel->setWordWrap(true);
    m_tooltipLayout->addWidget(m_descriptionLabel);
    
    // Step counter
    m_stepCounterLabel = new ElaText(this);
    m_stepCounterLabel->setTextStyle(ElaTextType::Caption);
    m_tooltipLayout->addWidget(m_stepCounterLabel);
    
    // Button layout
    m_buttonLayout = new QHBoxLayout();
    m_buttonLayout->setSpacing(8);
    
    // Skip button
    m_skipButton = new ElaPushButton("Skip Tour", this);
    connect(m_skipButton, &ElaPushButton::clicked, this, &OnboardingTour::onSkipClicked);
    m_buttonLayout->addWidget(m_skipButton);
    
    m_buttonLayout->addStretch();
    
    // Previous button
    m_previousButton = new ElaPushButton("Previous", this);
    connect(m_previousButton, &ElaPushButton::clicked, this, &OnboardingTour::onPreviousClicked);
    m_buttonLayout->addWidget(m_previousButton);
    
    // Next button
    m_nextButton = new ElaPushButton("Next", this);
    connect(m_nextButton, &ElaPushButton::clicked, this, &OnboardingTour::onNextClicked);
    m_buttonLayout->addWidget(m_nextButton);
    
    m_tooltipLayout->addLayout(m_buttonLayout);
    
    // Setup animations
    m_fadeAnimation = new QPropertyAnimation(this, "windowOpacity", this);
    m_fadeAnimation->setDuration(ANIMATION_DURATION);
    m_fadeAnimation->setEasingCurve(QEasingCurve::OutCubic);
    
    m_moveAnimation = new QPropertyAnimation(this, "pos", this);
    m_moveAnimation->setDuration(ANIMATION_DURATION);
    m_moveAnimation->setEasingCurve(QEasingCurve::OutCubic);
    
    // Add drop shadow
    auto* shadow = new QGraphicsDropShadowEffect(this);
    shadow->setBlurRadius(20);
    shadow->setColor(QColor(0, 0, 0, 80));
    shadow->setOffset(0, 4);
    setGraphicsEffect(shadow);
}

void OnboardingTour::showCurrentStep()
{
    if (!m_isActive || m_currentStep < 0 || m_currentStep >= m_steps.size()) {
        return;
    }
    
    updateStepContent();
    positionTooltip();
    highlightTarget();
    
    emit stepChanged(m_currentStep + 1, m_steps.size());
}

void OnboardingTour::hideCurrentStep()
{
    removeHighlight();
}

void OnboardingTour::updateStepContent()
{
    if (m_currentStep < 0 || m_currentStep >= m_steps.size()) {
        return;
    }
    
    const TourStep& step = m_steps[m_currentStep];
    
    m_titleLabel->setText(step.title);
    m_descriptionLabel->setText(step.description);
    m_stepCounterLabel->setText(QString("Step %1 of %2").arg(m_currentStep + 1).arg(m_steps.size()));
    
    // Update button states
    m_previousButton->setEnabled(m_currentStep > 0);
    m_nextButton->setText(m_currentStep == m_steps.size() - 1 ? "Finish" : "Next");
}

void OnboardingTour::positionTooltip()
{
    if (!parentWidget() || m_currentStep < 0 || m_currentStep >= m_steps.size()) {
        return;
    }
    
    const TourStep& step = m_steps[m_currentStep];
    QWidget* target = step.targetWidget;
    
    if (!target) {
        // Center on parent
        QPoint center = parentWidget()->rect().center();
        move(center - rect().center());
        return;
    }
    
    // Position relative to target
    QRect targetRect = target->geometry();
    QPoint targetCenter = target->mapToGlobal(targetRect.center());
    QPoint parentTopLeft = parentWidget()->mapToGlobal(QPoint(0, 0));
    QPoint relativeCenter = targetCenter - parentTopLeft;
    
    QPoint tooltipPos;
    
    switch (step.position) {
    case TourPosition::Top:
        tooltipPos = QPoint(relativeCenter.x() - width() / 2, 
                           relativeCenter.y() - targetRect.height() / 2 - height() - 20);
        break;
    case TourPosition::Bottom:
        tooltipPos = QPoint(relativeCenter.x() - width() / 2, 
                           relativeCenter.y() + targetRect.height() / 2 + 20);
        break;
    case TourPosition::Left:
        tooltipPos = QPoint(relativeCenter.x() - targetRect.width() / 2 - width() - 20, 
                           relativeCenter.y() - height() / 2);
        break;
    case TourPosition::Right:
        tooltipPos = QPoint(relativeCenter.x() + targetRect.width() / 2 + 20, 
                           relativeCenter.y() - height() / 2);
        break;
    case TourPosition::Center:
    default:
        tooltipPos = QPoint(relativeCenter.x() - width() / 2, 
                           relativeCenter.y() - height() / 2);
        break;
    }
    
    // Ensure tooltip stays within parent bounds
    if (parentWidget()) {
        QRect parentRect = parentWidget()->rect();
        tooltipPos.setX(qBound(10, tooltipPos.x(), parentRect.width() - width() - 10));
        tooltipPos.setY(qBound(10, tooltipPos.y(), parentRect.height() - height() - 10));
    }
    
    move(tooltipPos);
}

void OnboardingTour::highlightTarget()
{
    if (m_currentStep < 0 || m_currentStep >= m_steps.size()) {
        return;
    }
    
    const TourStep& step = m_steps[m_currentStep];
    QWidget* target = step.targetWidget;
    
    if (!target || !m_overlayWidget) {
        return;
    }
    
    // Create highlight widget if needed
    if (!m_highlightWidget) {
        m_highlightWidget = new QWidget(m_overlayWidget);
        m_highlightWidget->setStyleSheet("border: 2px solid #0078d4; background-color: transparent;");
    }
    
    // Position highlight over target
    QRect targetRect = target->geometry();
    QPoint targetPos = target->mapTo(m_overlayWidget, QPoint(0, 0));
    
    m_highlightWidget->setGeometry(targetPos.x() - HIGHLIGHT_MARGIN,
                                  targetPos.y() - HIGHLIGHT_MARGIN,
                                  targetRect.width() + 2 * HIGHLIGHT_MARGIN,
                                  targetRect.height() + 2 * HIGHLIGHT_MARGIN);
    m_highlightWidget->show();
}

void OnboardingTour::removeHighlight()
{
    if (m_highlightWidget) {
        m_highlightWidget->hide();
    }
}

void OnboardingTour::updateTheme()
{
    // Update theme based on current theme mode
    // This would typically be connected to the application's theme system
    update();
}
