name: Documentation Build and Deploy

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'docs/**'
      - 'include/**'
      - 'src/**'
      - 'README.md'
      - 'scripts/generate_docs.py'
      - 'scripts/validate_docs.py'
      - '.github/workflows/documentation.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'docs/**'
      - 'include/**'
      - 'src/**'
      - 'README.md'
      - 'scripts/generate_docs.py'
      - 'scripts/validate_docs.py'
  workflow_dispatch:
    inputs:
      deploy:
        description: 'Deploy to GitHub Pages'
        required: false
        default: false
        type: boolean

env:
  BUILD_TYPE: Release
  DOCS_OUTPUT_DIR: build/docs

jobs:
  build-docs:
    name: Build Documentation
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Full history for git-based features
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
        cache: 'pip'
    
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt || echo "No requirements.txt found"
    
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          doxygen \
          graphviz \
          optipng \
          jpegoptim \
          nodejs \
          npm
    
    - name: Install documentation tools
      run: |
        # Install additional tools for documentation processing
        npm install -g svgo
        pip install beautifulsoup4 lxml requests
    
    - name: Cache Doxygen
      uses: actions/cache@v3
      with:
        path: ~/.cache/doxygen
        key: ${{ runner.os }}-doxygen-${{ hashFiles('Doxyfile.in') }}
        restore-keys: |
          ${{ runner.os }}-doxygen-
    
    - name: Configure CMake
      run: |
        cmake -B build \
          -DCMAKE_BUILD_TYPE=$BUILD_TYPE \
          -DBUILD_DOCUMENTATION=ON \
          -DBUILD_API_DOCS=ON \
          -DBUILD_USER_DOCS=ON
    
    - name: Generate documentation
      run: |
        echo "🔨 Building documentation..."
        python scripts/generate_docs.py \
          --project-root . \
          --clean
    
    - name: Optimize documentation
      run: |
        echo "🖼️ Optimizing images..."
        python scripts/maintain_docs.py \
          --docs-dir $DOCS_OUTPUT_DIR \
          --action optimize
    
    - name: Validate documentation
      run: |
        echo "✅ Validating documentation..."
        python scripts/validate_docs.py \
          --docs-dir $DOCS_OUTPUT_DIR \
          --output validation-report.json \
          --fail-on-error
    
    - name: Upload validation report
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: validation-report
        path: validation-report.json
        retention-days: 30
    
    - name: Generate documentation metrics
      run: |
        echo "📊 Collecting metrics..."
        python scripts/maintain_docs.py \
          --docs-dir $DOCS_OUTPUT_DIR \
          --action metrics > docs-metrics.txt
    
    - name: Upload documentation metrics
      uses: actions/upload-artifact@v3
      with:
        name: documentation-metrics
        path: docs-metrics.txt
        retention-days: 30
    
    - name: Upload documentation artifacts
      uses: actions/upload-artifact@v3
      with:
        name: documentation
        path: ${{ env.DOCS_OUTPUT_DIR }}
        retention-days: 30
    
    - name: Create documentation package
      if: github.event_name == 'push' && github.ref == 'refs/heads/main'
      run: |
        cd build
        tar -czf pdf-viewer-docs-${{ github.sha }}.tar.gz docs/
        echo "📦 Documentation package created"
    
    - name: Upload documentation package
      if: github.event_name == 'push' && github.ref == 'refs/heads/main'
      uses: actions/upload-artifact@v3
      with:
        name: documentation-package
        path: build/pdf-viewer-docs-${{ github.sha }}.tar.gz
        retention-days: 90

  deploy-docs:
    name: Deploy Documentation
    needs: build-docs
    runs-on: ubuntu-latest
    if: |
      (github.event_name == 'push' && github.ref == 'refs/heads/main') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.deploy == 'true')
    
    permissions:
      contents: read
      pages: write
      id-token: write
    
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    
    steps:
    - name: Download documentation artifacts
      uses: actions/download-artifact@v3
      with:
        name: documentation
        path: docs
    
    - name: Setup Pages
      uses: actions/configure-pages@v3
    
    - name: Upload to GitHub Pages
      uses: actions/upload-pages-artifact@v2
      with:
        path: docs
    
    - name: Deploy to GitHub Pages
      id: deployment
      uses: actions/deploy-pages@v2

  check-links:
    name: Check External Links
    needs: build-docs
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Download documentation artifacts
      uses: actions/download-artifact@v3
      with:
        name: documentation
        path: docs
    
    - name: Install link checker
      run: |
        npm install -g markdown-link-check
        pip install requests beautifulsoup4
    
    - name: Check external links
      run: |
        echo "🔗 Checking external links..."
        find docs -name "*.html" -exec python -c "
import sys
import requests
from bs4 import BeautifulSoup
import re

def check_external_links(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        soup = BeautifulSoup(f.read(), 'html.parser')
    
    external_links = []
    for link in soup.find_all('a', href=True):
        href = link['href']
        if href.startswith(('http://', 'https://')):
            external_links.append(href)
    
    broken_links = []
    for url in set(external_links):
        try:
            response = requests.head(url, timeout=10, allow_redirects=True)
            if response.status_code >= 400:
                broken_links.append((url, response.status_code))
        except Exception as e:
            broken_links.append((url, str(e)))
    
    if broken_links:
        print(f'Broken links in {file_path}:')
        for url, error in broken_links:
            print(f'  - {url}: {error}')
        return False
    return True

if not check_external_links(sys.argv[1]):
    sys.exit(1)
        " {} \;

  accessibility-check:
    name: Accessibility Check
    needs: build-docs
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Download documentation artifacts
      uses: actions/download-artifact@v3
      with:
        name: documentation
        path: docs
    
    - name: Install accessibility checker
      run: |
        npm install -g @axe-core/cli
    
    - name: Run accessibility tests
      run: |
        echo "♿ Running accessibility tests..."
        find docs -name "*.html" -exec axe {} \; || true

  performance-check:
    name: Performance Check
    needs: build-docs
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Download documentation artifacts
      uses: actions/download-artifact@v3
      with:
        name: documentation
        path: docs
    
    - name: Install Lighthouse CI
      run: npm install -g @lhci/cli
    
    - name: Run Lighthouse CI
      run: |
        echo "⚡ Running performance tests..."
        # Create a simple server to serve the docs
        python -m http.server 8080 --directory docs &
        SERVER_PID=$!
        sleep 5
        
        # Run Lighthouse on key pages
        lhci autorun --upload.target=temporary-public-storage || true
        
        # Cleanup
        kill $SERVER_PID

  comment-pr:
    name: Comment on PR
    needs: [build-docs, check-links, accessibility-check, performance-check]
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request' && always()
    
    steps:
    - name: Download validation report
      uses: actions/download-artifact@v3
      with:
        name: validation-report
        path: .
    
    - name: Download documentation metrics
      uses: actions/download-artifact@v3
      with:
        name: documentation-metrics
        path: .
    
    - name: Create PR comment
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          
          let comment = '## 📚 Documentation Build Report\n\n';
          
          // Add build status
          const buildSuccess = '${{ needs.build-docs.result }}' === 'success';
          comment += `### Build Status: ${buildSuccess ? '✅ Success' : '❌ Failed'}\n\n`;
          
          // Add validation results if available
          try {
            const validation = JSON.parse(fs.readFileSync('validation-report.json', 'utf8'));
            comment += '### Validation Results\n';
            comment += `- Total issues: ${validation.summary.total_issues}\n`;
            comment += `- Errors: ${validation.summary.errors}\n`;
            comment += `- Warnings: ${validation.summary.warnings}\n\n`;
          } catch (e) {
            comment += '### Validation Results\n❌ Validation report not available\n\n';
          }
          
          // Add metrics if available
          try {
            const metrics = fs.readFileSync('docs-metrics.txt', 'utf8');
            comment += '### Documentation Metrics\n```\n' + metrics + '\n```\n\n';
          } catch (e) {
            comment += '### Documentation Metrics\n❌ Metrics not available\n\n';
          }
          
          // Add check results
          comment += '### Additional Checks\n';
          comment += `- Link Check: ${{ needs.check-links.result === 'success' && '✅' || '❌' }}\n`;
          comment += `- Accessibility: ${{ needs.accessibility-check.result === 'success' && '✅' || '❌' }}\n`;
          comment += `- Performance: ${{ needs.performance-check.result === 'success' && '✅' || '❌' }}\n\n`;
          
          comment += '---\n*This comment was automatically generated by the documentation workflow.*';
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });
