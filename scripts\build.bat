@echo off
REM Build script for Optimized PDF Viewer on Windows
REM Usage: scripts\build.bat [Debug|Release] [clean]

setlocal enabledelayedexpansion

REM Default values
set BUILD_TYPE=%1
if "%BUILD_TYPE%"=="" set BUILD_TYPE=Release

set CLEAN=%2
set BUILD_DIR=build
set SOURCE_DIR=%~dp0..

echo Building Optimized PDF Viewer...
echo Source directory: %SOURCE_DIR%
echo Build type: %BUILD_TYPE%

REM Clean build directory if requested
if "%CLEAN%"=="clean" (
    echo Cleaning build directory...
    if exist "%BUILD_DIR%" rmdir /s /q "%BUILD_DIR%"
)

REM Create build directory
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"
cd "%BUILD_DIR%"

REM Configure with CMake
echo Configuring with CMake...
cmake .. ^
    -DCMAKE_BUILD_TYPE=%BUILD_TYPE% ^
    -DCMAKE_EXPORT_COMPILE_COMMANDS=ON ^
    -DBUILD_TESTS=ON

if errorlevel 1 (
    echo CMake configuration failed!
    exit /b 1
)

REM Build
echo Building...
cmake --build . --config %BUILD_TYPE% --parallel

if errorlevel 1 (
    echo Build failed!
    exit /b 1
)

echo Build completed successfully!
echo Executable: %BUILD_DIR%\%BUILD_TYPE%\optimized-pdf-viewer.exe

REM Run tests if available
if exist "tests\%BUILD_TYPE%\test_annotation.exe" (
    echo Running tests...
    ctest --output-on-failure -C %BUILD_TYPE%
)

echo Done!
pause
