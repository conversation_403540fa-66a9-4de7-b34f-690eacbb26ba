#include "DocumentationViewer.h"
#include "DocumentationSyntaxHighlighter.h"
#include "Logger.h"

#include <QApplication>
#include <QDesktopServices>
#include <QFileInfo>
#include <QDir>
#include <QTextStream>
#include <QMessageBox>
#include <QPrintDialog>
#include <QPrintPreviewDialog>
#include <QPrinter>
#include <QTextDocumentWriter>
#include <QStandardPaths>
#include <QSplitterHandle>
#include <QHeaderView>
#include <QScrollBar>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QListWidget>
#include <QListWidgetItem>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QSplitter>
#include <QLabel>
#include <QToolBar>
#include <QCheckBox>
#include <QShortcut>
#include <QKeySequence>
#include <QMenu>
#include <QInputDialog>
#include <QRegularExpression>
#include <QRegularExpressionMatch>
#include <QAction>

#include "ElaIntegration.h"
#include "ElaToolButton.h"
#include "ElaToolBar.h"
#include "ElaComboBox.h"
#include "ElaLineEdit.h"

// Define static constants
const int DocumentationViewer::MIN_ZOOM;
const int DocumentationViewer::MAX_ZOOM;
const int DocumentationViewer::DEFAULT_ZOOM;
const int DocumentationViewer::ZOOM_STEP;

DocumentationViewer::DocumentationViewer(QWidget* parent)
    : QWidget(parent)
    , m_mainSplitter(nullptr)
    , m_leftSplitter(nullptr)
    , m_leftPanel(nullptr)
    , m_toolbar(nullptr)
    , m_backButton(nullptr)
    , m_forwardButton(nullptr)
    , m_homeButton(nullptr)
    , m_printButton(nullptr)
    , m_searchPanel(nullptr)
    , m_searchEdit(nullptr)
    , m_searchButton(nullptr)
    , m_clearSearchButton(nullptr)
    , m_findNextButton(nullptr)
    , m_findPrevButton(nullptr)
    , m_searchResultsLabel(nullptr)
    , m_tocTree(nullptr)
    , m_bookmarksList(nullptr)
    , m_addBookmarkButton(nullptr)
    , m_removeBookmarkButton(nullptr)
    , m_contentBrowser(nullptr)
    , m_zoomSlider(nullptr)
    , m_zoomCombo(nullptr)
    , m_zoomLabel(nullptr)
    , m_statusBar(nullptr)
    , m_statusLabel(nullptr)
    , m_loadingProgress(nullptr)
    , m_historyIndex(-1)
    , m_currentSearchResult(-1)
    , m_zoomLevel(DEFAULT_ZOOM)
    , m_settings(nullptr)
    , m_syntaxHighlighter(nullptr)
    , m_caseSensitive(false)
    , m_wholeWords(false)
{
    Logger* logger = Logger::instance();
    logger->info("Initializing DocumentationViewer", "DocumentationViewer");

    try {
        // Initialize settings
        m_settings = new QSettings("PDFViewer", "Documentation", this);
        
        // Setup UI
        setupUi();
        
        // Load saved bookmarks
        loadBookmarks();
        
        // Set initial zoom level
        setZoomLevel(m_settings->value("documentation/zoomLevel", DEFAULT_ZOOM).toInt());

        // Setup keyboard shortcuts
        setupKeyboardShortcuts();

        logger->info("DocumentationViewer initialized successfully", "DocumentationViewer");
    }
    catch (const std::exception& e) {
        logger->error(QString("Failed to initialize DocumentationViewer: %1").arg(e.what()), "DocumentationViewer");
        QMessageBox::critical(this, tr("Error"), tr("Failed to initialize documentation viewer: %1").arg(e.what()));
    }
}

DocumentationViewer::~DocumentationViewer()
{
    auto logger = Logger::instance();
    logger->info("Destroying DocumentationViewer", "DocumentationViewer");
    
    try {
        // Save current state
        if (m_settings) {
            m_settings->setValue("documentation/zoomLevel", m_zoomLevel);
            saveBookmarks();
        }
        
        // Cleanup syntax highlighter
        if (m_syntaxHighlighter) {
            delete m_syntaxHighlighter;
            m_syntaxHighlighter = nullptr;
        }
        
        logger->info("DocumentationViewer destroyed successfully", "DocumentationViewer");
    }
    catch (const std::exception& e) {
        logger->error(QString("Error during DocumentationViewer destruction: %1").arg(e.what()), "DocumentationViewer");
    }
}

void DocumentationViewer::setupUi()
{
    setWindowTitle(tr("Documentation Viewer"));
    setMinimumSize(800, 600);
    
    // Main layout
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(0);
    
    // Setup toolbar
    setupToolbar();
    mainLayout->addWidget(m_toolbar);
    
    // Setup search panel
    setupSearchPanel();
    mainLayout->addWidget(m_searchPanel);
    m_searchPanel->hide(); // Initially hidden
    
    // Main splitter (horizontal)
    m_mainSplitter = new QSplitter(Qt::Horizontal, this);
    m_mainSplitter->setChildrenCollapsible(false);
    
    // Left panel with vertical splitter
    m_leftSplitter = new QSplitter(Qt::Vertical, this);
    m_leftSplitter->setChildrenCollapsible(false);
    
    // Setup table of contents
    setupTableOfContents();
    m_leftSplitter->addWidget(m_tocTree);
    
    // Setup bookmarks panel
    setupBookmarksPanel();
    m_leftSplitter->addWidget(m_bookmarksList);
    
    // Set left splitter sizes (TOC gets more space)
    m_leftSplitter->setSizes({300, 200});
    
    m_mainSplitter->addWidget(m_leftSplitter);
    
    // Setup content area
    setupContentArea();
    m_mainSplitter->addWidget(m_contentBrowser);
    
    // Set main splitter sizes (content gets more space)
    m_mainSplitter->setSizes({250, 550});
    
    mainLayout->addWidget(m_mainSplitter);
    
    // Setup status bar
    setupStatusBar();
    mainLayout->addWidget(m_statusBar);
    
    // Apply modern styling
    setStyleSheet(
        "QSplitter::handle { "
        "    background: #e0e0e0; "
        "    border: 1px solid #c0c0c0; "
        "} "
        "QSplitter::handle:hover { "
        "    background: #d0d0d0; "
        "}"
    );
}

void DocumentationViewer::setupToolbar()
{
    m_toolbar = new ElaToolBar(this);
    m_toolbar->setFixedHeight(40);
    
    // Navigation buttons
    m_backButton = new ElaToolButton(this);
    m_backButton->setElaIcon(ElaIconType::ArrowLeft);
    m_backButton->setToolTip(tr("Go Back"));
    m_backButton->setEnabled(false);
    connect(m_backButton, &ElaToolButton::clicked, this, &DocumentationViewer::goBack);
    m_toolbar->addWidget(m_backButton);
    
    m_forwardButton = new ElaToolButton(this);
    m_forwardButton->setElaIcon(ElaIconType::ArrowRight);
    m_forwardButton->setToolTip(tr("Go Forward"));
    m_forwardButton->setEnabled(false);
    connect(m_forwardButton, &ElaToolButton::clicked, this, &DocumentationViewer::goForward);
    m_toolbar->addWidget(m_forwardButton);

    // History dropdown button
    ElaToolButton* historyButton = new ElaToolButton(this);
    historyButton->setElaIcon(ElaIconType::ClockRotateLeft);
    historyButton->setToolTip(tr("History"));
    historyButton->setPopupMode(QToolButton::InstantPopup);
    connect(historyButton, &ElaToolButton::clicked, this, &DocumentationViewer::showHistoryMenu);
    m_toolbar->addWidget(historyButton);

    m_toolbar->addSeparator();
    
    // Home button
    m_homeButton = new ElaToolButton(this);
    m_homeButton->setElaIcon(ElaIconType::House);
    m_homeButton->setToolTip(tr("Home"));
    connect(m_homeButton, &ElaToolButton::clicked, [this]() {
        loadDocumentation("index.html");
    });
    m_toolbar->addWidget(m_homeButton);

    m_toolbar->addSeparator();

    // Search toggle button
    ElaToolButton* searchToggle = new ElaToolButton(this);
    searchToggle->setElaIcon(ElaIconType::MagnifyingGlass);
    searchToggle->setToolTip(tr("Toggle Search"));
    searchToggle->setCheckable(true);
    connect(searchToggle, &ElaToolButton::toggled, [this](bool checked) {
        m_searchPanel->setVisible(checked);
        if (checked) {
            m_searchEdit->setFocus();
        }
    });
    m_toolbar->addWidget(searchToggle);

    m_toolbar->addSeparator();

    // Zoom controls
    m_zoomLabel = new QLabel(tr("Zoom:"), this);
    m_toolbar->addWidget(m_zoomLabel);

    ElaToolButton* zoomOutBtn = new ElaToolButton(this);
    zoomOutBtn->setElaIcon(ElaIconType::MagnifyingGlassMinus);
    zoomOutBtn->setToolTip(tr("Zoom Out"));
    connect(zoomOutBtn, &ElaToolButton::clicked, this, &DocumentationViewer::zoomOut);
    m_toolbar->addWidget(zoomOutBtn);

    m_zoomCombo = new ElaComboBox(this);
    m_zoomCombo->setFixedWidth(100);
    m_zoomCombo->addItems({"50%", "75%", "100%", "125%", "150%", "200%", "300%", "Fit Width"});
    m_zoomCombo->setCurrentText("100%");
    connect(m_zoomCombo, &ElaComboBox::currentTextChanged, this, &DocumentationViewer::onZoomComboChanged);
    m_toolbar->addWidget(m_zoomCombo);

    ElaToolButton* zoomInBtn = new ElaToolButton(this);
    zoomInBtn->setElaIcon(ElaIconType::MagnifyingGlassPlus);
    zoomInBtn->setToolTip(tr("Zoom In"));
    connect(zoomInBtn, &ElaToolButton::clicked, this, &DocumentationViewer::zoomIn);
    m_toolbar->addWidget(zoomInBtn);
    
    m_toolbar->addSeparator();
    
    // Print button
    m_printButton = new ElaToolButton(this);
    m_printButton->setElaIcon(ElaIconType::Print);
    m_printButton->setToolTip(tr("Print"));
    connect(m_printButton, &ElaToolButton::clicked, this, &DocumentationViewer::printPage);
    m_toolbar->addWidget(m_printButton);

    // Print preview button
    ElaToolButton* printPreviewBtn = new ElaToolButton(this);
    printPreviewBtn->setElaIcon(ElaIconType::Eye);
    printPreviewBtn->setToolTip(tr("Print Preview"));
    connect(printPreviewBtn, &ElaToolButton::clicked, this, &DocumentationViewer::showPrintPreview);
    m_toolbar->addWidget(printPreviewBtn);
}

void DocumentationViewer::setupSearchPanel()
{
    m_searchPanel = new QWidget(this);
    m_searchPanel->setFixedHeight(50);
    
    QHBoxLayout* searchLayout = new QHBoxLayout(m_searchPanel);
    searchLayout->setContentsMargins(10, 5, 10, 5);
    
    // Search input
    m_searchEdit = new ElaLineEdit(this);
    m_searchEdit->setPlaceholderText(tr("Search documentation..."));
    connect(m_searchEdit, &ElaLineEdit::textChanged, this, &DocumentationViewer::onSearchTextChanged);
    connect(m_searchEdit, &ElaLineEdit::returnPressed, this, &DocumentationViewer::findNext);
    searchLayout->addWidget(m_searchEdit);
    
    // Search buttons
    m_findPrevButton = new ElaToolButton(this);
    m_findPrevButton->setElaIcon(ElaIconType::ArrowUp);
    m_findPrevButton->setToolTip(tr("Find Previous"));
    m_findPrevButton->setEnabled(false);
    connect(m_findPrevButton, &ElaToolButton::clicked, this, &DocumentationViewer::findPrevious);
    searchLayout->addWidget(m_findPrevButton);
    
    m_findNextButton = new ElaToolButton(this);
    m_findNextButton->setElaIcon(ElaIconType::ArrowDown);
    m_findNextButton->setToolTip(tr("Find Next"));
    m_findNextButton->setEnabled(false);
    connect(m_findNextButton, &ElaToolButton::clicked, this, &DocumentationViewer::findNext);
    searchLayout->addWidget(m_findNextButton);
    
    // Clear search button
    m_clearSearchButton = new ElaToolButton(this);
    m_clearSearchButton->setElaIcon(ElaIconType::Xmark);
    m_clearSearchButton->setToolTip(tr("Clear Search"));
    connect(m_clearSearchButton, &ElaToolButton::clicked, this, &DocumentationViewer::clearSearch);
    searchLayout->addWidget(m_clearSearchButton);
    
    // Search options
    QCheckBox* caseSensitiveCheck = new QCheckBox(tr("Case sensitive"), this);
    caseSensitiveCheck->setChecked(m_caseSensitive);
    connect(caseSensitiveCheck, &QCheckBox::toggled, [this](bool checked) {
        m_caseSensitive = checked;
        if (!m_currentSearchTerm.isEmpty()) {
            searchText(m_currentSearchTerm, m_caseSensitive, m_wholeWords);
        }
    });
    searchLayout->addWidget(caseSensitiveCheck);

    QCheckBox* wholeWordsCheck = new QCheckBox(tr("Whole words"), this);
    wholeWordsCheck->setChecked(m_wholeWords);
    connect(wholeWordsCheck, &QCheckBox::toggled, [this](bool checked) {
        m_wholeWords = checked;
        if (!m_currentSearchTerm.isEmpty()) {
            searchText(m_currentSearchTerm, m_caseSensitive, m_wholeWords);
        }
    });
    searchLayout->addWidget(wholeWordsCheck);

    // Search results label
    m_searchResultsLabel = new QLabel(this);
    m_searchResultsLabel->setMinimumWidth(100);
    searchLayout->addWidget(m_searchResultsLabel);

    searchLayout->addStretch();
}

void DocumentationViewer::setupTableOfContents()
{
    m_tocTree = new QTreeWidget(this);
    m_tocTree->setHeaderLabel(tr("Table of Contents"));
    m_tocTree->setRootIsDecorated(true);
    m_tocTree->setAlternatingRowColors(true);
    m_tocTree->header()->hide();

    connect(m_tocTree, &QTreeWidget::itemClicked, this, &DocumentationViewer::onTocItemClicked);
}

void DocumentationViewer::setupBookmarksPanel()
{
    QWidget* bookmarksWidget = new QWidget(this);
    QVBoxLayout* bookmarksLayout = new QVBoxLayout(bookmarksWidget);
    bookmarksLayout->setContentsMargins(5, 5, 5, 5);

    // Bookmarks header with buttons
    QHBoxLayout* headerLayout = new QHBoxLayout();
    QLabel* bookmarksLabel = new QLabel(tr("Bookmarks"), this);
    bookmarksLabel->setStyleSheet("font-weight: bold;");
    headerLayout->addWidget(bookmarksLabel);

    headerLayout->addStretch();

    m_addBookmarkButton = new ElaToolButton(this);
    m_addBookmarkButton->setElaIcon(ElaIconType::Plus);
    m_addBookmarkButton->setToolTip(tr("Add Bookmark"));
    m_addBookmarkButton->setFixedSize(24, 24);
    connect(m_addBookmarkButton, &ElaToolButton::clicked, [this]() {
        addBookmark();
    });
    headerLayout->addWidget(m_addBookmarkButton);

    m_removeBookmarkButton = new ElaToolButton(this);
    m_removeBookmarkButton->setElaIcon(ElaIconType::Minus);
    m_removeBookmarkButton->setToolTip(tr("Remove Bookmark"));
    m_removeBookmarkButton->setFixedSize(24, 24);
    m_removeBookmarkButton->setEnabled(false);
    connect(m_removeBookmarkButton, &ElaToolButton::clicked, [this]() {
        if (auto item = m_bookmarksList->currentItem()) {
            removeBookmark(item->data(Qt::UserRole).toString());
        }
    });
    headerLayout->addWidget(m_removeBookmarkButton);

    bookmarksLayout->addLayout(headerLayout);

    // Bookmarks list
    m_bookmarksList = new QListWidget(this);
    m_bookmarksList->setAlternatingRowColors(true);
    m_bookmarksList->setContextMenuPolicy(Qt::CustomContextMenu);
    connect(m_bookmarksList, &QListWidget::itemClicked, this, &DocumentationViewer::onBookmarkItemClicked);
    connect(m_bookmarksList, &QListWidget::itemSelectionChanged, [this]() {
        m_removeBookmarkButton->setEnabled(m_bookmarksList->currentItem() != nullptr);
    });
    connect(m_bookmarksList, &QListWidget::customContextMenuRequested, this, &DocumentationViewer::showBookmarkContextMenu);
    bookmarksLayout->addWidget(m_bookmarksList);

    // Set the bookmarks widget as the container
    m_leftSplitter->addWidget(bookmarksWidget);
}

void DocumentationViewer::setupContentArea()
{
    m_contentBrowser = new QTextBrowser(this);
    m_contentBrowser->setOpenExternalLinks(false);
    m_contentBrowser->setOpenLinks(false);

    // Setup syntax highlighter
    m_syntaxHighlighter = new DocumentationSyntaxHighlighter(m_contentBrowser->document());

    // Connect link handling
    connect(m_contentBrowser, &QTextBrowser::anchorClicked, [this](const QUrl& url) {
        if (url.isRelative()) {
            QString path = m_baseUrl.resolved(url).toString();
            loadDocumentation(path);
        } else {
            emit linkClicked(url);
        }
    });

    // Connect hover events for status updates
    connect(m_contentBrowser, &QTextBrowser::highlighted, this, [this](const QUrl& url) {
        onLinkHovered(url.toString());
    });
}

void DocumentationViewer::setupStatusBar()
{
    m_statusBar = new QStatusBar(this);
    m_statusBar->setFixedHeight(25);

    m_statusLabel = new QLabel(tr("Ready"), this);
    m_statusBar->addWidget(m_statusLabel);

    m_loadingProgress = new ElaProgressBar(this);
    m_loadingProgress->setVisible(false);
    m_loadingProgress->setFixedWidth(200);
    m_statusBar->addPermanentWidget(m_loadingProgress);
}

void DocumentationViewer::setupKeyboardShortcuts()
{
    // Bookmark shortcuts
    QShortcut* addBookmarkShortcut = new QShortcut(QKeySequence("Ctrl+B"), this);
    connect(addBookmarkShortcut, &QShortcut::activated, this, [this]() {
        addBookmark();
    });

    // Search shortcuts
    QShortcut* searchShortcut = new QShortcut(QKeySequence("Ctrl+F"), this);
    connect(searchShortcut, &QShortcut::activated, this, [this]() {
        m_searchEdit->setFocus();
        m_searchEdit->selectAll();
    });

    // Navigation shortcuts
    QShortcut* findNextShortcut = new QShortcut(QKeySequence("F3"), this);
    connect(findNextShortcut, &QShortcut::activated, this, &DocumentationViewer::findNext);

    QShortcut* findPrevShortcut = new QShortcut(QKeySequence("Shift+F3"), this);
    connect(findPrevShortcut, &QShortcut::activated, this, &DocumentationViewer::findPrevious);

    // Zoom shortcuts
    QShortcut* zoomInShortcut = new QShortcut(QKeySequence("Ctrl++"), this);
    connect(zoomInShortcut, &QShortcut::activated, this, [this]() {
        setZoomLevel(m_zoomLevel + ZOOM_STEP);
    });

    QShortcut* zoomOutShortcut = new QShortcut(QKeySequence("Ctrl+-"), this);
    connect(zoomOutShortcut, &QShortcut::activated, this, [this]() {
        setZoomLevel(m_zoomLevel - ZOOM_STEP);
    });

    QShortcut* resetZoomShortcut = new QShortcut(QKeySequence("Ctrl+0"), this);
    connect(resetZoomShortcut, &QShortcut::activated, this, [this]() {
        setZoomLevel(DEFAULT_ZOOM);
    });

    // History navigation shortcuts
    QShortcut* backShortcut = new QShortcut(QKeySequence("Alt+Left"), this);
    connect(backShortcut, &QShortcut::activated, this, &DocumentationViewer::goBack);

    QShortcut* forwardShortcut = new QShortcut(QKeySequence("Alt+Right"), this);
    connect(forwardShortcut, &QShortcut::activated, this, &DocumentationViewer::goForward);
}

bool DocumentationViewer::loadDocumentation(const QString& source)
{
    auto logger = Logger::instance();
    if (!logger) {
        qWarning() << "Logger instance not available";
        return false;
    }

    logger->info(QString("Loading documentation from: %1").arg(source), "DocumentationViewer");

    try {
        // Validate input
        if (source.isEmpty()) {
            logger->warning("Empty source provided for documentation loading", "DocumentationViewer");
            QString defaultContent = generateDefaultDocumentation();
            if (m_contentBrowser) {
                m_contentBrowser->setHtml(defaultContent);
            }
            return true;
        }

        // Show loading progress safely
        if (m_loadingProgress) {
            m_loadingProgress->setVisible(true);
        }
        if (m_statusLabel) {
            m_statusLabel->setText(tr("Loading..."));
        }

        QString content;
        bool loadSuccess = false;

        // Handle different source types
        if (source.startsWith("http://") || source.startsWith("https://")) {
            // URL loading - placeholder for future implementation
            content = tr("<h1>URL Loading</h1><p>URL loading not yet implemented: %1</p>").arg(source);
            content += generateDefaultDocumentation();
            loadSuccess = false;
        } else if (source.startsWith("<")) {
            // Direct HTML content
            content = source;
            loadSuccess = true;
        } else {
            // File path
            QFileInfo fileInfo(source);

            if (!fileInfo.exists()) {
                logger->warning(QString("File does not exist: %1").arg(source), "DocumentationViewer");
                content = tr("<h1>File Not Found</h1><p>The requested file could not be found: %1</p>").arg(source);
                content += generateDefaultDocumentation();
                loadSuccess = false;
            } else if (!fileInfo.isReadable()) {
                logger->error(QString("File is not readable: %1").arg(source), "DocumentationViewer");
                content = tr("<h1>Access Denied</h1><p>Cannot read file: %1</p><p>Please check file permissions.</p>").arg(source);
                content += generateDefaultDocumentation();
                loadSuccess = false;
            } else {
                // Load from local file
                QFile file(source);
                if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
                    QTextStream stream(&file);
                    content = stream.readAll();
                    file.close();

                    // Validate content size (10MB limit)
                    if (content.size() > 10 * 1024 * 1024) {
                        logger->warning(QString("Large file detected: %1 bytes, truncating").arg(content.size()), "DocumentationViewer");
                        content = content.left(5 * 1024 * 1024) + "\n\n<p><em>Content truncated due to size limit.</em></p>";
                    }

                    // Set base URL for relative links
                    m_baseUrl = QUrl::fromLocalFile(fileInfo.absolutePath() + "/");
                    loadSuccess = true;
                } else {
                    QString errorMsg = QString("Cannot open file: %1 - %2").arg(source, file.errorString());
                    logger->error(errorMsg, "DocumentationViewer");
                    content = tr("<h1>File Error</h1><p>%1</p>").arg(errorMsg);
                    content += generateDefaultDocumentation();
                    loadSuccess = false;
                }
            }
        }

        // Validate content before setting
        if (content.isEmpty()) {
            logger->warning("Empty content loaded, using default", "DocumentationViewer");
            content = generateDefaultDocumentation();
            loadSuccess = false;
        }

        // Set content to browser with null check
        if (m_contentBrowser) {
            m_contentBrowser->setHtml(content);
        } else {
            logger->error("Content browser is null", "DocumentationViewer");
            return false;
        }

        // Add to history only if load was successful
        if (loadSuccess) {
            addToHistory(source);
        }

        // Load table of contents with error handling
        try {
            loadTableOfContents();
        } catch (const std::exception& e) {
            logger->warning(QString("Failed to load table of contents: %1").arg(e.what()), "DocumentationViewer");
        }

        // Update UI safely
        updateNavigationButtons();

        // Hide loading progress
        if (m_loadingProgress) {
            m_loadingProgress->setVisible(false);
        }
        if (m_statusLabel) {
            m_statusLabel->setText(loadSuccess ? tr("Ready") : tr("Loaded with errors"));
        }

        if (loadSuccess) {
            logger->info("Documentation loaded successfully", "DocumentationViewer");
        } else {
            logger->warning("Documentation loaded with errors", "DocumentationViewer");
        }

        return loadSuccess;
    }
    catch (const std::exception& e) {
        // Hide loading progress
        if (m_loadingProgress) {
            m_loadingProgress->setVisible(false);
        }
        if (m_statusLabel) {
            m_statusLabel->setText(tr("Error loading documentation"));
        }

        QString errorMsg = QString("Failed to load documentation: %1").arg(e.what());
        logger->error(errorMsg, "DocumentationViewer");

        // Show error content instead of popup
        if (m_contentBrowser) {
            QString errorContent = tr("<h1>Error Loading Documentation</h1><p>%1</p>").arg(errorMsg);
            errorContent += generateDefaultDocumentation();
            m_contentBrowser->setHtml(errorContent);
        }

        return false;
    }
    catch (...) {
        // Hide loading progress
        if (m_loadingProgress) {
            m_loadingProgress->setVisible(false);
        }
        if (m_statusLabel) {
            m_statusLabel->setText(tr("Unknown error"));
        }

        QString errorMsg = "Unknown error occurred while loading documentation";
        logger->error(errorMsg, "DocumentationViewer");

        // Show generic error content
        if (m_contentBrowser) {
            QString errorContent = tr("<h1>Unknown Error</h1><p>%1</p>").arg(errorMsg);
            errorContent += generateDefaultDocumentation();
            m_contentBrowser->setHtml(errorContent);
        }

        return false;
    }
}

QString DocumentationViewer::generateDefaultDocumentation()
{
    return QString(
        "<!DOCTYPE html>"
        "<html>"
        "<head>"
        "    <title>PDF Viewer Documentation</title>"
        "    <style>"
        "        body { font-family: 'Segoe UI', Arial, sans-serif; margin: 20px; line-height: 1.6; }"
        "        h1 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }"
        "        h2 { color: #34495e; margin-top: 30px; }"
        "        h3 { color: #7f8c8d; }"
        "        .feature { background: #ecf0f1; padding: 15px; margin: 10px 0; border-radius: 5px; }"
        "        .shortcut { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: monospace; }"
        "        code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; font-family: 'Consolas', monospace; }"
        "        pre { background: #2c3e50; color: #ecf0f1; padding: 15px; border-radius: 5px; overflow-x: auto; }"
        "    </style>"
        "</head>"
        "<body>"
        "    <h1>PDF Viewer Documentation</h1>"
        "    "
        "    <h2>Welcome</h2>"
        "    <p>Welcome to the Optimized PDF Viewer documentation. This application provides a modern, "
        "    feature-rich PDF viewing experience with advanced annotation capabilities.</p>"
        "    "
        "    <div class='feature'>"
        "        <h3>🚀 Key Features</h3>"
        "        <ul>"
        "            <li><strong>Modern Interface:</strong> Ribbon-style toolbar with intuitive controls</li>"
        "            <li><strong>Multi-tab Support:</strong> Open multiple documents simultaneously</li>"
        "            <li><strong>Advanced Annotations:</strong> Text, highlight, drawing, and shape tools</li>"
        "            <li><strong>Smart Search:</strong> Full-text search with highlighting</li>"
        "            <li><strong>Bookmarks:</strong> Save and organize frequently accessed pages</li>"
        "            <li><strong>Zoom Controls:</strong> Flexible zoom options including fit-to-window</li>"
        "        </ul>"
        "    </div>"
        "    "
        "    <h2>Getting Started</h2>"
        "    <p>To open a PDF document:</p>"
        "    <ol>"
        "        <li>Click the <strong>Open</strong> button or press <span class='shortcut'>Ctrl+O</span></li>"
        "        <li>Select your PDF file from the file dialog</li>"
        "        <li>The document will open in a new tab</li>"
        "    </ol>"
        "    "
        "    <h2>Keyboard Shortcuts</h2>"
        "    <div class='feature'>"
        "        <h3>📋 Essential Shortcuts</h3>"
        "        <ul>"
        "            <li><span class='shortcut'>Ctrl+O</span> - Open document</li>"
        "            <li><span class='shortcut'>Ctrl+W</span> - Close current tab</li>"
        "            <li><span class='shortcut'>Ctrl+T</span> - New tab</li>"
        "            <li><span class='shortcut'>Ctrl+F</span> - Find/Search</li>"
        "            <li><span class='shortcut'>Ctrl+P</span> - Print document</li>"
        "            <li><span class='shortcut'>F11</span> - Toggle fullscreen</li>"
        "            <li><span class='shortcut'>F1</span> - Show this help</li>"
        "        </ul>"
        "    </div>"
        "    "
        "    <h2>Navigation</h2>"
        "    <p>Navigate through your PDF documents using:</p>"
        "    <ul>"
        "        <li><strong>Page Controls:</strong> Use the navigation buttons or page input field</li>"
        "        <li><strong>Thumbnails Panel:</strong> Click on thumbnail images for quick navigation</li>"
        "        <li><strong>Outline Panel:</strong> Use the document outline for structured navigation</li>"
        "        <li><strong>Keyboard:</strong> Arrow keys, Page Up/Down, Home/End</li>"
        "    </ul>"
        "    "
        "    <h2>Annotation Tools</h2>"
        "    <div class='feature'>"
        "        <h3>✏️ Available Tools</h3>"
        "        <ul>"
        "            <li><strong>Text Annotations:</strong> Add text notes and comments</li>"
        "            <li><strong>Highlighting:</strong> Highlight important text passages</li>"
        "            <li><strong>Drawing Tools:</strong> Freehand drawing and sketching</li>"
        "            <li><strong>Shapes:</strong> Rectangles, circles, arrows, and lines</li>"
        "            <li><strong>Stamps:</strong> Predefined stamps and custom images</li>"
        "        </ul>"
        "    </div>"
        "    "
        "    <h2>Advanced Features</h2>"
        "    <h3>Search and Find</h3>"
        "    <p>Use the search functionality to quickly locate content:</p>"
        "    <pre><code>1. Press Ctrl+F to open search panel"
        "2. Enter your search term"
        "3. Use Find Next/Previous to navigate results"
        "4. Search supports case-sensitive and whole-word options</code></pre>"
        "    "
        "    <h3>Bookmarks</h3>"
        "    <p>Save important pages for quick access:</p>"
        "    <ul>"
        "        <li>Press <span class='shortcut'>Ctrl+B</span> to bookmark current page</li>"
        "        <li>Access bookmarks from the sidebar panel</li>"
        "        <li>Organize bookmarks with custom names</li>"
        "    </ul>"
        "    "
        "    <h2>Troubleshooting</h2>"
        "    <div class='feature'>"
        "        <h3>🔧 Common Issues</h3>"
        "        <ul>"
        "            <li><strong>Document won't open:</strong> Check file permissions and format</li>"
        "            <li><strong>Slow performance:</strong> Reduce cache size in settings</li>"
        "            <li><strong>Display issues:</strong> Try toggling hardware acceleration</li>"
        "            <li><strong>Annotation problems:</strong> Ensure PDF allows modifications</li>"
        "        </ul>"
        "    </div>"
        "    "
        "    <h2>Support</h2>"
        "    <p>For additional help and support:</p>"
        "    <ul>"
        "        <li>Check the application logs for error details</li>"
        "        <li>Visit the project documentation</li>"
        "        <li>Report issues with detailed descriptions</li>"
        "    </ul>"
        "    "
        "    <hr>"
        "    <p><em>PDF Viewer v1.0 - Built with Qt and ElaWidgetTools</em></p>"
        "</body>"
        "</html>"
    );
}

void DocumentationViewer::setBaseUrl(const QUrl& baseUrl)
{
    m_baseUrl = baseUrl;
}

int DocumentationViewer::getZoomLevel() const
{
    return m_zoomLevel;
}

void DocumentationViewer::setZoomLevel(int zoomLevel)
{
    try {
        // Validate input
        if (zoomLevel < MIN_ZOOM || zoomLevel > MAX_ZOOM) {
            Logger::instance()->warning(QString("Invalid zoom level: %1, clamping to valid range").arg(zoomLevel), "DocumentationViewer");
        }

        zoomLevel = qBound(MIN_ZOOM, zoomLevel, MAX_ZOOM);

        if (m_zoomLevel != zoomLevel) {
            m_zoomLevel = zoomLevel;

            // Apply zoom to content browser with null check
            if (m_contentBrowser) {
                QFont font = m_contentBrowser->font();
                int baseSize = 10; // Base font size
                int newSize = baseSize * m_zoomLevel / 100;

                // Validate font size
                if (newSize < 6) newSize = 6;   // Minimum readable size
                if (newSize > 72) newSize = 72; // Maximum reasonable size

                font.setPointSize(newSize);
                m_contentBrowser->setFont(font);
            } else {
                Logger::instance()->warning("Content browser is null in setZoomLevel", "DocumentationViewer");
            }

            // Update zoom controls with error handling
            try {
                updateZoomControls();
            } catch (const std::exception& e) {
                Logger::instance()->warning(QString("Failed to update zoom controls: %1").arg(e.what()), "DocumentationViewer");
            }

            // Save to settings with null check
            if (m_settings) {
                m_settings->setValue("documentation/zoomLevel", m_zoomLevel);
            }
        }
    }
    catch (const std::exception& e) {
        Logger::instance()->error(QString("Exception in setZoomLevel: %1").arg(e.what()), "DocumentationViewer");
    }
}

void DocumentationViewer::searchText(const QString& searchTerm, bool caseSensitive, bool wholeWords)
{
    try {
        // Validate input
        if (searchTerm.length() > 1000) {
            Logger::instance()->warning("Search term too long, truncating", "DocumentationViewer");
            QString truncatedTerm = searchTerm.left(1000);
            searchText(truncatedTerm, caseSensitive, wholeWords);
            return;
        }

        m_currentSearchTerm = searchTerm;
        m_caseSensitive = caseSensitive;
        m_wholeWords = wholeWords;

        if (searchTerm.isEmpty()) {
            clearSearch();
            return;
        }

        // Clear previous search
        m_searchResults.clear();
        m_currentSearchResult = -1;

        // Validate content browser and document
        if (!m_contentBrowser) {
            Logger::instance()->error("Content browser is null in searchText", "DocumentationViewer");
            return;
        }

        QTextDocument* document = m_contentBrowser->document();
        if (!document) {
            Logger::instance()->error("Document is null in searchText", "DocumentationViewer");
            return;
        }

        QTextCursor cursor(document);

        QTextDocument::FindFlags flags;
        if (caseSensitive) flags |= QTextDocument::FindCaseSensitively;
        if (wholeWords) flags |= QTextDocument::FindWholeWords;

        // Find all occurrences with safety limit
        int searchCount = 0;
        const int MAX_SEARCH_RESULTS = 10000; // Prevent excessive memory usage

        while (!cursor.isNull() && !cursor.atEnd() && searchCount < MAX_SEARCH_RESULTS) {
            cursor = document->find(searchTerm, cursor, flags);
            if (!cursor.isNull()) {
                m_searchResults.append(QString::number(cursor.position()));
                cursor.movePosition(QTextCursor::NextCharacter);
                searchCount++;
            }
        }

        if (searchCount >= MAX_SEARCH_RESULTS) {
            Logger::instance()->warning(QString("Search results limited to %1 items").arg(MAX_SEARCH_RESULTS), "DocumentationViewer");
        }

        // Update search highlighting with error handling
        try {
            updateSearchHighlighting();
        } catch (const std::exception& e) {
            Logger::instance()->warning(QString("Failed to update search highlighting: %1").arg(e.what()), "DocumentationViewer");
        }

        // Update UI safely
        if (!m_searchResults.isEmpty()) {
            m_currentSearchResult = 0;
            try {
                findNext();
            } catch (const std::exception& e) {
                Logger::instance()->warning(QString("Failed to navigate to search result: %1").arg(e.what()), "DocumentationViewer");
            }
        }

        // Update search results label safely
        if (m_searchResultsLabel) {
            if (m_searchResults.isEmpty()) {
                m_searchResultsLabel->setText(tr("No results found"));
            } else {
                m_searchResultsLabel->setText(tr("%1 of %2").arg(m_currentSearchResult + 1).arg(m_searchResults.size()));
            }
        }

        // Enable/disable navigation buttons safely
        if (m_findNextButton) {
            m_findNextButton->setEnabled(!m_searchResults.isEmpty());
        }
        if (m_findPrevButton) {
            m_findPrevButton->setEnabled(!m_searchResults.isEmpty());
        }

        emit searchResultsChanged(m_searchResults.size(), m_currentSearchResult + 1);
    }
    catch (const std::exception& e) {
        Logger::instance()->error(QString("Exception in searchText: %1").arg(e.what()), "DocumentationViewer");
        if (m_searchResultsLabel) {
            m_searchResultsLabel->setText(tr("Search error"));
        }
    }
}

void DocumentationViewer::clearSearch()
{
    m_currentSearchTerm.clear();
    m_searchResults.clear();
    m_currentSearchResult = -1;

    // Clear highlighting
    QTextCursor cursor(m_contentBrowser->document());
    cursor.select(QTextCursor::Document);
    QTextCharFormat format;
    format.setBackground(QBrush());
    cursor.setCharFormat(format);

    // Update UI
    m_searchResultsLabel->clear();
    m_findNextButton->setEnabled(false);
    m_findPrevButton->setEnabled(false);

    emit searchResultsChanged(0, 0);
}

void DocumentationViewer::findNext()
{
    if (m_searchResults.isEmpty()) return;

    m_currentSearchResult = (m_currentSearchResult + 1) % m_searchResults.size();

    // Navigate to result
    int position = m_searchResults[m_currentSearchResult].toInt();
    QTextCursor cursor(m_contentBrowser->document());
    cursor.setPosition(position);
    m_contentBrowser->setTextCursor(cursor);
    m_contentBrowser->ensureCursorVisible();

    // Update highlighting to show current result
    updateSearchHighlighting();

    // Update label
    m_searchResultsLabel->setText(tr("%1 of %2").arg(m_currentSearchResult + 1).arg(m_searchResults.size()));

    emit searchResultsChanged(m_searchResults.size(), m_currentSearchResult + 1);
}

void DocumentationViewer::findPrevious()
{
    if (m_searchResults.isEmpty()) return;

    m_currentSearchResult = (m_currentSearchResult - 1 + m_searchResults.size()) % m_searchResults.size();

    // Navigate to result
    int position = m_searchResults[m_currentSearchResult].toInt();
    QTextCursor cursor(m_contentBrowser->document());
    cursor.setPosition(position);
    m_contentBrowser->setTextCursor(cursor);
    m_contentBrowser->ensureCursorVisible();

    // Update highlighting to show current result
    updateSearchHighlighting();

    // Update label
    m_searchResultsLabel->setText(tr("%1 of %2").arg(m_currentSearchResult + 1).arg(m_searchResults.size()));

    emit searchResultsChanged(m_searchResults.size(), m_currentSearchResult + 1);
}

void DocumentationViewer::addBookmark(const QString& title)
{
    QString currentUrl = m_contentBrowser->source().toString();
    if (currentUrl.isEmpty()) return;

    QString bookmarkTitle = title;
    if (bookmarkTitle.isEmpty()) {
        bookmarkTitle = m_contentBrowser->documentTitle();
        if (bookmarkTitle.isEmpty()) {
            bookmarkTitle = QFileInfo(currentUrl).baseName();
        }
    }

    // Check if bookmark already exists
    for (const QString& bookmark : m_bookmarks) {
        if (bookmark.contains(currentUrl)) {
            QMessageBox::information(this, tr("Bookmark"), tr("This page is already bookmarked."));
            return;
        }
    }

    // Add bookmark
    QString bookmarkData = QString("%1|%2").arg(bookmarkTitle, currentUrl);
    m_bookmarks.append(bookmarkData);

    // Update UI
    QListWidgetItem* item = new QListWidgetItem(bookmarkTitle, m_bookmarksList);
    item->setData(Qt::UserRole, currentUrl);
    item->setToolTip(currentUrl);

    // Save bookmarks
    saveBookmarks();

    emit bookmarksChanged();
}

void DocumentationViewer::removeBookmark(const QString& url)
{
    // Remove from list
    for (int i = 0; i < m_bookmarks.size(); ++i) {
        if (m_bookmarks[i].contains(url)) {
            m_bookmarks.removeAt(i);
            break;
        }
    }

    // Update UI
    for (int i = 0; i < m_bookmarksList->count(); ++i) {
        QListWidgetItem* item = m_bookmarksList->item(i);
        if (item && item->data(Qt::UserRole).toString() == url) {
            delete m_bookmarksList->takeItem(i);
            break;
        }
    }

    // Save bookmarks
    saveBookmarks();

    emit bookmarksChanged();
}

void DocumentationViewer::navigateToBookmark(const QString& url)
{
    loadDocumentation(url);
}

void DocumentationViewer::showBookmarkContextMenu(const QPoint& pos)
{
    QListWidgetItem* item = m_bookmarksList->itemAt(pos);
    QMenu contextMenu(this);

    if (item) {
        // Actions for existing bookmark
        QAction* openAction = contextMenu.addAction(tr("Open"));
        connect(openAction, &QAction::triggered, [this, item]() {
            onBookmarkItemClicked(item);
        });

        QAction* renameAction = contextMenu.addAction(tr("Rename"));
        connect(renameAction, &QAction::triggered, [this, item]() {
            bool ok;
            QString newName = QInputDialog::getText(this, tr("Rename Bookmark"),
                                                  tr("Enter new name:"), QLineEdit::Normal,
                                                  item->text(), &ok);
            if (ok && !newName.isEmpty()) {
                // Update bookmark data
                QString url = item->data(Qt::UserRole).toString();
                for (int i = 0; i < m_bookmarks.size(); ++i) {
                    if (m_bookmarks[i].contains(url)) {
                        m_bookmarks[i] = QString("%1|%2").arg(newName, url);
                        break;
                    }
                }
                item->setText(newName);
                saveBookmarks();
            }
        });

        contextMenu.addSeparator();

        QAction* removeAction = contextMenu.addAction(tr("Remove"));
        connect(removeAction, &QAction::triggered, [this, item]() {
            removeBookmark(item->data(Qt::UserRole).toString());
        });
    } else {
        // Actions for empty area
        QAction* addAction = contextMenu.addAction(tr("Add Bookmark"));
        connect(addAction, &QAction::triggered, [this]() {
            addBookmark();
        });
    }

    contextMenu.exec(m_bookmarksList->mapToGlobal(pos));
}

void DocumentationViewer::goBack()
{
    if (m_historyIndex > 0) {
        m_historyIndex--;
        QString url = m_history[m_historyIndex];

        // Load without adding to history
        QFileInfo fileInfo(url);
        if (fileInfo.exists()) {
            QFile file(url);
            if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
                QTextStream stream(&file);
                QString content = stream.readAll();
                file.close();

                m_baseUrl = QUrl::fromLocalFile(fileInfo.absolutePath() + "/");
                m_contentBrowser->setHtml(content);
            }
        }

        updateNavigationButtons();
    }
}

void DocumentationViewer::goForward()
{
    if (m_historyIndex < m_history.size() - 1) {
        m_historyIndex++;
        QString url = m_history[m_historyIndex];

        // Load without adding to history
        QFileInfo fileInfo(url);
        if (fileInfo.exists()) {
            QFile file(url);
            if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
                QTextStream stream(&file);
                QString content = stream.readAll();
                file.close();

                m_baseUrl = QUrl::fromLocalFile(fileInfo.absolutePath() + "/");
                m_contentBrowser->setHtml(content);
            }
        }

        updateNavigationButtons();
    }
}

void DocumentationViewer::zoomIn()
{
    setZoomLevel(m_zoomLevel + ZOOM_STEP);
}

void DocumentationViewer::zoomOut()
{
    setZoomLevel(m_zoomLevel - ZOOM_STEP);
}

void DocumentationViewer::resetZoom()
{
    setZoomLevel(DEFAULT_ZOOM);
}

void DocumentationViewer::fitToWidth()
{
    // Calculate zoom level to fit content width
    int contentWidth = m_contentBrowser->document()->size().width();
    int viewportWidth = m_contentBrowser->viewport()->width();

    if (contentWidth > 0) {
        int fitZoom = (viewportWidth * 100) / contentWidth;
        fitZoom = qBound(MIN_ZOOM, fitZoom, MAX_ZOOM);
        setZoomLevel(fitZoom);
    }
}

void DocumentationViewer::printPage()
{
    QPrinter printer(QPrinter::HighResolution);
    QPrintDialog dialog(&printer, this);
    dialog.setWindowTitle(tr("Print Documentation"));

    if (dialog.exec() == QDialog::Accepted) {
        m_contentBrowser->print(&printer);
    }
}

void DocumentationViewer::showPrintPreview()
{
    QPrinter printer(QPrinter::HighResolution);
    QPrintPreviewDialog preview(&printer, this);
    preview.setWindowTitle(tr("Print Preview"));

    connect(&preview, &QPrintPreviewDialog::paintRequested, [this](QPrinter* printer) {
        m_contentBrowser->print(printer);
    });

    preview.exec();
}

// Private slot implementations
void DocumentationViewer::onSearchTextChanged()
{
    QString text = m_searchEdit->text();
    if (text.isEmpty()) {
        clearSearch();
    } else {
        // Delay search to avoid too frequent updates
        QTimer::singleShot(300, [this, text]() {
            if (m_searchEdit->text() == text) {
                searchText(text, m_caseSensitive, m_wholeWords);
            }
        });
    }
}

void DocumentationViewer::onSearchOptionsChanged()
{
    if (!m_currentSearchTerm.isEmpty()) {
        searchText(m_currentSearchTerm, m_caseSensitive, m_wholeWords);
    }
}

void DocumentationViewer::onTocItemClicked(QTreeWidgetItem* item, int column)
{
    Q_UNUSED(column)

    if (item && item->data(0, Qt::UserRole).isValid()) {
        QString anchor = item->data(0, Qt::UserRole).toString();

        if (anchor.startsWith("#")) {
            // Navigate to anchor within current document
            QString anchorName = anchor.mid(1); // Remove '#'

            // Try to find the anchor in the document
            QTextDocument* document = m_contentBrowser->document();
            QTextCursor cursor(document);

            // Look for heading with this id or text
            QRegularExpression anchorRegex(QString("(?:<[^>]+\\s+id=[\"']%1[\"'][^>]*>|<h[1-6][^>]*>\\s*%2\\s*</h[1-6]>)")
                                         .arg(QRegularExpression::escape(anchorName))
                                         .arg(QRegularExpression::escape(item->text(0))),
                                         QRegularExpression::CaseInsensitiveOption);

            cursor = document->find(anchorRegex, cursor);

            if (!cursor.isNull()) {
                m_contentBrowser->setTextCursor(cursor);
                m_contentBrowser->ensureCursorVisible();
            } else {
                // Fallback: search for the heading text
                cursor.movePosition(QTextCursor::Start);
                cursor = document->find(item->text(0), cursor);
                if (!cursor.isNull()) {
                    m_contentBrowser->setTextCursor(cursor);
                    m_contentBrowser->ensureCursorVisible();
                }
            }
        } else {
            // Load different document
            loadDocumentation(anchor);
        }
    }
}

void DocumentationViewer::onBookmarkItemClicked(QListWidgetItem* item)
{
    if (item) {
        QString url = item->data(Qt::UserRole).toString();
        navigateToBookmark(url);
    }
}

void DocumentationViewer::onZoomSliderChanged(int value)
{
    setZoomLevel(value);
}

void DocumentationViewer::onZoomComboChanged(const QString& text)
{
    if (text == "Fit Width") {
        fitToWidth();
    } else {
        QString cleanText = text;
        cleanText.remove('%');
        bool ok;
        int zoom = cleanText.toInt(&ok);
        if (ok) {
            setZoomLevel(zoom);
        }
    }
}

void DocumentationViewer::updateNavigationButtons()
{
    m_backButton->setEnabled(m_historyIndex > 0);
    m_forwardButton->setEnabled(m_historyIndex < m_history.size() - 1);

    emit navigationChanged(m_historyIndex > 0, m_historyIndex < m_history.size() - 1);
}

void DocumentationViewer::onLinkHovered(const QString& link)
{
    if (link.isEmpty()) {
        m_statusLabel->setText(tr("Ready"));
    } else {
        m_statusLabel->setText(link);
    }
}

// Private helper methods
void DocumentationViewer::loadTableOfContents()
{
    m_tocTree->clear();

    // Get HTML content from the browser
    QString html = m_contentBrowser->toHtml();

    // Parse HTML for headings and create TOC
    QRegularExpression headingRegex("<h([1-6])(?:[^>]*)(?:\\s+id=[\"']([^\"']*)[\"'])?[^>]*>([^<]+)</h[1-6]>",
                                   QRegularExpression::CaseInsensitiveOption);

    QRegularExpressionMatchIterator iterator = headingRegex.globalMatch(html);

    QTreeWidgetItem* parentItems[7] = {nullptr}; // Index 0 unused, 1-6 for h1-h6

    while (iterator.hasNext()) {
        QRegularExpressionMatch match = iterator.next();

        int level = match.captured(1).toInt();
        QString id = match.captured(2);
        QString text = match.captured(3).trimmed();

        if (text.isEmpty()) continue;

        // Clean up text (remove HTML entities, etc.)
        text = text.replace("&amp;", "&").replace("&lt;", "<").replace("&gt;", ">");

        // Generate anchor if no id provided
        if (id.isEmpty()) {
            id = text.toLower()
                    .replace(QRegularExpression("[^a-z0-9\\s]"), "")
                    .replace(QRegularExpression("\\s+"), "-");
        }

        // Find appropriate parent
        QTreeWidgetItem* parent = nullptr;
        for (int i = level - 1; i >= 1; --i) {
            if (parentItems[i]) {
                parent = parentItems[i];
                break;
            }
        }

        // Create tree item
        QTreeWidgetItem* item;
        if (parent) {
            item = new QTreeWidgetItem(parent, {text});
        } else {
            item = new QTreeWidgetItem(m_tocTree, {text});
        }

        // Store anchor for navigation
        item->setData(0, Qt::UserRole, QString("#%1").arg(id));
        item->setToolTip(0, text);

        // Update parent tracking
        parentItems[level] = item;
        // Clear deeper levels
        for (int i = level + 1; i <= 6; ++i) {
            parentItems[i] = nullptr;
        }
    }

    // Expand first two levels by default
    m_tocTree->expandToDepth(1);

    // If no headings found, add a default entry
    if (m_tocTree->topLevelItemCount() == 0) {
        QTreeWidgetItem* defaultItem = new QTreeWidgetItem(m_tocTree, {tr("Documentation")});
        defaultItem->setData(0, Qt::UserRole, "#top");
    }
}

void DocumentationViewer::updateSearchHighlighting()
{
    if (m_currentSearchTerm.isEmpty()) return;

    QTextDocument* document = m_contentBrowser->document();
    QTextCursor cursor(document);

    // Clear previous highlighting
    cursor.select(QTextCursor::Document);
    QTextCharFormat clearFormat;
    clearFormat.setBackground(QBrush());
    cursor.setCharFormat(clearFormat);

    // Apply new highlighting
    QTextDocument::FindFlags flags;
    if (m_caseSensitive) flags |= QTextDocument::FindCaseSensitively;
    if (m_wholeWords) flags |= QTextDocument::FindWholeWords;

    QTextCharFormat highlightFormat;
    highlightFormat.setBackground(QColor(255, 255, 0, 120)); // Yellow highlight

    QTextCharFormat currentHighlightFormat;
    currentHighlightFormat.setBackground(QColor(255, 165, 0, 180)); // Orange highlight for current result

    cursor.movePosition(QTextCursor::Start);
    int resultIndex = 0;
    while (!cursor.isNull() && !cursor.atEnd()) {
        cursor = document->find(m_currentSearchTerm, cursor, flags);
        if (!cursor.isNull()) {
            // Use different highlighting for current result
            if (resultIndex == m_currentSearchResult) {
                cursor.setCharFormat(currentHighlightFormat);
            } else {
                cursor.setCharFormat(highlightFormat);
            }
            resultIndex++;
        }
    }
}

void DocumentationViewer::saveBookmarks()
{
    if (!m_settings) return;

    m_settings->beginWriteArray("documentation/bookmarks");
    for (int i = 0; i < m_bookmarks.size(); ++i) {
        m_settings->setArrayIndex(i);
        QStringList parts = m_bookmarks[i].split('|');
        if (parts.size() == 2) {
            m_settings->setValue("title", parts[0]);
            m_settings->setValue("url", parts[1]);
        }
    }
    m_settings->endArray();
}

void DocumentationViewer::loadBookmarks()
{
    if (!m_settings) return;

    m_bookmarks.clear();
    m_bookmarksList->clear();

    int size = m_settings->beginReadArray("documentation/bookmarks");
    for (int i = 0; i < size; ++i) {
        m_settings->setArrayIndex(i);
        QString title = m_settings->value("title").toString();
        QString url = m_settings->value("url").toString();

        if (!title.isEmpty() && !url.isEmpty()) {
            m_bookmarks.append(QString("%1|%2").arg(title, url));

            QListWidgetItem* item = new QListWidgetItem(title, m_bookmarksList);
            item->setData(Qt::UserRole, url);
            item->setToolTip(url);
        }
    }
    m_settings->endArray();
}

void DocumentationViewer::addToHistory(const QString& url)
{
    // Remove any entries after current position
    if (m_historyIndex < m_history.size() - 1) {
        m_history.erase(m_history.begin() + m_historyIndex + 1, m_history.end());
    }

    // Add new entry
    m_history.append(url);
    m_historyIndex = m_history.size() - 1;

    // Limit history size
    const int MAX_HISTORY = 50;
    if (m_history.size() > MAX_HISTORY) {
        m_history.removeFirst();
        m_historyIndex--;
    }

    updateNavigationButtons();
}

void DocumentationViewer::showHistoryMenu()
{
    QMenu historyMenu(this);
    historyMenu.setTitle(tr("History"));

    if (m_history.isEmpty()) {
        QAction* emptyAction = historyMenu.addAction(tr("No history available"));
        emptyAction->setEnabled(false);
    } else {
        // Add recent history items (up to 10)
        int startIndex = qMax(0, m_history.size() - 10);
        for (int i = startIndex; i < m_history.size(); ++i) {
            QString url = m_history[i];
            QString displayName = QFileInfo(url).baseName();
            if (displayName.isEmpty()) {
                displayName = url;
            }

            QAction* action = historyMenu.addAction(displayName);
            action->setData(i);
            action->setCheckable(true);
            action->setChecked(i == m_historyIndex);

            connect(action, &QAction::triggered, [this, i]() {
                navigateToHistoryIndex(i);
            });
        }

        if (m_history.size() > 10) {
            historyMenu.addSeparator();
            QAction* showAllAction = historyMenu.addAction(tr("Show All History..."));
            connect(showAllAction, &QAction::triggered, this, &DocumentationViewer::showFullHistoryDialog);
        }
    }

    // Show menu at cursor position
    historyMenu.exec(QCursor::pos());
}

void DocumentationViewer::navigateToHistoryIndex(int index)
{
    if (index >= 0 && index < m_history.size()) {
        m_historyIndex = index;
        QString url = m_history[index];

        // Load without adding to history
        QFileInfo fileInfo(url);
        if (fileInfo.exists()) {
            QFile file(url);
            if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
                QTextStream stream(&file);
                QString content = stream.readAll();
                m_contentBrowser->setHtml(content);
                loadTableOfContents();
            }
        }

        updateNavigationButtons();
    }
}

void DocumentationViewer::showFullHistoryDialog()
{
    // For now, just show a simple message
    // In a full implementation, this could open a dialog with the complete history
    QMessageBox::information(this, tr("History"),
                           tr("Full history dialog not yet implemented.\n"
                              "Use the back/forward buttons or recent history menu."));
}

void DocumentationViewer::updateZoomControls()
{
    // Update zoom combo
    QString zoomText = QString("%1%").arg(m_zoomLevel);
    m_zoomCombo->setCurrentText(zoomText);

    // Update zoom slider if it exists
    if (m_zoomSlider) {
        m_zoomSlider->setValue(m_zoomLevel);
    }
}
