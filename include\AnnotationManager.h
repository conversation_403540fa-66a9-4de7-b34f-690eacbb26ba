#ifndef ANNOTATIONMANAGER_H
#define ANNOTATIONMANAGER_H

#include <QObject>
#include <QList>
#include <QHash>
#include <QString>
#include <QJsonObject>
#include <QJsonArray>
#include <QPointF>
#include <QRectF>
#include <QUndoStack>
#include <memory>

#include "Annotation.h"

class QPainter;

class AnnotationManager : public QObject
{
    Q_OBJECT

public:
    explicit AnnotationManager(QObject *parent = nullptr);
    ~AnnotationManager();

    // Annotation management
    void addAnnotation(std::unique_ptr<Annotation> annotation);
    void removeAnnotation(const QString& annotationId);
    void removeAnnotation(Annotation* annotation);
    void clearAnnotations();
    void clearAnnotationsForPage(int pageNumber);
    
    // Annotation access
    QList<Annotation*> getAnnotations() const;
    QList<Annotation*> getAnnotationsForPage(int pageNumber) const;
    Annotation* getAnnotation(const QString& annotationId) const;
    Annotation* getAnnotationAt(const QPointF& point, int pageNumber) const;
    
    // Selection management
    void selectAnnotation(Annotation* annotation);
    void selectAnnotation(const QString& annotationId);
    void deselectAll();
    QList<Annotation*> getSelectedAnnotations() const;
    Annotation* getSelectedAnnotation() const; // Returns first selected annotation

    // Access all annotations
    QList<Annotation*> getAllAnnotations() const;
    
    // Annotation creation helpers
    HighlightAnnotation* createHighlight(int pageNumber, const QList<QRectF>& quads);
    NoteAnnotation* createNote(int pageNumber, const QPointF& position);
    DrawingAnnotation* createDrawing(int pageNumber);
    ShapeAnnotation* createShape(int pageNumber, ShapeAnnotation::ShapeType shapeType, const QRectF& bounds);
    TextAnnotation* createText(int pageNumber, const QRectF& bounds, const QString& text = QString());
    
    // Rendering
    void renderAnnotations(QPainter* painter, int pageNumber, double dpi, double zoomFactor) const;
    
    // Persistence
    bool saveAnnotations(const QString& filePath) const;
    bool loadAnnotations(const QString& filePath);
    QJsonObject toJson() const;
    void fromJson(const QJsonObject& json);
    
    // Statistics
    int getAnnotationCount() const;
    int getAnnotationCountForPage(int pageNumber) const;
    QHash<int, int> getAnnotationCountsByPage() const;
    
    // Utility
    QString generateAnnotationFilePath(const QString& pdfFilePath) const;

    // Undo/Redo support
    QUndoStack* getUndoStack() const { return m_undoStack; }
    void pushCommand(QUndoCommand* command);
    void undo();
    void redo();
    bool canUndo() const;
    bool canRedo() const;

signals:
    void annotationAdded(Annotation* annotation);
    void annotationRemoved(const QString& annotationId);
    void annotationChanged(Annotation* annotation);
    void annotationSelected(Annotation* annotation);
    void annotationDeselected(Annotation* annotation);
    void annotationsCleared();

private slots:
    void onAnnotationChanged();
    void onAnnotationSelected(bool selected);

private:
    void connectAnnotationSignals(Annotation* annotation);
    void disconnectAnnotationSignals(Annotation* annotation);
    std::unique_ptr<Annotation> createAnnotationFromJson(const QJsonObject& json);

    QList<Annotation*> m_annotations;
    QHash<QString, Annotation*> m_annotationIndex; // For fast lookup by ID
    QList<Annotation*> m_selectedAnnotations;
    QUndoStack* m_undoStack;
};

// Annotation creation factory
class AnnotationFactory
{
public:
    static std::unique_ptr<Annotation> createAnnotation(AnnotationType type);
    static std::unique_ptr<Annotation> createFromJson(const QJsonObject& json);
    
    // Specific creation methods
    static std::unique_ptr<HighlightAnnotation> createHighlight(const QList<QRectF>& quads);
    static std::unique_ptr<NoteAnnotation> createNote(const QPointF& position);
    static std::unique_ptr<DrawingAnnotation> createDrawing();
    static std::unique_ptr<ShapeAnnotation> createShape(ShapeAnnotation::ShapeType shapeType, const QRectF& bounds);
    static std::unique_ptr<TextAnnotation> createText(const QRectF& bounds, const QString& text = QString());
};

#endif // ANNOTATIONMANAGER_H
