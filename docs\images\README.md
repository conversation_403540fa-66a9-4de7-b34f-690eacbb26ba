# Documentation Images

This directory contains images used in the documentation.

## Required Images

- `logo.png` - Project logo (recommended size: 64x64 or 128x128 pixels)
- `favicon-16x16.png` - Small favicon (16x16 pixels)
- `favicon-32x32.png` - Standard favicon (32x32 pixels)
- `apple-touch-icon.png` - Apple touch icon (180x180 pixels)
- `og-image.png` - Open Graph image for social sharing (1200x630 pixels)
- `twitter-card.png` - Twitter card image (1200x600 pixels)

## Usage

These images are automatically referenced by the Doxygen configuration and HTML templates. Make sure to:

1. Replace the placeholder images with your actual project branding
2. Optimize images for web (use appropriate compression)
3. Ensure images follow accessibility guidelines (provide alt text where needed)
4. Test images in both light and dark themes

## Image Guidelines

- **Logo**: Should work well on both light and dark backgrounds
- **Favicons**: Should be simple and recognizable at small sizes
- **Social images**: Should include project name and brief description
- **File formats**: PNG recommended for logos and icons, JPEG for photos
- **Optimization**: Use tools like ImageOptim, TinyPNG, or similar to reduce file sizes

## Creating Images

You can use tools like:
- **GIMP** (free, open-source)
- **Inkscape** (free, vector graphics)
- **Canva** (online, templates available)
- **Figma** (online, collaborative)
- **Adobe Creative Suite** (commercial)

## Accessibility

Ensure all images:
- Have appropriate contrast ratios
- Include descriptive alt text
- Work well for users with visual impairments
- Are not the only way to convey important information
