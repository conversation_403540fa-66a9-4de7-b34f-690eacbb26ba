/**
 * Fragment copy button functionality for Doxygen Awesome CSS
 * Adds copy-to-clipboard buttons to code fragments
 */

class DoxygenAwesomeFragmentCopyButton extends HTMLElement {
    static copyIcon = `<svg xmlns="http://www.w3.org/2000/svg" height="18" viewBox="0 0 24 24" width="18"><path d="M0 0h24v24H0z" fill="none"/><path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/></svg>`
    static successIcon = `<svg xmlns="http://www.w3.org/2000/svg" height="18" viewBox="0 0 24 24" width="18"><path d="M0 0h24v24H0z" fill="none"/><path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/></svg>`

    constructor() {
        super();
        this.onclick = this.copyFragment.bind(this);
    }

    connectedCallback() {
        this.innerHTML = DoxygenAwesomeFragmentCopyButton.copyIcon;
        this.title = "Copy to clipboard";
    }

    async copyFragment() {
        const fragment = this.closest('.doxygen-awesome-fragment-wrapper')?.querySelector('.fragment, pre.fragment');
        if (!fragment) return;

        let textContent = '';
        
        // Handle different fragment types
        if (fragment.classList.contains('fragment')) {
            // For div.fragment, extract text from .line elements
            const lines = fragment.querySelectorAll('.line');
            textContent = Array.from(lines).map(line => {
                // Remove line numbers if present
                const lineNumber = line.querySelector('.lineno');
                if (lineNumber) {
                    lineNumber.remove();
                }
                return line.textContent;
            }).join('\n');
        } else {
            // For pre.fragment, use textContent directly
            textContent = fragment.textContent;
        }

        // Clean up the text
        textContent = textContent
            .replace(/^\s*\n/, '') // Remove leading newline
            .replace(/\n\s*$/, '') // Remove trailing newline and spaces
            .replace(/\t/g, '    '); // Convert tabs to spaces

        try {
            await navigator.clipboard.writeText(textContent);
            this.showSuccess();
        } catch (err) {
            // Fallback for older browsers
            this.fallbackCopy(textContent);
        }
    }

    showSuccess() {
        this.innerHTML = DoxygenAwesomeFragmentCopyButton.successIcon;
        this.classList.add('success');
        this.title = "Copied!";
        
        setTimeout(() => {
            this.innerHTML = DoxygenAwesomeFragmentCopyButton.copyIcon;
            this.classList.remove('success');
            this.title = "Copy to clipboard";
        }, 2000);
    }

    fallbackCopy(text) {
        // Create a temporary textarea element
        const textarea = document.createElement('textarea');
        textarea.value = text;
        textarea.style.position = 'fixed';
        textarea.style.opacity = '0';
        textarea.style.pointerEvents = 'none';
        
        document.body.appendChild(textarea);
        textarea.select();
        
        try {
            const successful = document.execCommand('copy');
            if (successful) {
                this.showSuccess();
            }
        } catch (err) {
            console.error('Fallback copy failed:', err);
        } finally {
            document.body.removeChild(textarea);
        }
    }
}

customElements.define("doxygen-awesome-fragment-copy-button", DoxygenAwesomeFragmentCopyButton);

// Initialize copy buttons on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeFragmentCopyButtons();
});

function initializeFragmentCopyButtons() {
    // Find all code fragments
    const fragments = document.querySelectorAll('.fragment, pre.fragment');
    
    fragments.forEach(fragment => {
        // Skip if already wrapped
        if (fragment.closest('.doxygen-awesome-fragment-wrapper')) {
            return;
        }
        
        // Create wrapper
        const wrapper = document.createElement('div');
        wrapper.className = 'doxygen-awesome-fragment-wrapper';
        
        // Insert wrapper before fragment
        fragment.parentNode.insertBefore(wrapper, fragment);
        
        // Move fragment into wrapper
        wrapper.appendChild(fragment);
        
        // Create and add copy button
        const copyButton = document.createElement('doxygen-awesome-fragment-copy-button');
        wrapper.appendChild(copyButton);
    });
}

// Handle dynamically added fragments (for AJAX content)
const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // Check if the added node contains fragments
                    const fragments = node.querySelectorAll ? 
                        node.querySelectorAll('.fragment, pre.fragment') : [];
                    
                    if (fragments.length > 0) {
                        initializeFragmentCopyButtons();
                    }
                    
                    // Check if the added node itself is a fragment
                    if (node.classList && (node.classList.contains('fragment') || 
                        (node.tagName === 'PRE' && node.classList.contains('fragment')))) {
                        initializeFragmentCopyButtons();
                    }
                }
            });
        }
    });
});

// Start observing
observer.observe(document.body, {
    childList: true,
    subtree: true
});

// Keyboard shortcut for copying focused fragment (Ctrl/Cmd + Shift + C)
document.addEventListener('keydown', function(e) {
    if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'C') {
        // Find the fragment that contains the current selection or focus
        const selection = window.getSelection();
        let targetFragment = null;
        
        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            targetFragment = range.commonAncestorContainer;
            
            // Traverse up to find the fragment
            while (targetFragment && targetFragment.nodeType !== Node.ELEMENT_NODE) {
                targetFragment = targetFragment.parentNode;
            }
            
            while (targetFragment && 
                   !targetFragment.classList.contains('fragment') && 
                   !(targetFragment.tagName === 'PRE' && targetFragment.classList.contains('fragment'))) {
                targetFragment = targetFragment.parentNode;
            }
        }
        
        if (targetFragment) {
            e.preventDefault();
            const wrapper = targetFragment.closest('.doxygen-awesome-fragment-wrapper');
            const copyButton = wrapper?.querySelector('doxygen-awesome-fragment-copy-button');
            if (copyButton) {
                copyButton.copyFragment();
            }
        }
    }
});

// Export for use in other scripts
window.DoxygenAwesomeFragmentCopyButton = DoxygenAwesomeFragmentCopyButton;
window.initializeFragmentCopyButtons = initializeFragmentCopyButtons;
