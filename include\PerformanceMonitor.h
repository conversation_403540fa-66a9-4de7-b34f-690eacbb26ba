#ifndef PERFORMANCEMONITOR_H
#define PERFORMANCEMONITOR_H

#include <QObject>
#include <QTimer>
#include <QElapsedTimer>
#include <QMutex>
#include <QMap>
#include <QQueue>
#include <QDateTime>

struct PerformanceMetric {
    QString name;
    QString category;
    double value;
    QString unit;
    QDateTime timestamp;
    
    PerformanceMetric() : value(0.0) {}
    PerformanceMetric(const QString& n, const QString& c, double v, const QString& u)
        : name(n), category(c), value(v), unit(u), timestamp(QDateTime::currentDateTime()) {}
};

struct RenderingStats {
    int totalPagesRendered = 0;
    int cacheHits = 0;
    int cacheMisses = 0;
    double averageRenderTime = 0.0;
    double peakMemoryUsage = 0.0;
    int activeThreads = 0;
    QDateTime lastUpdate;
    
    double getCacheHitRatio() const {
        int total = cacheHits + cacheMisses;
        return total > 0 ? (double)cacheHits / total * 100.0 : 0.0;
    }
};

struct SystemMetrics {
    double cpuUsage = 0.0;
    qint64 memoryUsage = 0;
    qint64 availableMemory = 0;
    int threadCount = 0;
    double diskUsage = 0.0;
    QDateTime timestamp;
    
    double getMemoryUsagePercent() const {
        qint64 total = memoryUsage + availableMemory;
        return total > 0 ? (double)memoryUsage / total * 100.0 : 0.0;
    }
};

/**
 * @brief Performance monitoring and optimization system
 * 
 * This class provides comprehensive performance monitoring capabilities,
 * including memory usage tracking, rendering performance metrics, and
 * system resource monitoring.
 */
class PerformanceMonitor : public QObject
{
    Q_OBJECT

public:
    explicit PerformanceMonitor(QObject* parent = nullptr);
    ~PerformanceMonitor();

    // Monitoring control
    void startMonitoring();
    void stopMonitoring();
    bool isMonitoring() const { return m_isMonitoring; }
    
    // Metric collection
    void recordMetric(const QString& name, const QString& category, double value, const QString& unit = "");
    void recordRenderTime(double milliseconds);
    void recordCacheHit();
    void recordCacheMiss();
    void recordMemoryUsage(qint64 bytes);
    void recordActiveThreads(int count);
    
    // Performance timers
    void startTimer(const QString& operation);
    void endTimer(const QString& operation);
    
    // Data access
    QList<PerformanceMetric> getMetrics(const QString& category = "") const;
    QList<PerformanceMetric> getRecentMetrics(int minutes = 5) const;
    RenderingStats getRenderingStats() const;
    SystemMetrics getSystemMetrics() const;
    
    // Statistics
    double getAverageMetric(const QString& name, int minutes = 5) const;
    double getPeakMetric(const QString& name, int minutes = 5) const;
    QMap<QString, double> getCategorySummary() const;
    
    // Optimization recommendations
    QStringList getOptimizationRecommendations() const;
    bool shouldOptimizeCache() const;
    bool shouldReduceThreads() const;
    bool shouldClearMemory() const;
    
    // Configuration
    void setMonitoringInterval(int milliseconds);
    void setMaxHistorySize(int size);
    void setMetricRetentionTime(int minutes);
    
    // Export/Import
    QString exportMetricsToJson() const;
    QString exportMetricsToCsv() const;
    bool importMetricsFromJson(const QString& json);

signals:
    void metricsUpdated();
    void performanceAlert(const QString& message, const QString& category);
    void optimizationRecommended(const QString& recommendation);
    void memoryThresholdExceeded(qint64 currentUsage, qint64 threshold);
    void renderingPerformanceChanged(const RenderingStats& stats);

private slots:
    void updateSystemMetrics();
    void checkPerformanceThresholds();
    void cleanupOldMetrics();

private:
    void initializeMonitoring();
    void collectSystemInfo();
    double getCurrentCpuUsage() const;
    qint64 getCurrentMemoryUsage() const;
    qint64 getAvailableMemory() const;
    int getCurrentThreadCount() const;
    
    void analyzePerformanceTrends();
    void generateRecommendations();
    
    // Monitoring state
    bool m_isMonitoring;
    QTimer* m_monitoringTimer;
    QTimer* m_cleanupTimer;
    int m_monitoringInterval;
    int m_maxHistorySize;
    int m_metricRetentionMinutes;
    
    // Data storage
    mutable QMutex m_metricsMutex;
    QQueue<PerformanceMetric> m_metrics;
    QMap<QString, QElapsedTimer> m_activeTimers;
    
    // Performance tracking
    RenderingStats m_renderingStats;
    SystemMetrics m_lastSystemMetrics;
    QQueue<SystemMetrics> m_systemHistory;
    
    // Thresholds and alerts
    qint64 m_memoryThreshold;
    double m_cpuThreshold;
    int m_maxActiveThreads;
    
    // Optimization state
    QStringList m_currentRecommendations;
    QDateTime m_lastOptimizationCheck;
};

#endif // PERFORMANCEMONITOR_H
