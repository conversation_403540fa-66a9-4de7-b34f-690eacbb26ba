#ifndef DOCUMENTATIONSYNTAXHIGHLIGHTER_H
#define DOCUMENTATIONSYNTAXHIGHLIGHTER_H

#include <QSyntaxHighlighter>
#include <QTextDocument>
#include <QRegularExpression>
#include <QTextCharFormat>

/**
 * @brief Syntax highlighter for documentation with code block support
 */
class DocumentationSyntaxHighlighter : public QSyntaxHighlighter
{
    Q_OBJECT

public:
    explicit DocumentationSyntaxHighlighter(QTextDocument* parent = nullptr);

protected:
    void highlightBlock(const QString& text) override;

private:
    struct HighlightingRule
    {
        QRegularExpression pattern;
        QTextCharFormat format;
    };
    QVector<HighlightingRule> highlightingRules;

    // Formats for different elements
    QTextCharFormat codeBlockFormat;
    QTextCharFormat inlineCodeFormat;
    QTextCharFormat keywordFormat;
    QTextCharFormat stringFormat;
    QTextCharFormat commentFormat;
    QTextCharFormat numberFormat;
    QTextCharFormat functionFormat;
    QTextCharFormat headingFormat;
    QTextCharFormat linkFormat;
    QTextCharFormat boldFormat;
    QTextCharFormat italicFormat;

    void setupFormats();
    void setupRules();
};

#endif // DOCUMENTATIONSYNTAXHIGHLIGHTER_H
