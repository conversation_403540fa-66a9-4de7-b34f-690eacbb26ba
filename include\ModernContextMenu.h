#ifndef MODERNCONTEXTMENU_H
#define MODERNCONTEXTMENU_H

#include "ElaIntegration.h"
#include <QAction>

class ModernContextMenu : public ElaMenu
{
    Q_OBJECT

public:
    explicit ModernContextMenu(QWidget *parent = nullptr);
    ~ModernContextMenu();

    // Enhanced action creation with icons and shortcuts
    QAction* addActionWithIcon(ElaIconType::IconName icon, const QString& text, 
                              const QKeySequence& shortcut = QKeySequence(), 
                              const QString& tooltip = QString());
    
    QAction* addActionWithIcon(ElaIconType::IconName icon, const QString& text, 
                              const QString& shortcut, 
                              const QString& tooltip = QString());

    // Add separator with modern styling
    void addStyledSeparator();
    
    // Add submenu with modern styling
    ModernContextMenu* addSubMenu(ElaIconType::IconName icon, const QString& title);
    
    // Set menu theme
    void setMenuTheme(ElaThemeType::ThemeMode themeMode);

protected:
    void paintEvent(QPaintEvent *event) override;
    void showEvent(QShowEvent *event) override;

private:
    void setupStyling();
    void updateTheme();
    
    ElaThemeType::ThemeMode m_themeMode;
    static const int MENU_RADIUS = 8;
    static const int ITEM_HEIGHT = 32;
    static const int ICON_SIZE = 16;
};

#endif // MODERNCONTEXTMENU_H
