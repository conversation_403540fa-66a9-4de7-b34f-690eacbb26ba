#ifndef DOCUMENTTAB_H
#define DOCUMENTTAB_H

#include <QWidget>
#include <QSizeF>
#include "PdfController.h"
#include "Annotation.h"

class QLabel;
class QScrollArea;
class AnnotationManager;
class AnnotationToolbar;
class AnnotationOverlay;
class TextSelectionOverlay;
class AdvancedZoomManager;

class DocumentTab : public QWidget
{
    Q_OBJECT

public:
    // View mode enum
    enum class ViewMode { SinglePage, ContinuousPage, FacingPage };

    explicit DocumentTab(QWidget *parent = nullptr);
    ~DocumentTab();

    // Document management
    bool loadDocument(const QString& filePath);
    void closeDocument();
    bool isDocumentLoaded() const { return m_isDocumentLoaded; }
    QString getFilePath() const { return m_filePath; }
    QString getFileName() const;

    // Page navigation
    int getCurrentPage() const { return m_currentPage; }
    int getPageCount() const;
    void setCurrentPage(int page);
    void nextPage();
    void previousPage();
    void firstPage();
    void lastPage();

    // Zoom control
    double getZoomFactor() const { return m_zoomFactor; }
    void setZoomFactor(double factor);
    void zoomIn();
    void zoomOut();
    void zoom100();
    void fitToWindow();
    void fitToWidth();

    // Rotation
    int getCurrentRotation() const { return m_currentRotation; }
    void setRotation(int rotation);

    // View mode methods
    void setSinglePageMode();
    void setContinuousPageMode();
    void setFacingPageMode();
    ViewMode getViewMode() const { return m_viewMode; }

    // UI components
    QScrollArea* getScrollArea() const { return m_scrollArea; }
    QLabel* getPageLabel() const { return m_pageLabel; }
    PdfController* getPdfController() { return &m_pdfController; }

    // Search functionality
    QList<SearchResult> getSearchResults() const { return m_searchResults; }
    void setSearchResults(const QList<SearchResult>& results) { m_searchResults = results; }
    int getCurrentSearchIndex() const { return m_currentSearchIndex; }
    void setCurrentSearchIndex(int index) { m_currentSearchIndex = index; }

    // Enhanced search navigation
    void nextSearchResult();
    void previousSearchResult();
    void jumpToSearchResult(int index);
    void highlightSearchResults(bool highlight = true);
    void clearSearchHighlights();
    bool hasSearchResults() const { return !m_searchResults.isEmpty(); }
    int getSearchResultsCount() const { return m_searchResults.size(); }

    // Search within current page
    void searchInCurrentPage(const QString& term, bool caseSensitive = false);
    void findNext();
    void findPrevious();

    // Annotation functionality
    AnnotationManager* getAnnotationManager() const { return m_annotationManager; }
    void setAnnotationToolbar(AnnotationToolbar* toolbar);
    AnnotationToolbar* getAnnotationToolbar() const { return m_annotationToolbar; }
    AnnotationOverlay* getAnnotationOverlay() const { return m_annotationOverlay; }

    // Text selection functionality
    void setTextSelectionEnabled(bool enabled);
    bool isTextSelectionEnabled() const;
    bool hasTextSelection() const;
    QString getSelectedText() const;
    void copySelectedTextToClipboard();
    void clearTextSelection();
    TextSelectionOverlay* getTextSelectionOverlay() const { return m_textSelectionOverlay; }

    // Advanced zoom functionality
    void enableAdvancedZoom(bool enabled);
    void performSmartZoom();
    void enableMagnifier(bool enabled);
    bool isMagnifierEnabled() const;
    void zoomToRect(const QRectF& rect);
    AdvancedZoomManager* getAdvancedZoomManager() const { return m_advancedZoomManager; }

    // Page rendering (public for annotation updates)
    void requestCurrentPage();

signals:
    void documentLoaded(bool success, const QString& errorString);
    void loadingProgressChanged(int percentage, const QString& message);
    void pageChanged(int pageNum);
    void zoomChanged(double zoomFactor);
    void statusMessage(const QString& message, int timeout = 0);
    void textSelectionChanged(const QString& selectedText);
    void textSelectionCleared();

protected:
    void resizeEvent(QResizeEvent* event) override;
    bool eventFilter(QObject* object, QEvent* event) override;

private slots:
    void onDocumentLoaded(bool success, const QString& errorString);
    void onLoadingProgress(int percentage, const QString& message);
    void onPageReady(int pageNum, const QPixmap& pixmap, bool isHighQuality);

private:
    void createUi();
    void applyFitMode();
    void smoothScrollTo(int x, int y, int duration = 300);

    // UI components
    QScrollArea* m_scrollArea;
    QLabel* m_pageLabel;

    // PDF controller
    PdfController m_pdfController;

    // Document state
    QString m_filePath;
    bool m_isDocumentLoaded = false;
    int m_currentPage = 0;
    double m_zoomFactor = 1.0;
    int m_currentRotation = 0;
    QSizeF m_originalPageSize;

    // Fit mode
    enum class FitMode { None, FitToWindow, FitToWidth };
    FitMode m_fitMode = FitMode::None;

    // View mode
    ViewMode m_viewMode = ViewMode::SinglePage;

    // Search state
    QList<SearchResult> m_searchResults;
    int m_currentSearchIndex = -1;

    // Annotation support
    AnnotationManager* m_annotationManager = nullptr;
    AnnotationToolbar* m_annotationToolbar = nullptr;
    AnnotationOverlay* m_annotationOverlay = nullptr;

    // Text selection support
    TextSelectionOverlay* m_textSelectionOverlay = nullptr;

    // Advanced zoom support
    AdvancedZoomManager* m_advancedZoomManager = nullptr;

    // Destruction flag to prevent operations during cleanup
    bool m_isBeingDestroyed = false;

    // Helper method to check if the tab is in a valid state for operations
    bool isValidForOperations() const { return !m_isBeingDestroyed && !isHidden(); }
};

#endif // DOCUMENTTAB_H
