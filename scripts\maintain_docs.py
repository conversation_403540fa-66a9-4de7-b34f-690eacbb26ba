#!/usr/bin/env python3
"""
Documentation Maintenance Script for Optimized PDF Viewer

This script provides maintenance utilities for documentation including:
- Automatic link updates when files are moved
- Image optimization and compression
- Outdated content detection
- Documentation metrics and analytics
- Cleanup of unused assets
"""

import os
import sys
import argparse
import re
import shutil
from pathlib import Path
from typing import List, Dict, Set, Tuple, Optional
import json
import time
from datetime import datetime, timedelta
import subprocess
from dataclasses import dataclass

@dataclass
class DocumentationMetrics:
    """Documentation metrics and statistics"""
    total_files: int
    html_files: int
    markdown_files: int
    image_files: int
    total_size_mb: float
    last_updated: str
    broken_links: int
    outdated_files: int

class DocumentationMaintainer:
    """Main documentation maintenance class"""
    
    def __init__(self, docs_root: str, project_root: str):
        self.docs_root = Path(docs_root).resolve()
        self.project_root = Path(project_root).resolve()
        self.metrics = None
    
    def collect_metrics(self) -> DocumentationMetrics:
        """Collect comprehensive documentation metrics"""
        print("📊 Collecting documentation metrics...")
        
        total_files = 0
        html_files = 0
        markdown_files = 0
        image_files = 0
        total_size = 0
        newest_time = 0
        
        for file_path in self.docs_root.rglob('*'):
            if file_path.is_file():
                total_files += 1
                total_size += file_path.stat().st_size
                newest_time = max(newest_time, file_path.stat().st_mtime)
                
                suffix = file_path.suffix.lower()
                if suffix == '.html':
                    html_files += 1
                elif suffix in ['.md', '.markdown']:
                    markdown_files += 1
                elif suffix in ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp']:
                    image_files += 1
        
        last_updated = datetime.fromtimestamp(newest_time).isoformat() if newest_time > 0 else "Unknown"
        
        self.metrics = DocumentationMetrics(
            total_files=total_files,
            html_files=html_files,
            markdown_files=markdown_files,
            image_files=image_files,
            total_size_mb=total_size / (1024 * 1024),
            last_updated=last_updated,
            broken_links=0,  # Will be updated by link checker
            outdated_files=0  # Will be updated by outdated content checker
        )
        
        return self.metrics
    
    def optimize_images(self, dry_run: bool = False) -> Dict[str, any]:
        """Optimize images in documentation"""
        print("🖼️ Optimizing images...")
        
        results = {
            'processed': 0,
            'size_saved_mb': 0.0,
            'errors': []
        }
        
        image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.svg'}
        
        for image_path in self.docs_root.rglob('*'):
            if image_path.suffix.lower() in image_extensions:
                try:
                    original_size = image_path.stat().st_size
                    
                    if not dry_run:
                        optimized = self._optimize_single_image(image_path)
                        if optimized:
                            new_size = image_path.stat().st_size
                            size_saved = original_size - new_size
                            results['size_saved_mb'] += size_saved / (1024 * 1024)
                    
                    results['processed'] += 1
                    
                except Exception as e:
                    results['errors'].append(f"Error optimizing {image_path}: {e}")
        
        return results
    
    def _optimize_single_image(self, image_path: Path) -> bool:
        """Optimize a single image file"""
        suffix = image_path.suffix.lower()
        
        try:
            if suffix in ['.png']:
                # Use optipng if available
                if shutil.which('optipng'):
                    subprocess.run(['optipng', '-quiet', str(image_path)], check=True)
                    return True
            elif suffix in ['.jpg', '.jpeg']:
                # Use jpegoptim if available
                if shutil.which('jpegoptim'):
                    subprocess.run(['jpegoptim', '--quiet', '--strip-all', str(image_path)], check=True)
                    return True
            elif suffix == '.svg':
                # Use svgo if available
                if shutil.which('svgo'):
                    subprocess.run(['svgo', '--quiet', str(image_path)], check=True)
                    return True
        except subprocess.CalledProcessError:
            pass
        
        return False
    
    def find_outdated_content(self, days_threshold: int = 90) -> List[Path]:
        """Find potentially outdated documentation files"""
        print(f"📅 Finding content older than {days_threshold} days...")
        
        threshold_date = datetime.now() - timedelta(days=days_threshold)
        outdated_files = []
        
        for file_path in self.docs_root.rglob('*.md'):
            try:
                # Check file modification time
                mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                if mtime < threshold_date:
                    # Check if file references current version or recent features
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Look for version references or date stamps
                    if self._appears_outdated(content):
                        outdated_files.append(file_path)
            
            except Exception as e:
                print(f"Warning: Could not check {file_path}: {e}")
        
        return outdated_files
    
    def _appears_outdated(self, content: str) -> bool:
        """Check if content appears to be outdated"""
        # Look for old version references
        old_version_patterns = [
            r'version\s+0\.',  # Version 0.x
            r'Qt\s*5',         # Qt 5 references
            r'C\+\+14',        # Old C++ standard
            r'Windows\s*[78]', # Old Windows versions
        ]
        
        for pattern in old_version_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True
        
        return False
    
    def cleanup_unused_assets(self, dry_run: bool = False) -> Dict[str, any]:
        """Remove unused images and assets"""
        print("🧹 Cleaning up unused assets...")
        
        results = {
            'removed_files': [],
            'size_freed_mb': 0.0,
            'errors': []
        }
        
        # Find all asset files
        asset_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.svg', '.css', '.js', '.ico'}
        asset_files = set()
        
        for asset_path in self.docs_root.rglob('*'):
            if asset_path.suffix.lower() in asset_extensions:
                asset_files.add(asset_path)
        
        # Find all references in HTML and markdown files
        referenced_assets = set()
        
        for doc_file in self.docs_root.rglob('*'):
            if doc_file.suffix.lower() in {'.html', '.md', '.markdown'}:
                try:
                    with open(doc_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Find asset references
                    patterns = [
                        r'src=["\']([^"\']+)["\']',      # img src
                        r'href=["\']([^"\']+\.css)["\']', # CSS links
                        r'src=["\']([^"\']+\.js)["\']',   # JS scripts
                        r'!\[[^\]]*\]\(([^)]+)\)',        # Markdown images
                    ]
                    
                    for pattern in patterns:
                        for match in re.finditer(pattern, content):
                            asset_ref = match.group(1)
                            # Resolve relative path
                            if not asset_ref.startswith(('http://', 'https://')):
                                asset_path = self._resolve_asset_path(doc_file, asset_ref)
                                if asset_path and asset_path.exists():
                                    referenced_assets.add(asset_path)
                
                except Exception as e:
                    results['errors'].append(f"Error reading {doc_file}: {e}")
        
        # Find unused assets
        unused_assets = asset_files - referenced_assets
        
        for unused_asset in unused_assets:
            try:
                size = unused_asset.stat().st_size
                results['size_freed_mb'] += size / (1024 * 1024)
                results['removed_files'].append(str(unused_asset.relative_to(self.docs_root)))
                
                if not dry_run:
                    unused_asset.unlink()
            
            except Exception as e:
                results['errors'].append(f"Error removing {unused_asset}: {e}")
        
        return results
    
    def _resolve_asset_path(self, doc_file: Path, asset_ref: str) -> Optional[Path]:
        """Resolve asset path relative to document file"""
        try:
            if asset_ref.startswith('/'):
                # Absolute path from docs root
                return self.docs_root / asset_ref.lstrip('/')
            else:
                # Relative path
                return (doc_file.parent / asset_ref).resolve()
        except:
            return None
    
    def update_links(self, old_path: str, new_path: str, dry_run: bool = False) -> Dict[str, any]:
        """Update links when files are moved"""
        print(f"🔗 Updating links from {old_path} to {new_path}...")
        
        results = {
            'files_updated': [],
            'links_updated': 0,
            'errors': []
        }
        
        # Find all documentation files
        for doc_file in self.docs_root.rglob('*'):
            if doc_file.suffix.lower() in {'.html', '.md', '.markdown'}:
                try:
                    with open(doc_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    original_content = content
                    
                    # Update various link formats
                    patterns = [
                        (r'href=["\']' + re.escape(old_path) + r'["\']', f'href="{new_path}"'),
                        (r'src=["\']' + re.escape(old_path) + r'["\']', f'src="{new_path}"'),
                        (r'\[([^\]]*)\]\(' + re.escape(old_path) + r'\)', f'[\\1]({new_path})'),
                    ]
                    
                    for pattern, replacement in patterns:
                        content = re.sub(pattern, replacement, content)
                    
                    if content != original_content:
                        results['links_updated'] += content.count(new_path) - original_content.count(new_path)
                        results['files_updated'].append(str(doc_file.relative_to(self.docs_root)))
                        
                        if not dry_run:
                            with open(doc_file, 'w', encoding='utf-8') as f:
                                f.write(content)
                
                except Exception as e:
                    results['errors'].append(f"Error updating {doc_file}: {e}")
        
        return results
    
    def generate_report(self) -> str:
        """Generate comprehensive maintenance report"""
        if not self.metrics:
            self.collect_metrics()
        
        report = f"""
# Documentation Maintenance Report
Generated: {datetime.now().isoformat()}

## Overview
- Total files: {self.metrics.total_files}
- HTML files: {self.metrics.html_files}
- Markdown files: {self.metrics.markdown_files}
- Image files: {self.metrics.image_files}
- Total size: {self.metrics.total_size_mb:.2f} MB
- Last updated: {self.metrics.last_updated}

## Health Status
- Broken links: {self.metrics.broken_links}
- Outdated files: {self.metrics.outdated_files}

## Recommendations
"""
        
        if self.metrics.total_size_mb > 100:
            report += "- Consider image optimization to reduce size\n"
        
        if self.metrics.outdated_files > 0:
            report += f"- Review {self.metrics.outdated_files} potentially outdated files\n"
        
        if self.metrics.broken_links > 0:
            report += f"- Fix {self.metrics.broken_links} broken links\n"
        
        return report

def main():
    parser = argparse.ArgumentParser(description="Maintain documentation")
    parser.add_argument("--docs-dir", required=True, help="Documentation directory")
    parser.add_argument("--project-root", default=".", help="Project root directory")
    parser.add_argument("--action", choices=['metrics', 'optimize', 'cleanup', 'outdated', 'update-links'], 
                       required=True, help="Maintenance action to perform")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be done without making changes")
    parser.add_argument("--old-path", help="Old path for link updates")
    parser.add_argument("--new-path", help="New path for link updates")
    parser.add_argument("--days", type=int, default=90, help="Days threshold for outdated content")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.docs_dir):
        print(f"❌ Documentation directory not found: {args.docs_dir}")
        sys.exit(1)
    
    maintainer = DocumentationMaintainer(args.docs_dir, args.project_root)
    
    if args.action == 'metrics':
        metrics = maintainer.collect_metrics()
        print(maintainer.generate_report())
    
    elif args.action == 'optimize':
        results = maintainer.optimize_images(dry_run=args.dry_run)
        print(f"✅ Processed {results['processed']} images")
        print(f"💾 Saved {results['size_saved_mb']:.2f} MB")
        if results['errors']:
            print(f"⚠️ {len(results['errors'])} errors occurred")
    
    elif args.action == 'cleanup':
        results = maintainer.cleanup_unused_assets(dry_run=args.dry_run)
        print(f"🗑️ Removed {len(results['removed_files'])} unused files")
        print(f"💾 Freed {results['size_freed_mb']:.2f} MB")
        if results['errors']:
            print(f"⚠️ {len(results['errors'])} errors occurred")
    
    elif args.action == 'outdated':
        outdated_files = maintainer.find_outdated_content(args.days)
        print(f"📅 Found {len(outdated_files)} potentially outdated files:")
        for file_path in outdated_files:
            print(f"  - {file_path.relative_to(maintainer.docs_root)}")
    
    elif args.action == 'update-links':
        if not args.old_path or not args.new_path:
            print("❌ --old-path and --new-path are required for link updates")
            sys.exit(1)
        
        results = maintainer.update_links(args.old_path, args.new_path, dry_run=args.dry_run)
        print(f"🔗 Updated {results['links_updated']} links in {len(results['files_updated'])} files")
        if results['errors']:
            print(f"⚠️ {len(results['errors'])} errors occurred")

if __name__ == "__main__":
    main()
