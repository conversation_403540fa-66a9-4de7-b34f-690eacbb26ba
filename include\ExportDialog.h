#ifndef EXPORTDIALOG_H
#define EXPORTDIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QComboBox>
#include <QSpinBox>
#include <QSlider>
#include <QLabel>
#include <QPushButton>
#include <QCheckBox>
#include <QLineEdit>
#include <QProgressDialog>
#include "PdfController.h"

class ExportDialog : public QDialog
{
    Q_OBJECT

public:
    enum ExportType {
        CurrentPage,
        AllPages,
        PageRange
    };

    explicit ExportDialog(PdfController* controller, ExportType type, int currentPage, int totalPages, QWidget *parent = nullptr);

private slots:
    void onFormatChanged();
    void onQualityChanged(int value);
    void onDpiChanged(int value);
    void onBrowseClicked();
    void onExportClicked();
    void onPageRangeChanged();

private:
    void setupUi();
    void updateQualityLabel();
    void updateDpiLabel();
    void updateFileNamePreview();
    bool validatePageRange();
    QList<int> parsePageRange(const QString& range);
    void performExport();
    void exportPage(int pageNumber, const QString& filePath);

    // UI components
    QVBoxLayout* m_mainLayout;
    
    // Export type group
    QGroupBox* m_typeGroup;
    QLabel* m_typeLabel;
    
    // Page range group
    QGroupBox* m_rangeGroup;
    QLineEdit* m_rangeEdit;
    QLabel* m_rangeHelpLabel;
    
    // Format settings group
    QGroupBox* m_formatGroup;
    QComboBox* m_formatCombo;
    QSlider* m_qualitySlider;
    QLabel* m_qualityLabel;
    QSpinBox* m_dpiSpinBox;
    QLabel* m_dpiLabel;
    QCheckBox* m_transparentBackgroundCheck;
    
    // Output settings group
    QGroupBox* m_outputGroup;
    QLineEdit* m_directoryEdit;
    QPushButton* m_browseButton;
    QLineEdit* m_fileNameEdit;
    QLabel* m_previewLabel;
    
    // Buttons
    QHBoxLayout* m_buttonLayout;
    QPushButton* m_exportButton;
    QPushButton* m_cancelButton;
    
    // Data
    PdfController* m_controller;
    ExportType m_exportType;
    int m_currentPage;
    int m_totalPages;
    QProgressDialog* m_progressDialog;
};

#endif // EXPORTDIALOG_H
