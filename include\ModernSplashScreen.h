#ifndef MODERNSPLASHSCREEN_H
#define MODERNSPLASHSCREEN_H

#include "ElaIntegration.h"
#include <QPropertyAnimation>
#include <QTimer>
#include <QGraphicsDropShadowEffect>

class ModernSplashScreen : public QWidget
{
    Q_OBJECT

public:
    explicit ModernSplashScreen(QWidget *parent = nullptr);
    ~ModernSplashScreen();

    // Display control
    void showSplash();
    void hideSplash();
    void setProgress(int value, const QString& message = QString());
    void setApplicationInfo(const QString& name, const QString& version);
    
    // Animation control
    void setFadeInDuration(int ms);
    void setFadeOutDuration(int ms);
    void setDisplayDuration(int ms);

signals:
    void splashFinished();
    void progressChanged(int value);

protected:
    void paintEvent(QPaintEvent *event) override;
    void showEvent(QShowEvent *event) override;
    void hideEvent(QHideEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;

private slots:
    void onFadeInFinished();
    void onFadeOutFinished();
    void updateAnimation();
    void autoHide();

private:
    void setupUI();
    void setupAnimations();
    void updateTheme();
    void drawBackground(QPainter& painter);
    void drawLogo(QPainter& painter);
    void drawText(QPainter& painter);
    void drawProgressBar(QPainter& painter);
    void drawLoadingSpinner(QPainter& painter);
    
    // UI Components
    QVBoxLayout* m_mainLayout;
    ElaText* m_titleLabel;
    ElaText* m_versionLabel;
    ElaText* m_statusLabel;
    ElaProgress* m_progressBar;
    
    // Animation
    QPropertyAnimation* m_fadeAnimation;
    QPropertyAnimation* m_scaleAnimation;
    QTimer* m_animationTimer;
    QTimer* m_autoHideTimer;
    
    // State
    int m_currentProgress;
    QString m_currentMessage;
    QString m_applicationName;
    QString m_applicationVersion;
    ElaThemeType::ThemeMode m_themeMode;
    
    // Animation properties
    int m_fadeInDuration;
    int m_fadeOutDuration;
    int m_displayDuration;
    int m_rotationAngle;
    qreal m_scaleValue;
    
    // Visual properties
    QPixmap m_logoPixmap;
    QColor m_backgroundColor;
    QColor m_textColor;
    QColor m_accentColor;
    
    // Constants
    static const int DEFAULT_WIDTH = 480;
    static const int DEFAULT_HEIGHT = 320;
    static const int LOGO_SIZE = 64;
    static const int PROGRESS_HEIGHT = 4;
    static const int SPINNER_SIZE = 32;
    static const int ANIMATION_SPEED = 8; // degrees per frame
    static const int FADE_IN_DURATION = 500;
    static const int FADE_OUT_DURATION = 300;
    static const int DISPLAY_DURATION = 2000;
};

#endif // MODERNSPLASHSCREEN_H
