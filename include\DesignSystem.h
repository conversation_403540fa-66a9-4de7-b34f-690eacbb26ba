#ifndef DESIGNSYSTEM_H
#define DESIGNSYSTEM_H

#include <QObject>
#include <QColor>
#include <QFont>
#include <QMargins>
#include <QSize>
#include <QString>

/**
 * @brief Centralized design system for consistent visual hierarchy
 * 
 * This class provides a unified design system with consistent colors,
 * typography, spacing, and layout guidelines throughout the application.
 */
class DesignSystem
{
public:
    static DesignSystem* instance();

    // Color System
    struct Colors {
        // Primary Colors (Microsoft Office inspired)
        static const QColor Primary;           // #0078d4
        static const QColor PrimaryHover;      // #106ebe
        static const QColor PrimaryPressed;    // #005a9e
        static const QColor PrimaryLight;      // #deecf9
        
        // Neutral Colors (VS Code inspired)
        static const QColor TextPrimary;       // #1e1e1e
        static const QColor TextSecondary;     // #616161
        static const QColor TextTertiary;      // #8e8e8e
        static const QColor TextDisabled;      // #a19f9d
        
        // Background Colors
        static const QColor BackgroundPrimary; // #ffffff
        static const QColor BackgroundSecondary; // #f8f9fa
        static const QColor BackgroundTertiary; // #f1f3f4
        static const QColor BackgroundHover;   // #f6f8fa
        
        // Border Colors
        static const QColor BorderPrimary;     // #e1e4e8
        static const QColor BorderSecondary;   // #d1d5db
        static const QColor BorderHover;       // #0366d6
        
        // Status Colors
        static const QColor Success;           // #28a745
        static const QColor Warning;           // #ffc107
        static const QColor Error;             // #dc3545
        static const QColor Info;              // #17a2b8
    };

    // Typography System
    struct Typography {
        // Font Families
        static const QString PrimaryFont;     // "Segoe UI", system-ui, sans-serif
        static const QString MonospaceFont;   // "Consolas", "Monaco", monospace
        
        // Font Sizes (in pixels)
        static const int DisplayLarge;        // 36px
        static const int DisplayMedium;       // 32px
        static const int DisplaySmall;        // 28px
        
        static const int HeadingLarge;        // 24px
        static const int HeadingMedium;       // 20px
        static const int HeadingSmall;        // 18px
        
        static const int BodyLarge;           // 16px
        static const int BodyMedium;          // 14px
        static const int BodySmall;           // 12px
        
        static const int CaptionLarge;        // 11px
        static const int CaptionSmall;        // 10px
        
        // Font Weights
        static const int WeightLight;         // 300
        static const int WeightRegular;       // 400
        static const int WeightMedium;        // 500
        static const int WeightSemiBold;      // 600
        static const int WeightBold;          // 700
        
        // Line Heights (multipliers)
        static const double LineHeightTight;  // 1.2
        static const double LineHeightNormal; // 1.4
        static const double LineHeightLoose;  // 1.6
    };

    // Spacing System (8px grid)
    struct Spacing {
        static const int XSmall;              // 4px
        static const int Small;               // 8px
        static const int Medium;              // 16px
        static const int Large;               // 24px
        static const int XLarge;              // 32px
        static const int XXLarge;             // 48px
        static const int XXXLarge;            // 64px
        
        // Component-specific spacing
        static const int ButtonPadding;       // 12px
        static const int CardPadding;         // 16px
        static const int SectionPadding;      // 24px
        static const int PagePadding;         // 32px
    };

    // Border Radius System
    struct BorderRadius {
        static const int None;                // 0px
        static const int Small;               // 4px
        static const int Medium;              // 6px
        static const int Large;               // 8px
        static const int XLarge;              // 12px
        static const int Round;               // 50%
    };

    // Shadow System
    struct Shadows {
        static const QString None;
        static const QString Small;           // Subtle shadow for cards
        static const QString Medium;          // Standard shadow for modals
        static const QString Large;           // Prominent shadow for overlays
    };

    // Component Sizes
    struct ComponentSizes {
        // Button sizes
        static const QSize ButtonSmall;       // 24x24
        static const QSize ButtonMedium;      // 32x32
        static const QSize ButtonLarge;       // 48x48
        
        // Icon sizes
        static const int IconSmall;           // 16px
        static const int IconMedium;          // 20px
        static const int IconLarge;           // 24px
        static const int IconXLarge;          // 32px
        
        // Input heights
        static const int InputSmall;          // 28px
        static const int InputMedium;         // 32px
        static const int InputLarge;          // 40px
    };

    // Animation System
    struct Animations {
        static const int DurationFast;        // 150ms
        static const int DurationNormal;      // 250ms
        static const int DurationSlow;        // 400ms
        
        static const QString EasingStandard;  // cubic-bezier(0.4, 0.0, 0.2, 1)
        static const QString EasingDecelerate; // cubic-bezier(0.0, 0.0, 0.2, 1)
        static const QString EasingAccelerate; // cubic-bezier(0.4, 0.0, 1, 1)
    };

    // Utility Methods
    static QString getColorStyleSheet(const QColor& color);
    static QString getFontStyleSheet(const QString& family, int size, int weight = Typography::WeightRegular);
    static QString getSpacingStyleSheet(int top, int right, int bottom, int left);
    static QString getBorderRadiusStyleSheet(int radius);
    static QString getShadowStyleSheet(const QString& shadow);
    
    // Component Style Generators
    static QString getButtonStyleSheet(const QString& variant = "primary");
    static QString getCardStyleSheet();
    static QString getInputStyleSheet();
    static QString getTextStyleSheet(const QString& variant = "body-medium");

private:
    DesignSystem();
    static DesignSystem* s_instance;
};

#endif // DESIGNSYSTEM_H
