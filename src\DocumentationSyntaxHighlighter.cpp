#include "DocumentationSyntaxHighlighter.h"
#include <QColor>

DocumentationSyntaxHighlighter::DocumentationSyntaxHighlighter(QTextDocument* parent)
    : QSyntaxHighlighter(parent)
{
    setupFormats();
    setupRules();
}

void DocumentationSyntaxHighlighter::setupFormats()
{
    // Code block format (for <pre><code> blocks)
    codeBlockFormat.setBackground(QColor(248, 248, 248));
    codeBlockFormat.setForeground(QColor(51, 51, 51));
    codeBlockFormat.setFontFamily("Consolas, Monaco, monospace");

    // Inline code format (for <code> tags)
    inlineCodeFormat.setBackground(QColor(240, 240, 240));
    inlineCodeFormat.setForeground(QColor(199, 37, 78));
    inlineCodeFormat.setFontFamily("Consolas, Monaco, monospace");

    // Programming language syntax
    keywordFormat.setForeground(QColor(0, 0, 255));
    keywordFormat.setFontWeight(QFont::Bold);

    stringFormat.setForeground(QColor(163, 21, 21));

    commentFormat.setForeground(QColor(0, 128, 0));
    commentFormat.setFontItalic(true);

    numberFormat.setForeground(QColor(255, 140, 0));

    functionFormat.setForeground(QColor(128, 0, 128));
    functionFormat.setFontWeight(QFont::Bold);

    // Documentation formatting
    headingFormat.setForeground(QColor(0, 0, 139));
    headingFormat.setFontWeight(QFont::Bold);

    linkFormat.setForeground(QColor(0, 0, 238));
    linkFormat.setUnderlineStyle(QTextCharFormat::SingleUnderline);

    boldFormat.setFontWeight(QFont::Bold);

    italicFormat.setFontItalic(true);
}

void DocumentationSyntaxHighlighter::setupRules()
{
    HighlightingRule rule;

    // HTML headings
    rule.pattern = QRegularExpression("<h[1-6][^>]*>.*?</h[1-6]>");
    rule.format = headingFormat;
    highlightingRules.append(rule);

    // HTML links
    rule.pattern = QRegularExpression("<a[^>]*>.*?</a>");
    rule.format = linkFormat;
    highlightingRules.append(rule);

    // HTML bold
    rule.pattern = QRegularExpression("<(b|strong)[^>]*>.*?</(b|strong)>");
    rule.format = boldFormat;
    highlightingRules.append(rule);

    // HTML italic
    rule.pattern = QRegularExpression("<(i|em)[^>]*>.*?</(i|em)>");
    rule.format = italicFormat;
    highlightingRules.append(rule);

    // Inline code (single backticks or <code> tags)
    rule.pattern = QRegularExpression("`[^`]+`|<code[^>]*>.*?</code>");
    rule.format = inlineCodeFormat;
    highlightingRules.append(rule);

    // Programming keywords (common across languages)
    QStringList keywordPatterns;
    keywordPatterns << "\\bclass\\b" << "\\bfunction\\b" << "\\bdef\\b" << "\\breturn\\b"
                    << "\\bif\\b" << "\\belse\\b" << "\\belif\\b" << "\\bfor\\b" << "\\bwhile\\b"
                    << "\\btry\\b" << "\\bcatch\\b" << "\\bfinally\\b" << "\\bthrow\\b"
                    << "\\bpublic\\b" << "\\bprivate\\b" << "\\bprotected\\b" << "\\bstatic\\b"
                    << "\\bconst\\b" << "\\bvar\\b" << "\\blet\\b" << "\\bint\\b" << "\\bfloat\\b"
                    << "\\bdouble\\b" << "\\bstring\\b" << "\\bbool\\b" << "\\bvoid\\b"
                    << "\\bimport\\b" << "\\bfrom\\b" << "\\binclude\\b" << "\\busing\\b";

    foreach (const QString &pattern, keywordPatterns) {
        rule.pattern = QRegularExpression(pattern);
        rule.format = keywordFormat;
        highlightingRules.append(rule);
    }

    // String literals
    rule.pattern = QRegularExpression("\".*?\"|'.*?'");
    rule.format = stringFormat;
    highlightingRules.append(rule);

    // Numbers
    rule.pattern = QRegularExpression("\\b\\d+(\\.\\d+)?\\b");
    rule.format = numberFormat;
    highlightingRules.append(rule);

    // Single line comments
    rule.pattern = QRegularExpression("//[^\n]*|#[^\n]*");
    rule.format = commentFormat;
    highlightingRules.append(rule);

    // Function calls
    rule.pattern = QRegularExpression("\\b[A-Za-z_][A-Za-z0-9_]*(?=\\()");
    rule.format = functionFormat;
    highlightingRules.append(rule);
}

void DocumentationSyntaxHighlighter::highlightBlock(const QString& text)
{
    // Check if we're inside a code block
    bool inCodeBlock = false;
    QTextBlock currentBlock = this->currentBlock();
    QTextBlock previousBlock = currentBlock.previous();
    
    // Check previous blocks for <pre> or <code> tags
    while (previousBlock.isValid()) {
        QString prevText = previousBlock.text();
        if (prevText.contains("<pre>") || prevText.contains("<code>")) {
            inCodeBlock = true;
            break;
        }
        if (prevText.contains("</pre>") || prevText.contains("</code>")) {
            break;
        }
        previousBlock = previousBlock.previous();
    }
    
    // Check current block for code tags
    if (text.contains("<pre>") || text.contains("<code>")) {
        inCodeBlock = true;
    }
    if (text.contains("</pre>") || text.contains("</code>")) {
        inCodeBlock = false;
    }

    // Apply code block formatting if inside code block
    if (inCodeBlock) {
        setFormat(0, text.length(), codeBlockFormat);
        
        // Apply syntax highlighting within code blocks
        foreach (const HighlightingRule &rule, highlightingRules) {
            // Skip HTML-specific rules inside code blocks
            if (rule.format == headingFormat || rule.format == linkFormat || 
                rule.format == boldFormat || rule.format == italicFormat) {
                continue;
            }
            
            QRegularExpressionMatchIterator matchIterator = rule.pattern.globalMatch(text);
            while (matchIterator.hasNext()) {
                QRegularExpressionMatch match = matchIterator.next();
                setFormat(match.capturedStart(), match.capturedLength(), rule.format);
            }
        }
    } else {
        // Apply all highlighting rules for regular text
        foreach (const HighlightingRule &rule, highlightingRules) {
            QRegularExpressionMatchIterator matchIterator = rule.pattern.globalMatch(text);
            while (matchIterator.hasNext()) {
                QRegularExpressionMatch match = matchIterator.next();
                setFormat(match.capturedStart(), match.capturedLength(), rule.format);
            }
        }
    }

    // Handle multi-line comments
    QRegularExpression startExpression("/\\*");
    QRegularExpression endExpression("\\*/");

    setCurrentBlockState(0);

    QRegularExpressionMatch startMatch = startExpression.match(text);
    int startIndex = 0;
    if (previousBlockState() != 1)
        startIndex = startMatch.capturedStart();

    while (startIndex >= 0) {
        QRegularExpressionMatch endMatch = endExpression.match(text, startIndex);
        int endIndex = endMatch.capturedStart();
        int commentLength = 0;
        if (endIndex == -1) {
            setCurrentBlockState(1);
            commentLength = text.length() - startIndex;
        } else {
            commentLength = endIndex - startIndex + endMatch.capturedLength();
        }
        setFormat(startIndex, commentLength, commentFormat);
        startIndex = startExpression.match(text, startIndex + commentLength).capturedStart();
    }
}
