// WelcomeScreen.h
#ifndef WELCOMESCREEN_H
#define WELCOMESCREEN_H

#include "ElaIntegration.h"
#include <QWidget>
#include <QStringList>

class QVBoxLayout;
class QHBoxLayout;
class QGridLayout;
class ElaFlowLayout;
class ElaInteractiveCard;
class ElaPopularCard;
class ElaImageCard;
class ElaScrollArea;
class ElaText;
class ElaPushButton;
class ElaListView;
class QStandardItemModel;
class QStandardItem;

class WelcomeScreen : public ElaWidget
{
    Q_OBJECT

public:
    explicit WelcomeScreen(QWidget *parent = nullptr);
    ~WelcomeScreen();

    void updateRecentFiles(const QStringList& recentFiles);
    void setApplicationInfo(const QString& appName, const QString& version, const QString& description);

signals:
    void openFileRequested();
    void newDocumentRequested();
    void recentFileSelected(const QString& filePath);
    void settingsRequested();
    void aboutRequested();
    void helpRequested();

protected:
    void paintEvent(QPaintEvent* event) override;

private slots:
    void onQuickActionClicked();
    void onRecentFileClicked();
    void onGetStartedClicked();

private:
    void setupUI();
    void createHeader();
    void createQuickActions();
    void createRecentFiles();
    void createGettingStarted();
    void createFooter();
    void updateRecentFilesDisplay();

    // UI Components
    ElaScrollArea* m_scrollArea;
    QWidget* m_contentWidget;
    QVBoxLayout* m_mainLayout;
    
    // Header section
    QWidget* m_headerWidget;
    ElaText* m_titleLabel;
    ElaText* m_subtitleLabel;
    ElaText* m_versionLabel;
    ElaImageCard* m_logoCard;
    
    // Quick actions section
    QWidget* m_quickActionsWidget;
    ElaText* m_quickActionsTitle;
    ElaFlowLayout* m_quickActionsLayout;
    ElaInteractiveCard* m_openFileCard;
    ElaInteractiveCard* m_newDocumentCard;
    ElaInteractiveCard* m_settingsCard;
    
    // Recent files section
    QWidget* m_recentFilesWidget;
    ElaText* m_recentFilesTitle;
    ElaListView* m_recentFilesList;
    QStandardItemModel* m_recentFilesModel;
    ElaPushButton* m_clearRecentButton;
    
    // Getting started section
    QWidget* m_gettingStartedWidget;
    ElaText* m_gettingStartedTitle;
    ElaFlowLayout* m_gettingStartedLayout;
    ElaPopularCard* m_helpCard;
    ElaPopularCard* m_aboutCard;
    ElaPopularCard* m_shortcutsCard;
    
    // Footer section
    QWidget* m_footerWidget;
    ElaText* m_footerText;
    
    // Data
    QStringList m_recentFiles;
    QString m_applicationName;
    QString m_applicationVersion;
    QString m_applicationDescription;
};

#endif // WELCOMESCREEN_H
