#include "AnnotationClipboard.h"
#include "AnnotationManager.h"
#include "AnnotationCommand.h"
#include <QJsonDocument>
#include <QApplication>
#include <QClipboard>
#include <QMimeData>
#include <QDebug>

AnnotationClipboard* AnnotationClipboard::s_instance = nullptr;

AnnotationClipboard* AnnotationClipboard::instance()
{
    if (!s_instance) {
        s_instance = new AnnotationClipboard(qApp);
    }
    return s_instance;
}

AnnotationClipboard::AnnotationClipboard(QObject* parent)
    : QObject(parent)
{
}

void AnnotationClipboard::copyAnnotation(Annotation* annotation)
{
    if (!annotation) return;
    
    QList<Annotation*> annotations;
    annotations.append(annotation);
    copyAnnotations(annotations);
}

void AnnotationClipboard::copyAnnotations(const QList<Annotation*>& annotations)
{
    if (annotations.isEmpty()) return;
    
    m_clipboardData = QJsonArray();
    
    // Calculate center point for relative positioning
    QPointF totalCenter(0, 0);
    for (Annotation* annotation : annotations) {
        QRectF bounds = annotation->getBoundingRect();
        totalCenter += bounds.center();
    }
    m_originalCenterPoint = totalCenter / annotations.size();
    
    // Copy annotation data
    for (Annotation* annotation : annotations) {
        QJsonObject annotationData = annotation->toJson();
        m_clipboardData.append(annotationData);
    }
    
    // Also copy to system clipboard as JSON
    QJsonDocument doc(m_clipboardData);
    QClipboard* clipboard = QApplication::clipboard();
    
    QMimeData* mimeData = new QMimeData();
    mimeData->setData("application/x-pdf-annotations", doc.toJson());
    mimeData->setText(exportToText());
    clipboard->setMimeData(mimeData);
    
    emit clipboardChanged();
    
    qDebug() << "Copied" << annotations.size() << "annotations to clipboard";
}

void AnnotationClipboard::copySelectedAnnotations(AnnotationManager* manager)
{
    if (!manager) return;
    
    QList<Annotation*> selectedAnnotations = manager->getSelectedAnnotations();
    if (!selectedAnnotations.isEmpty()) {
        copyAnnotations(selectedAnnotations);
    }
}

bool AnnotationClipboard::canPaste() const
{
    if (!m_clipboardData.isEmpty()) {
        return true;
    }
    
    // Check system clipboard for annotation data
    QClipboard* clipboard = QApplication::clipboard();
    const QMimeData* mimeData = clipboard->mimeData();
    return mimeData && mimeData->hasFormat("application/x-pdf-annotations");
}

std::vector<std::unique_ptr<Annotation>> AnnotationClipboard::pasteAnnotations(int targetPage, const QPointF& pastePosition) const
{
    std::vector<std::unique_ptr<Annotation>> result;
    
    QJsonArray dataToUse = m_clipboardData;
    
    // If internal clipboard is empty, try system clipboard
    if (dataToUse.isEmpty()) {
        QClipboard* clipboard = QApplication::clipboard();
        const QMimeData* mimeData = clipboard->mimeData();
        if (mimeData && mimeData->hasFormat("application/x-pdf-annotations")) {
            QByteArray data = mimeData->data("application/x-pdf-annotations");
            QJsonDocument doc = QJsonDocument::fromJson(data);
            if (doc.isArray()) {
                dataToUse = doc.array();
            }
        }
    }
    
    if (dataToUse.isEmpty()) {
        return result;
    }
    
    // Convert JSON array to list for offset calculation
    QList<QJsonObject> annotationObjects;
    for (const QJsonValue& value : dataToUse) {
        if (value.isObject()) {
            annotationObjects.append(value.toObject());
        }
    }
    
    // Calculate paste offset
    QPointF offset = calculatePasteOffset(annotationObjects, pastePosition);
    
    // Create annotations with adjusted positions
    for (const QJsonObject& annotationData : annotationObjects) {
        QJsonObject adjustedData = adjustAnnotationPosition(annotationData, offset, targetPage);
        
        AnnotationType type = static_cast<AnnotationType>(adjustedData["type"].toInt());
        auto annotation = AnnotationFactory::createAnnotation(type);
        if (annotation) {
            annotation->fromJson(adjustedData);
            result.push_back(std::move(annotation));
        }
    }
    
    return result;
}

void AnnotationClipboard::pasteAnnotationsToManager(AnnotationManager* manager, int targetPage, const QPointF& pastePosition)
{
    if (!manager) return;
    
    auto annotations = pasteAnnotations(targetPage, pastePosition);
    for (auto& annotation : annotations) {
        manager->addAnnotation(std::move(annotation));
    }
    
    qDebug() << "Pasted" << annotations.size() << "annotations to page" << targetPage;
}

int AnnotationClipboard::getAnnotationCount() const
{
    return m_clipboardData.size();
}

QStringList AnnotationClipboard::getAnnotationTypes() const
{
    QStringList types;
    QSet<QString> uniqueTypes;
    
    for (const QJsonValue& value : m_clipboardData) {
        if (value.isObject()) {
            QJsonObject obj = value.toObject();
            AnnotationType type = static_cast<AnnotationType>(obj["type"].toInt());
            QString typeName = getAnnotationTypeName(type);
            if (!uniqueTypes.contains(typeName)) {
                uniqueTypes.insert(typeName);
                types.append(typeName);
            }
        }
    }
    
    return types;
}

bool AnnotationClipboard::isEmpty() const
{
    return m_clipboardData.isEmpty() && !canPaste();
}

void AnnotationClipboard::clear()
{
    m_clipboardData = QJsonArray();
    m_originalCenterPoint = QPointF();
    emit clipboardChanged();
}

QString AnnotationClipboard::exportToText() const
{
    if (m_clipboardData.isEmpty()) {
        return QString();
    }
    
    QStringList lines;
    lines.append(QString("PDF Annotations (%1 items)").arg(m_clipboardData.size()));
    lines.append(QString(50, '='));
    
    for (int i = 0; i < m_clipboardData.size(); ++i) {
        QJsonObject obj = m_clipboardData[i].toObject();
        AnnotationType type = static_cast<AnnotationType>(obj["type"].toInt());
        QString typeName = getAnnotationTypeName(type);
        
        lines.append(QString("%1. %2").arg(i + 1).arg(typeName));
        
        if (obj.contains("content") && !obj["content"].toString().isEmpty()) {
            lines.append(QString("   Content: %1").arg(obj["content"].toString()));
        }
        
        if (obj.contains("author") && !obj["author"].toString().isEmpty()) {
            lines.append(QString("   Author: %1").arg(obj["author"].toString()));
        }
        
        lines.append(QString("   Page: %1").arg(obj["page"].toInt() + 1));
        lines.append("");
    }
    
    return lines.join("\n");
}

QString AnnotationClipboard::exportToJson() const
{
    QJsonDocument doc(m_clipboardData);
    return doc.toJson();
}

bool AnnotationClipboard::importFromJson(const QString& jsonString)
{
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8(), &error);
    
    if (error.error != QJsonParseError::NoError) {
        qDebug() << "JSON parse error:" << error.errorString();
        return false;
    }
    
    if (!doc.isArray()) {
        qDebug() << "JSON document is not an array";
        return false;
    }
    
    m_clipboardData = doc.array();
    emit clipboardChanged();
    return true;
}

QPointF AnnotationClipboard::calculatePasteOffset(const QList<QJsonObject>& annotations, const QPointF& pastePosition) const
{
    if (annotations.isEmpty()) {
        return QPointF();
    }
    
    if (!pastePosition.isNull()) {
        // Paste at specific position - calculate offset from original center
        return pastePosition - m_originalCenterPoint;
    } else {
        // Default paste offset (slightly down and right)
        return QPointF(20, 20);
    }
}

QJsonObject AnnotationClipboard::adjustAnnotationPosition(const QJsonObject& annotation, const QPointF& offset, int newPage) const
{
    QJsonObject adjusted = annotation;
    
    // Update page number
    adjusted["page"] = newPage;
    
    // Adjust position based on annotation type
    AnnotationType type = static_cast<AnnotationType>(annotation["type"].toInt());
    
    switch (type) {
    case AnnotationType::Note:
        if (annotation.contains("position")) {
            QJsonObject pos = annotation["position"].toObject();
            pos["x"] = pos["x"].toDouble() + offset.x();
            pos["y"] = pos["y"].toDouble() + offset.y();
            adjusted["position"] = pos;
        }
        break;
        
    case AnnotationType::Highlight:
        if (annotation.contains("quads")) {
            QJsonArray quads = annotation["quads"].toArray();
            QJsonArray adjustedQuads;
            for (const QJsonValue& quadValue : quads) {
                QJsonObject quad = quadValue.toObject();
                quad["x"] = quad["x"].toDouble() + offset.x();
                quad["y"] = quad["y"].toDouble() + offset.y();
                adjustedQuads.append(quad);
            }
            adjusted["quads"] = adjustedQuads;
        }
        break;
        
    case AnnotationType::Drawing:
        if (annotation.contains("path")) {
            QJsonArray path = annotation["path"].toArray();
            QJsonArray adjustedPath;
            for (const QJsonValue& pointValue : path) {
                QJsonObject point = pointValue.toObject();
                point["x"] = point["x"].toDouble() + offset.x();
                point["y"] = point["y"].toDouble() + offset.y();
                adjustedPath.append(point);
            }
            adjusted["path"] = adjustedPath;
        }
        break;
        
    case AnnotationType::Rectangle:
    case AnnotationType::Circle:
    case AnnotationType::Arrow:
    case AnnotationType::Text:
        if (annotation.contains("bounds")) {
            QJsonObject bounds = annotation["bounds"].toObject();
            bounds["x"] = bounds["x"].toDouble() + offset.x();
            bounds["y"] = bounds["y"].toDouble() + offset.y();
            adjusted["bounds"] = bounds;
        }
        break;
    }
    
    return adjusted;
}
