/**
 * Paragraph link functionality for Doxygen Awesome CSS
 * Adds clickable anchor links to headings
 */

class DoxygenAwesomeParagraphLink {
    static linkIcon = `<svg xmlns="http://www.w3.org/2000/svg" height="16" viewBox="0 0 24 24" width="16"><path d="M0 0h24v24H0z" fill="none"/><path d="M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H6.9C3.58 7 1 9.58 1 12.9S3.58 18.8 6.9 18.8H11v-1.9H6.9c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm5-6h4.1c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1H13v1.9h4.1c3.31 0 5.9-2.68 5.9-5.9S20.42 7 17.1 7H13v2z"/></svg>`

    static init() {
        // Find all headings that should have anchor links
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        
        headings.forEach(heading => {
            DoxygenAwesomeParagraphLink.addAnchorLink(heading);
        });
    }

    static addAnchorLink(heading) {
        // Skip if heading already has an anchor link
        if (heading.querySelector('.anchorlink')) {
            return;
        }

        // Find or create an ID for the heading
        let headingId = heading.id;
        
        if (!headingId) {
            // Look for an anchor tag before the heading
            const prevElement = heading.previousElementSibling;
            if (prevElement && prevElement.tagName === 'A' && prevElement.name) {
                headingId = prevElement.name;
            } else {
                // Generate an ID from the heading text
                headingId = DoxygenAwesomeParagraphLink.generateId(heading.textContent);
            }
            heading.id = headingId;
        }

        // Create the anchor link
        const anchorLink = document.createElement('a');
        anchorLink.className = 'anchorlink';
        anchorLink.href = '#' + headingId;
        anchorLink.innerHTML = DoxygenAwesomeParagraphLink.linkIcon;
        anchorLink.title = 'Link to this section';
        anchorLink.setAttribute('aria-label', 'Link to this section');

        // Add click handler for smooth scrolling
        anchorLink.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update URL without triggering scroll
            if (history.pushState) {
                history.pushState(null, null, '#' + headingId);
            } else {
                location.hash = '#' + headingId;
            }
            
            // Smooth scroll to the heading
            heading.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
            
            // Focus the heading for accessibility
            heading.focus();
        });

        // Append the anchor link to the heading
        heading.appendChild(anchorLink);
    }

    static generateId(text) {
        // Convert text to a valid ID
        return text
            .toLowerCase()
            .replace(/[^\w\s-]/g, '') // Remove special characters
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/-+/g, '-') // Replace multiple hyphens with single
            .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
    }

    static handleDynamicContent() {
        // Handle dynamically added content
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Check if the added node contains headings
                            const headings = node.querySelectorAll ? 
                                node.querySelectorAll('h1, h2, h3, h4, h5, h6') : [];
                            
                            headings.forEach(heading => {
                                DoxygenAwesomeParagraphLink.addAnchorLink(heading);
                            });
                            
                            // Check if the added node itself is a heading
                            if (node.tagName && /^H[1-6]$/.test(node.tagName)) {
                                DoxygenAwesomeParagraphLink.addAnchorLink(node);
                            }
                        }
                    });
                }
            });
        });

        // Start observing
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    static addKeyboardSupport() {
        // Add keyboard navigation support
        document.addEventListener('keydown', function(e) {
            // Alt + L to focus on the nearest heading link
            if (e.altKey && e.key === 'l') {
                e.preventDefault();
                
                const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'));
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                
                // Find the heading closest to the current scroll position
                let closestHeading = null;
                let closestDistance = Infinity;
                
                headings.forEach(heading => {
                    const rect = heading.getBoundingClientRect();
                    const distance = Math.abs(rect.top);
                    
                    if (distance < closestDistance) {
                        closestDistance = distance;
                        closestHeading = heading;
                    }
                });
                
                if (closestHeading) {
                    const anchorLink = closestHeading.querySelector('.anchorlink');
                    if (anchorLink) {
                        anchorLink.focus();
                    }
                }
            }
        });
    }

    static addCopyLinkFeature() {
        // Add right-click context menu to copy link
        document.addEventListener('contextmenu', function(e) {
            const anchorLink = e.target.closest('.anchorlink');
            if (anchorLink) {
                e.preventDefault();
                
                // Copy the full URL with hash to clipboard
                const fullUrl = window.location.origin + window.location.pathname + anchorLink.getAttribute('href');
                
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(fullUrl).then(() => {
                        // Show a temporary tooltip
                        DoxygenAwesomeParagraphLink.showTooltip(anchorLink, 'Link copied!');
                    });
                } else {
                    // Fallback for older browsers
                    const textarea = document.createElement('textarea');
                    textarea.value = fullUrl;
                    textarea.style.position = 'fixed';
                    textarea.style.opacity = '0';
                    document.body.appendChild(textarea);
                    textarea.select();
                    
                    try {
                        document.execCommand('copy');
                        DoxygenAwesomeParagraphLink.showTooltip(anchorLink, 'Link copied!');
                    } catch (err) {
                        DoxygenAwesomeParagraphLink.showTooltip(anchorLink, 'Copy failed');
                    } finally {
                        document.body.removeChild(textarea);
                    }
                }
            }
        });
    }

    static showTooltip(element, message) {
        const tooltip = document.createElement('div');
        tooltip.textContent = message;
        tooltip.style.cssText = `
            position: absolute;
            background: var(--primary-color, #1779c4);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10000;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        document.body.appendChild(tooltip);
        
        // Position the tooltip
        const rect = element.getBoundingClientRect();
        tooltip.style.left = (rect.left + rect.width / 2 - tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = (rect.top - tooltip.offsetHeight - 5) + 'px';
        
        // Show and hide the tooltip
        setTimeout(() => {
            tooltip.style.opacity = '1';
        }, 10);
        
        setTimeout(() => {
            tooltip.style.opacity = '0';
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 300);
        }, 2000);
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    DoxygenAwesomeParagraphLink.init();
    DoxygenAwesomeParagraphLink.handleDynamicContent();
    DoxygenAwesomeParagraphLink.addKeyboardSupport();
    DoxygenAwesomeParagraphLink.addCopyLinkFeature();
});

// Handle hash changes for smooth scrolling
window.addEventListener('hashchange', function() {
    const hash = window.location.hash;
    if (hash) {
        const target = document.querySelector(hash);
        if (target) {
            setTimeout(() => {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 100);
        }
    }
});

// Export for use in other scripts
window.DoxygenAwesomeParagraphLink = DoxygenAwesomeParagraphLink;
