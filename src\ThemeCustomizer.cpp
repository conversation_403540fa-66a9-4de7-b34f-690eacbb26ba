#include "ThemeCustomizer.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QScrollArea>
#include <QPainter>

ThemeCustomizer::ThemeCustomizer(QWidget *parent)
    : ElaContentDialog(parent)
    , m_tabWidget(nullptr)
    , m_colorTab(nullptr)
    , m_accentColorButton(nullptr)
    , m_accentColorLabel(nullptr)
    , m_colorPreview(nullptr)
    , m_effectsTab(nullptr)
    , m_transparencySlider(nullptr)
    , m_transparencyLabel(nullptr)
    , m_blurEffectCheck(nullptr)
    , m_shadowEffectCheck(nullptr)
    , m_animationsCheck(nullptr)
    , m_presetsTab(nullptr)
    , m_presetCombo(nullptr)
    , m_presetPreview(nullptr)
    , m_previewWidget(nullptr)
    , m_previewTitle(nullptr)
    , m_previewButton(nullptr)
    , m_previewProgress(nullptr)
    , m_currentAccentColor(0, 103, 192) // Default blue
    , m_currentTransparency(0)
    , m_currentBlurEffect(false)
    , m_currentShadowEffect(true)
    , m_currentAnimations(true)
{
    setupUI();
    loadCurrentTheme();
}

ThemeCustomizer::~ThemeCustomizer()
{
}

void ThemeCustomizer::setupUI()
{
    setWindowTitle(tr("Theme Customization"));
    setFixedSize(600, 500);
    
    // Create main layout
    QVBoxLayout* mainLayout = new QVBoxLayout();
    
    // Create tab widget
    m_tabWidget = new ElaTab(this);
    
    // Create tabs
    createColorSection();
    createEffectsSection();
    createPresetsSection();
    
    m_tabWidget->addTab(m_colorTab, tr("Colors"));
    m_tabWidget->addTab(m_effectsTab, tr("Effects"));
    m_tabWidget->addTab(m_presetsTab, tr("Presets"));
    
    mainLayout->addWidget(m_tabWidget);
    
    // Create preview section
    QGroupBox* previewGroup = new QGroupBox(tr("Preview"));
    QHBoxLayout* previewLayout = new QHBoxLayout(previewGroup);
    
    m_previewWidget = new QWidget();
    m_previewWidget->setFixedSize(PREVIEW_WIDTH, PREVIEW_HEIGHT);
    m_previewWidget->setStyleSheet("background-color: white; border: 1px solid #ccc; border-radius: 8px;");
    
    // Add preview content
    QVBoxLayout* previewContentLayout = new QVBoxLayout(m_previewWidget);
    m_previewTitle = new ElaText(tr("Preview Window"), m_previewWidget);
    m_previewTitle->setTextPixelSize(16);
    QFont titleFont = m_previewTitle->font();
    titleFont.setBold(true);
    m_previewTitle->setFont(titleFont);
    
    m_previewButton = new ElaPushButton(tr("Sample Button"), m_previewWidget);
    m_previewProgress = new ElaProgress(m_previewWidget);
    m_previewProgress->setValue(65);
    
    previewContentLayout->addWidget(m_previewTitle);
    previewContentLayout->addWidget(m_previewButton);
    previewContentLayout->addWidget(m_previewProgress);
    previewContentLayout->addStretch();
    
    previewLayout->addWidget(m_previewWidget);
    previewLayout->addStretch();
    
    mainLayout->addWidget(previewGroup);
    
    // Set main layout
    QWidget* centralWidget = new QWidget();
    centralWidget->setLayout(mainLayout);
    setCentralWidget(centralWidget);
    
    // Initialize presets
    m_presets = {
        {tr("Office Blue"), QColor(0, 103, 192), 0, false, tr("Classic Microsoft Office blue theme")},
        {tr("Modern Green"), QColor(16, 124, 16), 5, true, tr("Fresh green theme with subtle transparency")},
        {tr("Elegant Purple"), QColor(102, 45, 145), 10, true, tr("Sophisticated purple theme")},
        {tr("Warm Orange"), QColor(255, 140, 0), 0, false, tr("Energetic orange theme")},
        {tr("Cool Teal"), QColor(0, 150, 136), 8, true, tr("Calming teal theme with blur effects")}
    };
    
    // Connect signals
    connect(m_accentColorButton, &ElaPushButton::clicked, this, &ThemeCustomizer::onAccentColorClicked);
    
    updatePreview();
}

void ThemeCustomizer::createColorSection()
{
    m_colorTab = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(m_colorTab);
    
    // Accent color selection
    QGroupBox* accentGroup = new QGroupBox(tr("Accent Color"));
    QHBoxLayout* accentLayout = new QHBoxLayout(accentGroup);
    
    m_accentColorLabel = new ElaText(tr("Choose your accent color:"));
    m_accentColorButton = new ElaPushButton(tr("Select Color"));
    m_accentColorButton->setFixedSize(COLOR_BUTTON_SIZE, COLOR_BUTTON_SIZE);
    
    m_colorPreview = new QWidget();
    m_colorPreview->setFixedSize(100, 60);
    m_colorPreview->setStyleSheet("border: 1px solid #ccc; border-radius: 4px;");
    
    accentLayout->addWidget(m_accentColorLabel);
    accentLayout->addWidget(m_accentColorButton);
    accentLayout->addWidget(m_colorPreview);
    accentLayout->addStretch();
    
    layout->addWidget(accentGroup);
    layout->addStretch();
    
    updateAccentColorButton();
}

void ThemeCustomizer::createEffectsSection()
{
    m_effectsTab = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(m_effectsTab);
    
    // Transparency
    QGroupBox* transparencyGroup = new QGroupBox(tr("Window Transparency"));
    QVBoxLayout* transparencyLayout = new QVBoxLayout(transparencyGroup);
    
    m_transparencyLabel = new ElaText(tr("Transparency: 0%"));
    m_transparencySlider = new ElaSliderWidget(Qt::Horizontal);
    m_transparencySlider->setRange(0, 20);
    m_transparencySlider->setValue(m_currentTransparency);
    
    connect(m_transparencySlider, &ElaSliderWidget::valueChanged, this, &ThemeCustomizer::onTransparencyChanged);
    
    transparencyLayout->addWidget(m_transparencyLabel);
    transparencyLayout->addWidget(m_transparencySlider);
    
    // Effects checkboxes
    QGroupBox* effectsGroup = new QGroupBox(tr("Visual Effects"));
    QVBoxLayout* effectsLayout = new QVBoxLayout(effectsGroup);
    
    m_blurEffectCheck = new ElaCheck(tr("Blur background effects"));
    m_blurEffectCheck->setChecked(m_currentBlurEffect);
    connect(m_blurEffectCheck, &ElaCheck::toggled, this, &ThemeCustomizer::onBlurToggled);
    
    m_shadowEffectCheck = new ElaCheck(tr("Drop shadow effects"));
    m_shadowEffectCheck->setChecked(m_currentShadowEffect);
    
    m_animationsCheck = new ElaCheck(tr("Smooth animations"));
    m_animationsCheck->setChecked(m_currentAnimations);
    
    effectsLayout->addWidget(m_blurEffectCheck);
    effectsLayout->addWidget(m_shadowEffectCheck);
    effectsLayout->addWidget(m_animationsCheck);
    
    layout->addWidget(transparencyGroup);
    layout->addWidget(effectsGroup);
    layout->addStretch();
}

void ThemeCustomizer::createPresetsSection()
{
    m_presetsTab = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(m_presetsTab);
    
    // Preset selection
    QGroupBox* presetGroup = new QGroupBox(tr("Theme Presets"));
    QVBoxLayout* presetLayout = new QVBoxLayout(presetGroup);
    
    m_presetCombo = new ElaCombo();
    for (const auto& preset : m_presets) {
        m_presetCombo->addItem(preset.name);
    }
    
    connect(m_presetCombo, QOverload<int>::of(&ElaCombo::currentIndexChanged), 
            this, &ThemeCustomizer::onPresetSelected);
    
    presetLayout->addWidget(new ElaText(tr("Select a preset theme:")));
    presetLayout->addWidget(m_presetCombo);
    
    // Preset preview
    m_presetPreview = new QScrollArea();
    m_presetPreview->setFixedHeight(150);
    m_presetPreview->setWidgetResizable(true);
    
    presetLayout->addWidget(m_presetPreview);
    
    layout->addWidget(presetGroup);
    layout->addStretch();
}

void ThemeCustomizer::loadCurrentTheme()
{
    // Load current theme settings from ElaTheme
    // This would integrate with the actual theme system
    updatePreview();
}

void ThemeCustomizer::updateAccentColorButton()
{
    QString colorStyle = QString(
        "background-color: %1; border: 2px solid #ccc; border-radius: 4px;"
    ).arg(m_currentAccentColor.name());
    
    m_accentColorButton->setStyleSheet(colorStyle);
    
    if (m_colorPreview) {
        m_colorPreview->setStyleSheet(QString(
            "background-color: %1; border: 1px solid #ccc; border-radius: 4px;"
        ).arg(m_currentAccentColor.name()));
    }
}

void ThemeCustomizer::updatePreview()
{
    if (!m_previewWidget) return;
    
    // Update preview widget with current settings
    QString previewStyle = QString(
        "background-color: rgba(255, 255, 255, %1); "
        "border: 1px solid #ccc; border-radius: 8px;"
    ).arg(255 - m_currentTransparency * 10);
    
    m_previewWidget->setStyleSheet(previewStyle);
    
    // Update preview button with accent color
    if (m_previewButton) {
        QString buttonStyle = QString(
            "background-color: %1; color: white; border-radius: 4px; padding: 8px;"
        ).arg(m_currentAccentColor.name());
        m_previewButton->setStyleSheet(buttonStyle);
    }
}

void ThemeCustomizer::onAccentColorClicked()
{
    QColorDialog colorDialog(m_currentAccentColor, this);
    if (colorDialog.exec() == QDialog::Accepted) {
        m_currentAccentColor = colorDialog.selectedColor();
        updateAccentColorButton();
        updatePreview();
        emit accentColorChanged(m_currentAccentColor);
    }
}

void ThemeCustomizer::onTransparencyChanged(int value)
{
    m_currentTransparency = value;
    m_transparencyLabel->setText(tr("Transparency: %1%").arg(value * 5));
    updatePreview();
    emit transparencyChanged(value);
}

void ThemeCustomizer::onBlurToggled(bool enabled)
{
    m_currentBlurEffect = enabled;
    updatePreview();
}

void ThemeCustomizer::onPresetSelected(int index)
{
    if (index >= 0 && index < m_presets.size()) {
        const ThemePreset& preset = m_presets[index];
        
        m_currentAccentColor = preset.accentColor;
        m_currentTransparency = preset.transparency;
        m_currentBlurEffect = preset.blurEffect;
        
        // Update UI controls
        updateAccentColorButton();
        m_transparencySlider->setValue(m_currentTransparency);
        m_blurEffectCheck->setChecked(m_currentBlurEffect);
        
        updatePreview();
    }
}

void ThemeCustomizer::setAccentColor(const QColor& color)
{
    m_currentAccentColor = color;
    updateAccentColorButton();
    updatePreview();
}

QColor ThemeCustomizer::getAccentColor() const
{
    return m_currentAccentColor;
}

void ThemeCustomizer::setWindowTransparency(int transparency)
{
    m_currentTransparency = transparency;
    if (m_transparencySlider) {
        m_transparencySlider->setValue(transparency);
    }
    updatePreview();
}

int ThemeCustomizer::getWindowTransparency() const
{
    return m_currentTransparency;
}

void ThemeCustomizer::setBlurEffect(bool enabled)
{
    m_currentBlurEffect = enabled;
    if (m_blurEffectCheck) {
        m_blurEffectCheck->setChecked(enabled);
    }
    updatePreview();
}

bool ThemeCustomizer::getBlurEffect() const
{
    return m_currentBlurEffect;
}

void ThemeCustomizer::onApplyClicked()
{
    // Apply the current theme settings
    emit themeChanged();
    accept();
}

void ThemeCustomizer::onResetClicked()
{
    // Reset to default theme settings
    resetToDefaults();
    updatePreview();
}

void ThemeCustomizer::resetToDefaults()
{
    // Reset to default values
    m_currentAccentColor = QColor(0, 120, 212); // Default blue
    m_currentTransparency = 0;
    m_currentBlurEffect = false;

    // Update UI controls
    updateAccentColorButton();
    if (m_transparencySlider) {
        m_transparencySlider->setValue(m_currentTransparency);
    }
    if (m_blurEffectCheck) {
        m_blurEffectCheck->setChecked(m_currentBlurEffect);
    }
}
