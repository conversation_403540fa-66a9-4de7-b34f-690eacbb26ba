#ifndef ANNOTATIONTOOLBAR_H
#define ANNOTATIONTOOLBAR_H

#include <QWidget>
#include <QToolBar>
#include <QButtonGroup>
#include <QColor>
#include "Annotation.h"

class QToolButton;
class QAction;
class QColorDialog;
class QSpinBox;
class QDoubleSpinBox;
class QComboBox;
class QSlider;
class QLabel;

enum class AnnotationTool {
    None,
    Select,
    Highlight,
    Note,
    Drawing,
    Rectangle,
    Circle,
    Arrow,
    Text
};

class AnnotationToolbar : public QWidget
{
    Q_OBJECT

public:
    explicit AnnotationToolbar(QWidget *parent = nullptr);
    ~AnnotationToolbar();

    // Tool selection
    AnnotationTool getCurrentTool() const { return m_currentTool; }
    void setCurrentTool(AnnotationTool tool);
    
    // Tool properties
    QColor getCurrentColor() const { return m_currentColor; }
    void setCurrentColor(const QColor& color);
    qreal getCurrentOpacity() const { return m_currentOpacity; }
    void setCurrentOpacity(qreal opacity);
    qreal getCurrentLineWidth() const { return m_currentLineWidth; }
    void setCurrentLineWidth(qreal width);
    
    // Enable/disable tools
    void setToolsEnabled(bool enabled);
    void setAnnotationToolsEnabled(bool enabled);

signals:
    void toolChanged(AnnotationTool tool);
    void colorChanged(const QColor& color);
    void opacityChanged(qreal opacity);
    void lineWidthChanged(qreal width);
    void deleteSelectedAnnotations();
    void copySelectedAnnotations();
    void pasteAnnotations();

private slots:
    void onToolButtonClicked();
    void onColorButtonClicked();
    void onOpacityChanged(int value);
    void onLineWidthChanged(double value);
    void onDeleteClicked();
    void onCopyClicked();
    void onPasteClicked();

private:
    void createToolButtons();
    void createPropertyControls();
    void updateColorButton();
    void updateToolSelection();
    void setupKeyboardShortcuts();

    // Tool buttons
    QToolButton* m_selectButton;
    QToolButton* m_highlightButton;
    QToolButton* m_noteButton;
    QToolButton* m_drawingButton;
    QToolButton* m_rectangleButton;
    QToolButton* m_circleButton;
    QToolButton* m_arrowButton;
    QToolButton* m_textButton;
    
    // Property controls
    QToolButton* m_colorButton;
    QSlider* m_opacitySlider;
    QDoubleSpinBox* m_lineWidthSpinBox;
    QLabel* m_opacityLabel;
    QLabel* m_lineWidthLabel;
    
    // Action buttons
    QToolButton* m_deleteButton;
    QToolButton* m_copyButton;
    QToolButton* m_pasteButton;
    
    // Button group for exclusive selection
    QButtonGroup* m_toolButtonGroup;
    
    // Current state
    AnnotationTool m_currentTool = AnnotationTool::Select;
    QColor m_currentColor = Qt::yellow;
    qreal m_currentOpacity = 0.5;
    qreal m_currentLineWidth = 2.0;
    
    // Color dialog
    QColorDialog* m_colorDialog = nullptr;
};

#endif // ANNOTATIONTOOLBAR_H
