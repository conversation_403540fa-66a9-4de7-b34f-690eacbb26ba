#include "ModernContextMenu.h"
#include "ElaIntegration.h"
#include <QPainter>
#include <QPainterPath>
#include <QGraphicsDropShadowEffect>

ModernContextMenu::ModernContextMenu(QWidget *parent)
    : ElaMenu(parent)
    , m_themeMode(eTheme->getThemeMode())
{
    setupStyling();
    
    // Connect to theme changes
    connect(eTheme, &ElaTheme::themeModeChanged, this, &ModernContextMenu::setMenuTheme);
}

ModernContextMenu::~ModernContextMenu()
{
}

void ModernContextMenu::setupStyling()
{
    // Set window flags for modern appearance
    setWindowFlags(Qt::Popup | Qt::FramelessWindowHint | Qt::NoDropShadowWindowHint);
    setAttribute(Qt::WA_TranslucentBackground);
    
    // Add drop shadow effect
    QGraphicsDropShadowEffect* shadowEffect = new QGraphicsDropShadowEffect(this);
    shadowEffect->setBlurRadius(15);
    shadowEffect->setColor(QColor(0, 0, 0, 80));
    shadowEffect->setOffset(0, 3);
    setGraphicsEffect(shadowEffect);
    
    updateTheme();
}

void ModernContextMenu::updateTheme()
{
    QString styleSheet;
    
    if (m_themeMode == ElaThemeType::Light) {
        styleSheet = QString(
            "ElaMenu {"
            "    background-color: rgba(255, 255, 255, 245);"
            "    border: 1px solid rgba(0, 0, 0, 0.1);"
            "    border-radius: %1px;"
            "    padding: 4px;"
            "}"
            "ElaMenu::item {"
            "    background-color: transparent;"
            "    color: #333333;"
            "    padding: 6px 12px 6px 32px;"
            "    border-radius: 4px;"
            "    margin: 1px;"
            "    min-height: %2px;"
            "}"
            "ElaMenu::item:selected {"
            "    background-color: rgba(0, 103, 192, 0.1);"
            "    color: #0067C0;"
            "}"
            "ElaMenu::item:disabled {"
            "    color: #999999;"
            "}"
            "ElaMenu::separator {"
            "    height: 1px;"
            "    background-color: rgba(0, 0, 0, 0.1);"
            "    margin: 4px 8px;"
            "}"
        ).arg(MENU_RADIUS).arg(ITEM_HEIGHT);
    } else {
        styleSheet = QString(
            "ElaMenu {"
            "    background-color: rgba(45, 45, 45, 245);"
            "    border: 1px solid rgba(255, 255, 255, 0.1);"
            "    border-radius: %1px;"
            "    padding: 4px;"
            "}"
            "ElaMenu::item {"
            "    background-color: transparent;"
            "    color: #FFFFFF;"
            "    padding: 6px 12px 6px 32px;"
            "    border-radius: 4px;"
            "    margin: 1px;"
            "    min-height: %2px;"
            "}"
            "ElaMenu::item:selected {"
            "    background-color: rgba(76, 194, 255, 0.2);"
            "    color: #4CC2FF;"
            "}"
            "ElaMenu::item:disabled {"
            "    color: #666666;"
            "}"
            "ElaMenu::separator {"
            "    height: 1px;"
            "    background-color: rgba(255, 255, 255, 0.1);"
            "    margin: 4px 8px;"
            "}"
        ).arg(MENU_RADIUS).arg(ITEM_HEIGHT);
    }
    
    setStyleSheet(styleSheet);
}

QAction* ModernContextMenu::addActionWithIcon(ElaIconType::IconName icon, const QString& text, 
                                            const QKeySequence& shortcut, const QString& tooltip)
{
    QAction* action = new QAction(text, this);
    action->setIcon(ElaIcon::getInstance()->getElaIcon(icon, ICON_SIZE));
    
    if (!shortcut.isEmpty()) {
        action->setShortcut(shortcut);
        // Add shortcut text to the action text
        QString displayText = text;
        if (!shortcut.toString().isEmpty()) {
            displayText += QString("\t%1").arg(shortcut.toString(QKeySequence::NativeText));
        }
        action->setText(displayText);
    }
    
    if (!tooltip.isEmpty()) {
        action->setToolTip(tooltip);
        action->setStatusTip(tooltip);
    }
    
    addAction(action);
    return action;
}

QAction* ModernContextMenu::addActionWithIcon(ElaIconType::IconName icon, const QString& text, 
                                            const QString& shortcut, const QString& tooltip)
{
    QKeySequence keySequence;
    if (!shortcut.isEmpty()) {
        keySequence = QKeySequence::fromString(shortcut);
    }
    return addActionWithIcon(icon, text, keySequence, tooltip);
}

void ModernContextMenu::addStyledSeparator()
{
    addSeparator();
}

ModernContextMenu* ModernContextMenu::addSubMenu(ElaIconType::IconName icon, const QString& title)
{
    ModernContextMenu* subMenu = new ModernContextMenu(this);
    subMenu->setTitle(title);
    
    QAction* subMenuAction = addMenu(subMenu);
    subMenuAction->setIcon(ElaIcon::getInstance()->getElaIcon(icon, ICON_SIZE));
    
    return subMenu;
}

void ModernContextMenu::setMenuTheme(ElaThemeType::ThemeMode themeMode)
{
    m_themeMode = themeMode;
    updateTheme();
}

void ModernContextMenu::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Draw rounded background
    QPainterPath path;
    path.addRoundedRect(rect().adjusted(1, 1, -1, -1), MENU_RADIUS, MENU_RADIUS);
    
    QColor backgroundColor;
    QColor borderColor;
    
    if (m_themeMode == ElaThemeType::Light) {
        backgroundColor = QColor(255, 255, 255, 245);
        borderColor = QColor(0, 0, 0, 25);
    } else {
        backgroundColor = QColor(45, 45, 45, 245);
        borderColor = QColor(255, 255, 255, 25);
    }
    
    painter.fillPath(path, backgroundColor);
    painter.setPen(QPen(borderColor, 1));
    painter.drawPath(path);
    
    // Call parent paint event for menu items
    ElaMenu::paintEvent(event);
}

void ModernContextMenu::showEvent(QShowEvent *event)
{
    updateTheme();
    ElaMenu::showEvent(event);
}
