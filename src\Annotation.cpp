#include "Annotation.h"
#include <QPainter>
#include <QJsonDocument>
#include <QJsonArray>
#include <QUuid>
#include <QDebug>
#include <QFont>
#include <cmath>

// Base Annotation class
Annotation::Annotation(AnnotationType type, QObject *parent)
    : QObject(parent)
    , m_id(QUuid::createUuid().toString())
    , m_type(type)
    , m_creationDate(QDateTime::currentDateTime())
    , m_modificationDate(QDateTime::currentDateTime())
{
}

QJsonObject Annotation::toJson() const
{
    QJsonObject json;
    json["id"] = m_id;
    json["type"] = static_cast<int>(m_type);
    json["pageNumber"] = m_pageNumber;
    json["color"] = m_color.name();
    json["opacity"] = m_opacity;
    json["lineWidth"] = m_lineWidth;
    json["content"] = m_content;
    json["author"] = m_author;
    json["creationDate"] = m_creationDate.toString(Qt::ISODate);
    json["modificationDate"] = m_modificationDate.toString(Qt::ISODate);
    json["selected"] = m_selected;
    json["editable"] = m_editable;
    return json;
}

void Annotation::fromJson(const QJsonObject& json)
{
    m_id = json["id"].toString();
    m_type = static_cast<AnnotationType>(json["type"].toInt());
    m_pageNumber = json["pageNumber"].toInt();
    m_color = QColor(json["color"].toString());
    m_opacity = json["opacity"].toDouble();
    m_lineWidth = json["lineWidth"].toDouble();
    m_content = json["content"].toString();
    m_author = json["author"].toString();
    m_creationDate = QDateTime::fromString(json["creationDate"].toString(), Qt::ISODate);
    m_modificationDate = QDateTime::fromString(json["modificationDate"].toString(), Qt::ISODate);
    m_selected = json["selected"].toBool();
    m_editable = json["editable"].toBool();
}

// HighlightAnnotation implementation
HighlightAnnotation::HighlightAnnotation(QObject *parent)
    : Annotation(AnnotationType::Highlight, parent)
{
    m_color = Qt::yellow;
    m_opacity = 0.3;
}

QRectF HighlightAnnotation::getBoundingRect() const
{
    if (m_quads.isEmpty()) return QRectF();
    
    QRectF bounds = m_quads.first();
    for (const QRectF& quad : m_quads) {
        bounds = bounds.united(quad);
    }
    return bounds;
}

bool HighlightAnnotation::contains(const QPointF& point) const
{
    for (const QRectF& quad : m_quads) {
        if (quad.contains(point)) {
            return true;
        }
    }
    return false;
}

void HighlightAnnotation::move(const QPointF& offset)
{
    for (QRectF& quad : m_quads) {
        quad.translate(offset);
    }
    emit annotationChanged();
}

void HighlightAnnotation::resize(const QRectF& newBounds)
{
    if (m_quads.isEmpty()) return;
    
    QRectF currentBounds = getBoundingRect();
    if (currentBounds.isEmpty()) return;
    
    double scaleX = newBounds.width() / currentBounds.width();
    double scaleY = newBounds.height() / currentBounds.height();
    
    for (QRectF& quad : m_quads) {
        quad.translate(-currentBounds.topLeft());
        quad = QRectF(quad.x() * scaleX, quad.y() * scaleY, 
                     quad.width() * scaleX, quad.height() * scaleY);
        quad.translate(newBounds.topLeft());
    }
    emit annotationChanged();
}

void HighlightAnnotation::render(QPainter* painter, double dpi, double zoomFactor) const
{
    if (!painter || m_quads.isEmpty()) return;
    
    painter->save();
    
    // Set up highlight brush
    QColor highlightColor = m_color;
    highlightColor.setAlphaF(m_opacity);
    painter->setBrush(QBrush(highlightColor));
    painter->setPen(Qt::NoPen);
    
    // Convert PDF coordinates to pixel coordinates
    const double pointsToPixels = (dpi * zoomFactor) / 72.0;
    
    for (const QRectF& quad : m_quads) {
        QRectF pixelRect = QRectF(
            quad.x() * pointsToPixels,
            quad.y() * pointsToPixels,
            quad.width() * pointsToPixels,
            quad.height() * pointsToPixels
        );
        painter->drawRect(pixelRect);
    }
    
    // Draw selection handles if selected
    if (m_selected) {
        painter->setBrush(Qt::blue);
        painter->setPen(QPen(Qt::blue, 1));
        QRectF bounds = getBoundingRect();
        QRectF pixelBounds = QRectF(
            bounds.x() * pointsToPixels,
            bounds.y() * pointsToPixels,
            bounds.width() * pointsToPixels,
            bounds.height() * pointsToPixels
        );
        
        // Draw corner handles
        const double handleSize = 6;
        painter->drawRect(pixelBounds.topLeft().x() - handleSize/2, 
                         pixelBounds.topLeft().y() - handleSize/2, 
                         handleSize, handleSize);
        painter->drawRect(pixelBounds.topRight().x() - handleSize/2, 
                         pixelBounds.topRight().y() - handleSize/2, 
                         handleSize, handleSize);
        painter->drawRect(pixelBounds.bottomLeft().x() - handleSize/2, 
                         pixelBounds.bottomLeft().y() - handleSize/2, 
                         handleSize, handleSize);
        painter->drawRect(pixelBounds.bottomRight().x() - handleSize/2, 
                         pixelBounds.bottomRight().y() - handleSize/2, 
                         handleSize, handleSize);
    }
    
    painter->restore();
}

QJsonObject HighlightAnnotation::toJson() const
{
    QJsonObject json = Annotation::toJson();
    
    QJsonArray quadsArray;
    for (const QRectF& quad : m_quads) {
        QJsonObject quadObj;
        quadObj["x"] = quad.x();
        quadObj["y"] = quad.y();
        quadObj["width"] = quad.width();
        quadObj["height"] = quad.height();
        quadsArray.append(quadObj);
    }
    json["quads"] = quadsArray;
    
    return json;
}

void HighlightAnnotation::fromJson(const QJsonObject& json)
{
    Annotation::fromJson(json);
    
    m_quads.clear();
    QJsonArray quadsArray = json["quads"].toArray();
    for (const QJsonValue& value : quadsArray) {
        QJsonObject quadObj = value.toObject();
        QRectF quad(quadObj["x"].toDouble(), quadObj["y"].toDouble(),
                   quadObj["width"].toDouble(), quadObj["height"].toDouble());
        m_quads.append(quad);
    }
}

// NoteAnnotation implementation
NoteAnnotation::NoteAnnotation(QObject *parent)
    : Annotation(AnnotationType::Note, parent)
{
    m_color = Qt::yellow;
    m_opacity = 1.0;
}

QRectF NoteAnnotation::getBoundingRect() const
{
    return QRectF(m_position, m_size);
}

bool NoteAnnotation::contains(const QPointF& point) const
{
    return getBoundingRect().contains(point);
}

void NoteAnnotation::move(const QPointF& offset)
{
    m_position += offset;
    emit annotationChanged();
}

void NoteAnnotation::resize(const QRectF& newBounds)
{
    m_position = newBounds.topLeft();
    m_size = newBounds.size();
    emit annotationChanged();
}

void NoteAnnotation::render(QPainter* painter, double dpi, double zoomFactor) const
{
    if (!painter) return;
    
    painter->save();
    
    // Convert PDF coordinates to pixel coordinates
    const double pointsToPixels = (dpi * zoomFactor) / 72.0;
    
    QRectF pixelRect = QRectF(
        m_position.x() * pointsToPixels,
        m_position.y() * pointsToPixels,
        m_size.width() * pointsToPixels,
        m_size.height() * pointsToPixels
    );
    
    // Draw note icon
    QColor noteColor = m_color;
    noteColor.setAlphaF(m_opacity);
    painter->setBrush(QBrush(noteColor));
    painter->setPen(QPen(Qt::black, 1));
    painter->drawRect(pixelRect);
    
    // Draw note icon details
    painter->setPen(QPen(Qt::black, 1));
    double margin = pixelRect.width() * 0.1;
    painter->drawLine(pixelRect.left() + margin, pixelRect.top() + margin * 2,
                     pixelRect.right() - margin, pixelRect.top() + margin * 2);
    painter->drawLine(pixelRect.left() + margin, pixelRect.top() + margin * 4,
                     pixelRect.right() - margin, pixelRect.top() + margin * 4);
    painter->drawLine(pixelRect.left() + margin, pixelRect.top() + margin * 6,
                     pixelRect.right() - margin * 3, pixelRect.top() + margin * 6);
    
    // Draw selection handles if selected
    if (m_selected) {
        painter->setBrush(Qt::blue);
        painter->setPen(QPen(Qt::blue, 1));
        
        const double handleSize = 6;
        painter->drawRect(pixelRect.topLeft().x() - handleSize/2, 
                         pixelRect.topLeft().y() - handleSize/2, 
                         handleSize, handleSize);
        painter->drawRect(pixelRect.topRight().x() - handleSize/2, 
                         pixelRect.topRight().y() - handleSize/2, 
                         handleSize, handleSize);
        painter->drawRect(pixelRect.bottomLeft().x() - handleSize/2, 
                         pixelRect.bottomLeft().y() - handleSize/2, 
                         handleSize, handleSize);
        painter->drawRect(pixelRect.bottomRight().x() - handleSize/2, 
                         pixelRect.bottomRight().y() - handleSize/2, 
                         handleSize, handleSize);
    }
    
    painter->restore();
}

QJsonObject NoteAnnotation::toJson() const
{
    QJsonObject json = Annotation::toJson();
    json["x"] = m_position.x();
    json["y"] = m_position.y();
    json["width"] = m_size.width();
    json["height"] = m_size.height();
    return json;
}

void NoteAnnotation::fromJson(const QJsonObject& json)
{
    Annotation::fromJson(json);
    m_position = QPointF(json["x"].toDouble(), json["y"].toDouble());
    m_size = QSizeF(json["width"].toDouble(), json["height"].toDouble());
}

// DrawingAnnotation implementation
DrawingAnnotation::DrawingAnnotation(QObject *parent)
    : Annotation(AnnotationType::Drawing, parent)
{
    m_color = Qt::red;
    m_opacity = 1.0;
    m_lineWidth = 2.0;
}

QRectF DrawingAnnotation::getBoundingRect() const
{
    return m_boundingRect;
}

bool DrawingAnnotation::contains(const QPointF& point) const
{
    // Check if point is near the path (within line width tolerance)
    QPainterPathStroker stroker;
    stroker.setWidth(m_lineWidth + 5); // Add some tolerance
    QPainterPath strokedPath = stroker.createStroke(m_path);
    return strokedPath.contains(point);
}

void DrawingAnnotation::move(const QPointF& offset)
{
    m_path.translate(offset);
    updateBounds();
    emit annotationChanged();
}

void DrawingAnnotation::resize(const QRectF& newBounds)
{
    if (m_boundingRect.isEmpty()) return;

    double scaleX = newBounds.width() / m_boundingRect.width();
    double scaleY = newBounds.height() / m_boundingRect.height();

    QTransform transform;
    transform.translate(-m_boundingRect.left(), -m_boundingRect.top());
    transform.scale(scaleX, scaleY);
    transform.translate(newBounds.left(), newBounds.top());

    m_path = transform.map(m_path);
    updateBounds();
    emit annotationChanged();
}

void DrawingAnnotation::render(QPainter* painter, double dpi, double zoomFactor) const
{
    if (!painter || m_path.isEmpty()) return;

    painter->save();

    // Convert PDF coordinates to pixel coordinates
    const double pointsToPixels = (dpi * zoomFactor) / 72.0;

    QTransform transform;
    transform.scale(pointsToPixels, pointsToPixels);
    QPainterPath pixelPath = transform.map(m_path);

    // Set up drawing pen
    QColor drawColor = m_color;
    drawColor.setAlphaF(m_opacity);
    QPen pen(drawColor, m_lineWidth * pointsToPixels);
    pen.setCapStyle(Qt::RoundCap);
    pen.setJoinStyle(Qt::RoundJoin);
    painter->setPen(pen);
    painter->setBrush(Qt::NoBrush);

    painter->drawPath(pixelPath);

    // Draw selection handles if selected
    if (m_selected) {
        painter->setBrush(Qt::blue);
        painter->setPen(QPen(Qt::blue, 1));

        QRectF pixelBounds = QRectF(
            m_boundingRect.x() * pointsToPixels,
            m_boundingRect.y() * pointsToPixels,
            m_boundingRect.width() * pointsToPixels,
            m_boundingRect.height() * pointsToPixels
        );

        const double handleSize = 6;
        painter->drawRect(pixelBounds.topLeft().x() - handleSize/2,
                         pixelBounds.topLeft().y() - handleSize/2,
                         handleSize, handleSize);
        painter->drawRect(pixelBounds.topRight().x() - handleSize/2,
                         pixelBounds.topRight().y() - handleSize/2,
                         handleSize, handleSize);
        painter->drawRect(pixelBounds.bottomLeft().x() - handleSize/2,
                         pixelBounds.bottomLeft().y() - handleSize/2,
                         handleSize, handleSize);
        painter->drawRect(pixelBounds.bottomRight().x() - handleSize/2,
                         pixelBounds.bottomRight().y() - handleSize/2,
                         handleSize, handleSize);
    }

    painter->restore();
}

void DrawingAnnotation::updateBounds()
{
    m_boundingRect = m_path.boundingRect();
}

QJsonObject DrawingAnnotation::toJson() const
{
    QJsonObject json = Annotation::toJson();

    // Serialize the path as a series of elements
    QJsonArray pathArray;
    for (int i = 0; i < m_path.elementCount(); ++i) {
        QPainterPath::Element element = m_path.elementAt(i);
        QJsonObject elementObj;
        elementObj["type"] = static_cast<int>(element.type);
        elementObj["x"] = element.x;
        elementObj["y"] = element.y;
        pathArray.append(elementObj);
    }
    json["path"] = pathArray;

    return json;
}

void DrawingAnnotation::fromJson(const QJsonObject& json)
{
    Annotation::fromJson(json);

    m_path = QPainterPath();
    QJsonArray pathArray = json["path"].toArray();
    for (const QJsonValue& value : pathArray) {
        QJsonObject elementObj = value.toObject();
        QPainterPath::ElementType type = static_cast<QPainterPath::ElementType>(elementObj["type"].toInt());
        double x = elementObj["x"].toDouble();
        double y = elementObj["y"].toDouble();

        switch (type) {
        case QPainterPath::MoveToElement:
            m_path.moveTo(x, y);
            break;
        case QPainterPath::LineToElement:
            m_path.lineTo(x, y);
            break;
        case QPainterPath::CurveToElement:
            // For curves, we'd need to handle the control points too
            // For simplicity, treating as line for now
            m_path.lineTo(x, y);
            break;
        default:
            break;
        }
    }
    updateBounds();
}

// ShapeAnnotation implementation
ShapeAnnotation::ShapeAnnotation(ShapeType shapeType, QObject *parent)
    : Annotation(AnnotationType::Rectangle, parent) // Will be updated based on shapeType
    , m_shapeType(shapeType)
{
    m_color = Qt::blue;
    m_opacity = 0.3;
    m_lineWidth = 2.0;

    // Update annotation type based on shape type
    switch (shapeType) {
    case ShapeType::Rectangle:
        m_type = AnnotationType::Rectangle;
        break;
    case ShapeType::Circle:
        m_type = AnnotationType::Circle;
        break;
    case ShapeType::Arrow:
        m_type = AnnotationType::Arrow;
        break;
    }
}

QRectF ShapeAnnotation::getBoundingRect() const
{
    return m_rect;
}

bool ShapeAnnotation::contains(const QPointF& point) const
{
    switch (m_shapeType) {
    case ShapeType::Rectangle:
        return m_rect.contains(point);
    case ShapeType::Circle: {
        QPointF center = m_rect.center();
        double radiusX = m_rect.width() / 2.0;
        double radiusY = m_rect.height() / 2.0;
        double dx = (point.x() - center.x()) / radiusX;
        double dy = (point.y() - center.y()) / radiusY;
        return (dx * dx + dy * dy) <= 1.0;
    }
    case ShapeType::Arrow:
        // For arrow, check if point is near the line
        return m_rect.contains(point); // Simplified for now
    }
    return false;
}

void ShapeAnnotation::move(const QPointF& offset)
{
    m_rect.translate(offset);
    emit annotationChanged();
}

void ShapeAnnotation::resize(const QRectF& newBounds)
{
    m_rect = newBounds;
    emit annotationChanged();
}

void ShapeAnnotation::render(QPainter* painter, double dpi, double zoomFactor) const
{
    if (!painter) return;

    painter->save();

    // Convert PDF coordinates to pixel coordinates
    const double pointsToPixels = (dpi * zoomFactor) / 72.0;

    QRectF pixelRect = QRectF(
        m_rect.x() * pointsToPixels,
        m_rect.y() * pointsToPixels,
        m_rect.width() * pointsToPixels,
        m_rect.height() * pointsToPixels
    );

    // Set up drawing properties
    QColor shapeColor = m_color;
    shapeColor.setAlphaF(m_opacity);
    QPen pen(shapeColor, m_lineWidth * pointsToPixels);
    painter->setPen(pen);
    painter->setBrush(Qt::NoBrush);

    // Draw shape based on type
    switch (m_shapeType) {
    case ShapeType::Rectangle:
        painter->drawRect(pixelRect);
        break;
    case ShapeType::Circle:
        painter->drawEllipse(pixelRect);
        break;
    case ShapeType::Arrow: {
        // Draw arrow from top-left to bottom-right
        QPointF start = pixelRect.topLeft();
        QPointF end = pixelRect.bottomRight();
        painter->drawLine(start, end);

        // Draw arrowhead
        double arrowLength = 15 * pointsToPixels;
        double arrowAngle = 30 * M_PI / 180; // 30 degrees
        QPointF direction = end - start;
        double length = sqrt(direction.x() * direction.x() + direction.y() * direction.y());
        if (length > 0) {
            direction /= length;
            QPointF perpendicular(-direction.y(), direction.x());

            QPointF arrowPoint1 = end - direction * arrowLength + perpendicular * arrowLength * tan(arrowAngle);
            QPointF arrowPoint2 = end - direction * arrowLength - perpendicular * arrowLength * tan(arrowAngle);

            painter->drawLine(end, arrowPoint1);
            painter->drawLine(end, arrowPoint2);
        }
        break;
    }
    }

    // Draw selection handles if selected
    if (m_selected) {
        painter->setBrush(Qt::blue);
        painter->setPen(QPen(Qt::blue, 1));

        const double handleSize = 6;
        painter->drawRect(pixelRect.topLeft().x() - handleSize/2,
                         pixelRect.topLeft().y() - handleSize/2,
                         handleSize, handleSize);
        painter->drawRect(pixelRect.topRight().x() - handleSize/2,
                         pixelRect.topRight().y() - handleSize/2,
                         handleSize, handleSize);
        painter->drawRect(pixelRect.bottomLeft().x() - handleSize/2,
                         pixelRect.bottomLeft().y() - handleSize/2,
                         handleSize, handleSize);
        painter->drawRect(pixelRect.bottomRight().x() - handleSize/2,
                         pixelRect.bottomRight().y() - handleSize/2,
                         handleSize, handleSize);
    }

    painter->restore();
}

QJsonObject ShapeAnnotation::toJson() const
{
    QJsonObject json = Annotation::toJson();
    json["shapeType"] = static_cast<int>(m_shapeType);
    json["x"] = m_rect.x();
    json["y"] = m_rect.y();
    json["width"] = m_rect.width();
    json["height"] = m_rect.height();
    return json;
}

void ShapeAnnotation::fromJson(const QJsonObject& json)
{
    Annotation::fromJson(json);
    m_shapeType = static_cast<ShapeType>(json["shapeType"].toInt());
    m_rect = QRectF(json["x"].toDouble(), json["y"].toDouble(),
                   json["width"].toDouble(), json["height"].toDouble());
}

// TextAnnotation implementation
TextAnnotation::TextAnnotation(QObject *parent)
    : Annotation(AnnotationType::Text, parent)
{
    m_color = Qt::black;
    m_opacity = 1.0;
    m_font = QFont("Arial", 12);
}

QRectF TextAnnotation::getBoundingRect() const
{
    return m_rect;
}

bool TextAnnotation::contains(const QPointF& point) const
{
    return m_rect.contains(point);
}

void TextAnnotation::move(const QPointF& offset)
{
    m_rect.translate(offset);
    emit annotationChanged();
}

void TextAnnotation::resize(const QRectF& newBounds)
{
    m_rect = newBounds;
    emit annotationChanged();
}

void TextAnnotation::render(QPainter* painter, double dpi, double zoomFactor) const
{
    if (!painter || m_text.isEmpty()) return;

    painter->save();

    // Convert PDF coordinates to pixel coordinates
    const double pointsToPixels = (dpi * zoomFactor) / 72.0;

    QRectF pixelRect = QRectF(
        m_rect.x() * pointsToPixels,
        m_rect.y() * pointsToPixels,
        m_rect.width() * pointsToPixels,
        m_rect.height() * pointsToPixels
    );

    // Set up text properties
    QColor textColor = m_color;
    textColor.setAlphaF(m_opacity);
    painter->setPen(textColor);

    // Scale font for zoom
    QFont scaledFont = m_font;
    scaledFont.setPointSizeF(m_font.pointSizeF() * zoomFactor);
    painter->setFont(scaledFont);

    // Draw text background if needed
    if (m_opacity < 1.0) {
        QColor bgColor = Qt::white;
        bgColor.setAlphaF(0.8);
        painter->fillRect(pixelRect, bgColor);
    }

    // Draw text
    painter->drawText(pixelRect, Qt::AlignLeft | Qt::AlignTop | Qt::TextWordWrap, m_text);

    // Draw border
    painter->setPen(QPen(Qt::gray, 1));
    painter->setBrush(Qt::NoBrush);
    painter->drawRect(pixelRect);

    // Draw selection handles if selected
    if (m_selected) {
        painter->setBrush(Qt::blue);
        painter->setPen(QPen(Qt::blue, 1));

        const double handleSize = 6;
        painter->drawRect(pixelRect.topLeft().x() - handleSize/2,
                         pixelRect.topLeft().y() - handleSize/2,
                         handleSize, handleSize);
        painter->drawRect(pixelRect.topRight().x() - handleSize/2,
                         pixelRect.topRight().y() - handleSize/2,
                         handleSize, handleSize);
        painter->drawRect(pixelRect.bottomLeft().x() - handleSize/2,
                         pixelRect.bottomLeft().y() - handleSize/2,
                         handleSize, handleSize);
        painter->drawRect(pixelRect.bottomRight().x() - handleSize/2,
                         pixelRect.bottomRight().y() - handleSize/2,
                         handleSize, handleSize);
    }

    painter->restore();
}

QJsonObject TextAnnotation::toJson() const
{
    QJsonObject json = Annotation::toJson();
    json["text"] = m_text;
    json["x"] = m_rect.x();
    json["y"] = m_rect.y();
    json["width"] = m_rect.width();
    json["height"] = m_rect.height();
    json["fontFamily"] = m_font.family();
    json["fontSize"] = m_font.pointSizeF();
    json["fontBold"] = m_font.bold();
    json["fontItalic"] = m_font.italic();
    return json;
}

void TextAnnotation::fromJson(const QJsonObject& json)
{
    Annotation::fromJson(json);
    m_text = json["text"].toString();
    m_rect = QRectF(json["x"].toDouble(), json["y"].toDouble(),
                   json["width"].toDouble(), json["height"].toDouble());

    m_font.setFamily(json["fontFamily"].toString());
    m_font.setPointSizeF(json["fontSize"].toDouble());
    m_font.setBold(json["fontBold"].toBool());
    m_font.setItalic(json["fontItalic"].toBool());
}
