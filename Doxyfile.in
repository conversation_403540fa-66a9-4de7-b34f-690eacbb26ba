# Doxyfile configuration for Optimized PDF Viewer
# This file is processed by <PERSON><PERSON><PERSON> to generate the final Doxyfile

# Project information
PROJECT_NAME           = "@DOXYGEN_PROJECT_NAME@"
PROJECT_NUMBER         = "@DOXYGEN_PROJECT_NUMBER@"
PROJECT_BRIEF          = "@DOXYGEN_PROJECT_BRIEF@"
PROJECT_LOGO           = @CMAKE_CURRENT_SOURCE_DIR@/docs/images/logo.png

# Build related configuration options
OUTPUT_DIRECTORY       = "@DOXYGEN_OUTPUT_DIRECTORY@"
CREATE_SUBDIRS         = NO
ALLOW_UNICODE_NAMES    = NO
OUTPUT_LANGUAGE        = English
BRIEF_MEMBER_DESC      = YES
REPEAT_BRIEF           = YES
ABBREVIATE_BRIEF       = "The $name class" \
                         "The $name widget" \
                         "The $name file" \
                         is \
                         provides \
                         specifies \
                         contains \
                         represents \
                         a \
                         an \
                         the

ALWAYS_DETAILED_SEC    = NO
INLINE_INHERITED_MEMB  = NO
FULL_PATH_NAMES        = YES
STRIP_FROM_PATH        = "@DOXYGEN_STRIP_FROM_PATH@"
STRIP_FROM_INC_PATH    = "@DOXYGEN_STRIP_FROM_INC_PATH@"
SHORT_NAMES            = NO
JAVADOC_AUTOBRIEF      = "@DOXYGEN_JAVADOC_AUTOBRIEF@"
QT_AUTOBRIEF           = "@DOXYGEN_QT_AUTOBRIEF@"
MULTILINE_CPP_IS_BRIEF = "@DOXYGEN_MULTILINE_CPP_IS_BRIEF@"
INHERIT_DOCS           = "@DOXYGEN_INHERIT_DOCS@"
SEPARATE_MEMBER_PAGES  = "@DOXYGEN_SEPARATE_MEMBER_PAGES@"
TAB_SIZE               = "@DOXYGEN_TAB_SIZE@"
ALIASES                = "@DOXYGEN_ALIASES@"

# Build related configuration options
EXTRACT_ALL            = "@DOXYGEN_EXTRACT_ALL@"
EXTRACT_PRIVATE        = "@DOXYGEN_EXTRACT_PRIVATE@"
EXTRACT_PACKAGE        = NO
EXTRACT_STATIC         = "@DOXYGEN_EXTRACT_STATIC@"
EXTRACT_LOCAL_CLASSES  = YES
EXTRACT_LOCAL_METHODS  = NO
EXTRACT_ANON_NSPACES   = NO
HIDE_UNDOC_MEMBERS     = NO
HIDE_UNDOC_CLASSES     = NO
HIDE_FRIEND_COMPOUNDS  = NO
HIDE_IN_BODY_DOCS      = NO
INTERNAL_DOCS          = NO
CASE_SENSE_NAMES       = YES
HIDE_SCOPE_NAMES       = NO
HIDE_COMPOUND_REFERENCE= NO
SHOW_INCLUDE_FILES     = YES
SHOW_GROUPED_MEMB_INC  = NO
FORCE_LOCAL_INCLUDES   = NO
INLINE_INFO            = YES
SORT_MEMBER_DOCS       = YES
SORT_BRIEF_DOCS        = NO
SORT_MEMBERS_CTORS_1ST = NO
SORT_GROUP_NAMES       = NO
SORT_BY_SCOPE_NAME     = NO
STRICT_PROTO_MATCHING  = NO
GENERATE_TODOLIST      = YES
GENERATE_TESTLIST      = YES
GENERATE_BUGLIST       = YES
GENERATE_DEPRECATEDLIST= YES
ENABLED_SECTIONS       = 
MAX_INITIALIZER_LINES  = 30
SHOW_USED_FILES        = YES
SHOW_FILES             = YES
SHOW_NAMESPACES        = YES
FILE_VERSION_FILTER    = 
LAYOUT_FILE            = 
CITE_BIB_FILES         = 

# Configuration options related to warning and progress messages
QUIET                  = NO
WARNINGS               = YES
WARN_IF_UNDOCUMENTED   = YES
WARN_IF_DOC_ERROR      = YES
WARN_NO_PARAMDOC       = NO
WARN_AS_ERROR          = NO
WARN_FORMAT            = "$file:$line: $text"
WARN_LOGFILE           = 

# Configuration options related to the input files
INPUT                  = "@DOXYGEN_INPUT_DIRECTORY@"
INPUT_ENCODING         = UTF-8
FILE_PATTERNS          = *.c \
                         *.cc \
                         *.cxx \
                         *.cpp \
                         *.c++ \
                         *.h \
                         *.hh \
                         *.hxx \
                         *.hpp \
                         *.h++ \
                         *.md \
                         *.markdown

RECURSIVE              = "@DOXYGEN_RECURSIVE@"
EXCLUDE                = 
EXCLUDE_SYMLINKS       = NO
EXCLUDE_PATTERNS       = */build/* \
                         */.*/* \
                         */.git/* \
                         */moc_* \
                         */ui_* \
                         */qrc_*

EXCLUDE_SYMBOLS        = 
EXAMPLE_PATH           = "@DOXYGEN_EXAMPLE_PATH@"
EXAMPLE_PATTERNS       = *
EXAMPLE_RECURSIVE      = NO
IMAGE_PATH             = "@DOXYGEN_IMAGE_PATH@"
INPUT_FILTER           = 
FILTER_PATTERNS        = 
FILTER_SOURCE_FILES    = NO
FILTER_SOURCE_PATTERNS = 
USE_MDFILE_AS_MAINPAGE = "@DOXYGEN_USE_MDFILE_AS_MAINPAGE@"

# Configuration options related to source browsing
SOURCE_BROWSER         = "@DOXYGEN_SOURCE_BROWSER@"
INLINE_SOURCES         = "@DOXYGEN_INLINE_SOURCES@"
STRIP_CODE_COMMENTS    = YES
REFERENCED_BY_RELATION = NO
REFERENCES_RELATION    = NO
REFERENCES_LINK_SOURCE = YES
SOURCE_TOOLTIPS        = YES
USE_HTAGS              = NO
VERBATIM_HEADERS       = "@DOXYGEN_VERBATIM_HEADERS@"
CLANG_ASSISTED_PARSING = "@DOXYGEN_CLANG_ASSISTED_PARSING@"
CLANG_OPTIONS          = "@DOXYGEN_CLANG_OPTIONS@"
CLANG_DATABASE_PATH    = 

# Configuration options related to the alphabetical class index
ALPHABETICAL_INDEX     = YES
COLS_IN_ALPHA_INDEX    = 5
IGNORE_PREFIX          = 

# Configuration options related to the HTML output
GENERATE_HTML          = "@DOXYGEN_GENERATE_HTML@"
HTML_OUTPUT            = "@DOXYGEN_HTML_OUTPUT@"
HTML_FILE_EXTENSION    = .html
HTML_HEADER            = "@DOXYGEN_HTML_HEADER@"
HTML_FOOTER            = "@DOXYGEN_HTML_FOOTER@"
HTML_STYLESHEET        = 
HTML_EXTRA_STYLESHEET  = "@DOXYGEN_HTML_EXTRA_STYLESHEET@"
HTML_EXTRA_FILES       = "@DOXYGEN_HTML_EXTRA_FILES@"
HTML_COLORSTYLE_HUE    = "@DOXYGEN_HTML_COLORSTYLE_HUE@"
HTML_COLORSTYLE_SAT    = "@DOXYGEN_HTML_COLORSTYLE_SAT@"
HTML_COLORSTYLE_GAMMA  = "@DOXYGEN_HTML_COLORSTYLE_GAMMA@"
HTML_TIMESTAMP         = "@DOXYGEN_HTML_TIMESTAMP@"
HTML_DYNAMIC_SECTIONS  = "@DOXYGEN_HTML_DYNAMIC_SECTIONS@"
HTML_INDEX_NUM_ENTRIES = "@DOXYGEN_HTML_INDEX_NUM_ENTRIES@"
GENERATE_DOCSET        = NO
GENERATE_HTMLHELP      = NO
GENERATE_QHP           = NO
GENERATE_ECLIPSEHELP   = NO
DISABLE_INDEX          = NO
GENERATE_TREEVIEW      = NO
ENUM_VALUES_PER_LINE   = 4
TREEVIEW_WIDTH         = 250
EXT_LINKS_IN_WINDOW    = NO
FORMULA_FONTSIZE       = 10
FORMULA_TRANSPARENT    = YES
USE_MATHJAX            = NO
SEARCHENGINE           = "@DOXYGEN_SEARCHENGINE@"
SERVER_BASED_SEARCH    = "@DOXYGEN_SERVER_BASED_SEARCH@"
EXTERNAL_SEARCH        = NO
SEARCHENGINE_URL       = 
SEARCHDATA_FILE        = searchdata.xml
EXTERNAL_SEARCH_ID     = 
EXTRA_SEARCH_MAPPINGS  = 

# Configuration options related to the LaTeX output
GENERATE_LATEX         = "@DOXYGEN_GENERATE_LATEX@"
LATEX_OUTPUT           = latex
LATEX_CMD_NAME         = latex
MAKEINDEX_CMD_NAME     = makeindex
COMPACT_LATEX          = NO
PAPER_TYPE             = a4
EXTRA_PACKAGES         = 
LATEX_HEADER           = 
LATEX_FOOTER           = 
LATEX_EXTRA_STYLESHEET = 
LATEX_EXTRA_FILES      = 
PDF_HYPERLINKS         = YES
USE_PDFLATEX           = YES
LATEX_BATCHMODE        = NO
LATEX_HIDE_INDICES     = NO
LATEX_SOURCE_CODE      = NO
LATEX_BIB_STYLE        = plain

# Configuration options related to the XML output
GENERATE_XML           = "@DOXYGEN_GENERATE_XML@"
XML_OUTPUT             = "@DOXYGEN_XML_OUTPUT@"
XML_PROGRAMLISTING     = YES

# Configuration options related to the DOCBOOK output
GENERATE_DOCBOOK       = NO
DOCBOOK_OUTPUT         = docbook
DOCBOOK_PROGRAMLISTING = NO

# Configuration options for the AutoGen Definitions output
GENERATE_AUTOGEN_DEF   = NO

# Configuration options related to the Perl module output
GENERATE_PERLMOD       = NO
PERLMOD_LATEX          = NO
PERLMOD_PRETTY         = YES
PERLMOD_MAKEVAR_PREFIX = 

# Configuration options related to the preprocessor
ENABLE_PREPROCESSING   = YES
MACRO_EXPANSION        = NO
EXPAND_ONLY_PREDEF     = NO
SEARCH_INCLUDES        = YES
INCLUDE_PATH           = 
INCLUDE_FILE_PATTERNS  = 
PREDEFINED             = Q_OBJECT \
                         Q_GADGET \
                         Q_DECLARE_METATYPE(x) \
                         Q_ENUMS(x) \
                         Q_FLAGS(x) \
                         Q_INTERFACES(x) \
                         Q_PROPERTY(x) \
                         Q_PRIVATE_SLOT(d,x) \
                         Q_SLOTS=slots \
                         Q_SIGNALS=signals \
                         Q_EMIT= \
                         Q_DECL_EXPORT= \
                         Q_DECL_IMPORT= \
                         Q_DECL_CONSTEXPR=constexpr \
                         Q_DECL_RELAXED_CONSTEXPR=constexpr \
                         Q_DECL_OVERRIDE=override \
                         Q_DECL_FINAL=final \
                         Q_DECL_NOEXCEPT=noexcept \
                         Q_DECL_NOEXCEPT_EXPR(x)=noexcept(x) \
                         Q_DECL_NOTHROW=noexcept \
                         Q_REQUIRED_RESULT= \
                         Q_COMPILER_MANGLES_RETURN_TYPE= \
                         Q_OUTOFLINE_TEMPLATE=template \
                         Q_TYPENAME=typename \
                         Q_DECL_DEPRECATED= \
                         Q_DECL_DEPRECATED_X(x)= \
                         Q_DECL_VARIABLE_DEPRECATED= \
                         Q_DECL_ENUMERATOR_DEPRECATED= \
                         Q_DECL_ENUMERATOR_DEPRECATED_X(x)= \
                         Q_LIKELY(x)=x \
                         Q_UNLIKELY(x)=x \
                         Q_UNREACHABLE()= \
                         Q_ASSUME(x)= \
                         Q_FALLTHROUGH()= \
                         Q_NODISCARD_CTOR= \
                         Q_NODISCARD_CTOR_X(x)= \
                         Q_IMPLICIT= \
                         Q_DISABLE_COPY(x)= \
                         Q_DISABLE_COPY_MOVE(x)= \
                         Q_DISABLE_MOVE(x)= \
                         Q_DECLARE_PRIVATE(x)= \
                         Q_DECLARE_PUBLIC(x)= \
                         Q_D(x)= \
                         Q_Q(x)= \
                         QT_BEGIN_NAMESPACE= \
                         QT_END_NAMESPACE= \
                         QT_FORWARD_DECLARE_CLASS(x)= \
                         QT_MANGLE_NAMESPACE(x)=x \
                         QT_PREPEND_NAMESPACE(x)=x \
                         QT_USE_NAMESPACE= \
                         QT_BEGIN_INCLUDE_NAMESPACE= \
                         QT_END_INCLUDE_NAMESPACE= \
                         QT_BEGIN_MOC_NAMESPACE= \
                         QT_END_MOC_NAMESPACE= \
                         QT_FORWARD_DECLARE_STRUCT(x)= \
                         QT_FORWARD_DECLARE_UNION(x)= \
                         QT_MAKE_UNCHECKED_ARRAY_ITERATOR(x)=x \
                         QT_MAKE_CHECKED_ARRAY_ITERATOR(x,y)=x

EXPAND_AS_DEFINED      = 
SKIP_FUNCTION_MACROS   = YES

# Configuration options related to external references
TAGFILES               = 
GENERATE_TAGFILE       = 
ALLEXTERNALS           = NO
EXTERNAL_GROUPS        = YES
EXTERNAL_PAGES         = YES
PERL_PATH              = /usr/bin/perl

# Configuration options related to the dot tool
CLASS_DIAGRAMS         = "@DOXYGEN_CLASS_DIAGRAMS@"
MSCGEN_PATH            = 
DIA_PATH               = 
HIDE_UNDOC_RELATIONS   = YES
HAVE_DOT               = "@DOXYGEN_HAVE_DOT@"
DOT_NUM_THREADS        = 0
DOT_FONTNAME           = Helvetica
DOT_FONTSIZE           = 10
DOT_FONTPATH           = 
CLASS_GRAPH            = YES
COLLABORATION_GRAPH    = "@DOXYGEN_COLLABORATION_GRAPH@"
GROUP_GRAPHS           = "@DOXYGEN_GROUP_GRAPHS@"
UML_LOOK               = "@DOXYGEN_UML_LOOK@"
UML_LIMIT_NUM_FIELDS   = "@DOXYGEN_UML_LIMIT_NUM_FIELDS@"
TEMPLATE_RELATIONS     = "@DOXYGEN_TEMPLATE_RELATIONS@"
INCLUDE_GRAPH          = "@DOXYGEN_INCLUDE_GRAPH@"
INCLUDED_BY_GRAPH      = "@DOXYGEN_INCLUDED_BY_GRAPH@"
CALL_GRAPH             = "@DOXYGEN_CALL_GRAPH@"
CALLER_GRAPH           = "@DOXYGEN_CALLER_GRAPH@"
GRAPHICAL_HIERARCHY    = "@DOXYGEN_GRAPHICAL_HIERARCHY@"
DIRECTORY_GRAPH        = "@DOXYGEN_DIRECTORY_GRAPH@"
DOT_IMAGE_FORMAT       = "@DOXYGEN_DOT_IMAGE_FORMAT@"
INTERACTIVE_SVG        = "@DOXYGEN_INTERACTIVE_SVG@"
DOT_PATH               = 
DOTFILE_DIRS           = 
MSCFILE_DIRS           = 
DIAFILE_DIRS           = 
PLANTUML_JAR_PATH      = 
PLANTUML_CFG_FILE      = 
PLANTUML_INCLUDE_PATH  = 
DOT_GRAPH_MAX_NODES    = "@DOXYGEN_DOT_GRAPH_MAX_NODES@"
MAX_DOT_GRAPH_DEPTH    = 0
DOT_TRANSPARENT        = "@DOXYGEN_DOT_TRANSPARENT@"
DOT_MULTI_TARGETS      = NO
GENERATE_LEGEND        = YES
DOT_CLEANUP            = YES
