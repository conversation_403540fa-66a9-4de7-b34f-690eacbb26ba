#include <QtTest/QtTest>
#include <QSignalSpy>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QTemporaryFile>
#include <memory>

#include "AnnotationManager.h"
#include "Annotation.h"

class TestAnnotationManager : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Basic functionality tests
    void testManagerCreation();
    void testAddAnnotation();
    void testRemoveAnnotation();
    void testClearAnnotations();
    void testGetAnnotations();
    void testGetAnnotationsForPage();
    void testGetAnnotationById();
    void testGetAnnotationAt();

    // Selection tests
    void testSelectAnnotation();
    void testDeselectAll();
    void testMultipleSelection();

    // Creation helper tests
    void testCreateHighlight();
    void testCreateNote();
    void testCreateShape();
    void testCreateText();
    void testCreateDrawing();

    // Persistence tests
    void testSaveLoadAnnotations();
    void testJsonSerialization();
    void testAnnotationFilePath();

    // Statistics tests
    void testAnnotationCounts();
    void testAnnotationCountsByPage();

    // Signal tests
    void testAnnotationSignals();
    void testSelectionSignals();

    // Undo/Redo tests
    void testUndoRedoSupport();

private:
    AnnotationManager* m_manager;
    QSignalSpy* m_addedSpy;
    QSignalSpy* m_removedSpy;
    QSignalSpy* m_selectedSpy;
    QSignalSpy* m_deselectedSpy;
    QSignalSpy* m_clearedSpy;
};

void TestAnnotationManager::initTestCase()
{
    // Setup for all tests
}

void TestAnnotationManager::cleanupTestCase()
{
    // Cleanup after all tests
}

void TestAnnotationManager::init()
{
    m_manager = new AnnotationManager(this);
    
    // Setup signal spies
    m_addedSpy = new QSignalSpy(m_manager, &AnnotationManager::annotationAdded);
    m_removedSpy = new QSignalSpy(m_manager, &AnnotationManager::annotationRemoved);
    m_selectedSpy = new QSignalSpy(m_manager, &AnnotationManager::annotationSelected);
    m_deselectedSpy = new QSignalSpy(m_manager, &AnnotationManager::annotationDeselected);
    m_clearedSpy = new QSignalSpy(m_manager, &AnnotationManager::annotationsCleared);
}

void TestAnnotationManager::cleanup()
{
    delete m_addedSpy;
    delete m_removedSpy;
    delete m_selectedSpy;
    delete m_deselectedSpy;
    delete m_clearedSpy;
    delete m_manager;
}

void TestAnnotationManager::testManagerCreation()
{
    QVERIFY(m_manager != nullptr);
    QCOMPARE(m_manager->getAnnotationCount(), 0);
    QVERIFY(m_manager->getAnnotations().isEmpty());
    QVERIFY(m_manager->getSelectedAnnotations().isEmpty());
}

void TestAnnotationManager::testAddAnnotation()
{
    auto highlight = std::make_unique<HighlightAnnotation>();
    highlight->setPageNumber(1);
    highlight->addQuad(QRectF(10, 20, 100, 15));
    
    QString annotationId = highlight->getId();
    Annotation* annotationPtr = highlight.get();
    
    m_manager->addAnnotation(std::move(highlight));
    
    // Verify annotation was added
    QCOMPARE(m_manager->getAnnotationCount(), 1);
    QCOMPARE(m_manager->getAnnotations().size(), 1);
    QCOMPARE(m_manager->getAnnotation(annotationId), annotationPtr);
    
    // Verify signal was emitted
    QCOMPARE(m_addedSpy->count(), 1);
    QList<QVariant> arguments = m_addedSpy->takeFirst();
    QCOMPARE(arguments.at(0).value<Annotation*>(), annotationPtr);
}

void TestAnnotationManager::testRemoveAnnotation()
{
    // Add annotation first
    auto note = std::make_unique<NoteAnnotation>();
    note->setPageNumber(2);
    QString annotationId = note->getId();
    
    m_manager->addAnnotation(std::move(note));
    QCOMPARE(m_manager->getAnnotationCount(), 1);
    
    // Remove annotation
    m_manager->removeAnnotation(annotationId);
    
    // Verify annotation was removed
    QCOMPARE(m_manager->getAnnotationCount(), 0);
    QVERIFY(m_manager->getAnnotation(annotationId) == nullptr);
    
    // Verify signal was emitted
    QCOMPARE(m_removedSpy->count(), 1);
    QList<QVariant> arguments = m_removedSpy->takeFirst();
    QCOMPARE(arguments.at(0).toString(), annotationId);
}

void TestAnnotationManager::testClearAnnotations()
{
    // Add multiple annotations
    auto highlight = std::make_unique<HighlightAnnotation>();
    highlight->setPageNumber(1);
    m_manager->addAnnotation(std::move(highlight));
    
    auto note = std::make_unique<NoteAnnotation>();
    note->setPageNumber(2);
    m_manager->addAnnotation(std::move(note));
    
    QCOMPARE(m_manager->getAnnotationCount(), 2);
    
    // Clear all annotations
    m_manager->clearAnnotations();
    
    // Verify all annotations were cleared
    QCOMPARE(m_manager->getAnnotationCount(), 0);
    QVERIFY(m_manager->getAnnotations().isEmpty());
    
    // Verify signal was emitted
    QCOMPARE(m_clearedSpy->count(), 1);
}

void TestAnnotationManager::testGetAnnotations()
{
    // Add annotations on different pages
    auto highlight = std::make_unique<HighlightAnnotation>();
    highlight->setPageNumber(1);
    m_manager->addAnnotation(std::move(highlight));
    
    auto note = std::make_unique<NoteAnnotation>();
    note->setPageNumber(2);
    m_manager->addAnnotation(std::move(note));
    
    auto shape = std::make_unique<ShapeAnnotation>(ShapeAnnotation::ShapeType::Rectangle);
    shape->setPageNumber(1);
    m_manager->addAnnotation(std::move(shape));
    
    // Test getting all annotations
    QList<Annotation*> allAnnotations = m_manager->getAnnotations();
    QCOMPARE(allAnnotations.size(), 3);
}

void TestAnnotationManager::testGetAnnotationsForPage()
{
    // Add annotations on different pages
    auto highlight1 = std::make_unique<HighlightAnnotation>();
    highlight1->setPageNumber(1);
    m_manager->addAnnotation(std::move(highlight1));
    
    auto note = std::make_unique<NoteAnnotation>();
    note->setPageNumber(2);
    m_manager->addAnnotation(std::move(note));
    
    auto highlight2 = std::make_unique<HighlightAnnotation>();
    highlight2->setPageNumber(1);
    m_manager->addAnnotation(std::move(highlight2));
    
    // Test getting annotations for specific page
    QList<Annotation*> page1Annotations = m_manager->getAnnotationsForPage(1);
    QList<Annotation*> page2Annotations = m_manager->getAnnotationsForPage(2);
    QList<Annotation*> page3Annotations = m_manager->getAnnotationsForPage(3);
    
    QCOMPARE(page1Annotations.size(), 2);
    QCOMPARE(page2Annotations.size(), 1);
    QCOMPARE(page3Annotations.size(), 0);
    
    // Verify page numbers
    for (Annotation* annotation : page1Annotations) {
        QCOMPARE(annotation->getPageNumber(), 1);
    }
    for (Annotation* annotation : page2Annotations) {
        QCOMPARE(annotation->getPageNumber(), 2);
    }
}

void TestAnnotationManager::testGetAnnotationById()
{
    auto highlight = std::make_unique<HighlightAnnotation>();
    QString annotationId = highlight->getId();
    Annotation* annotationPtr = highlight.get();
    
    m_manager->addAnnotation(std::move(highlight));
    
    // Test getting annotation by ID
    Annotation* foundAnnotation = m_manager->getAnnotation(annotationId);
    QCOMPARE(foundAnnotation, annotationPtr);
    
    // Test getting non-existent annotation
    Annotation* notFound = m_manager->getAnnotation("non-existent-id");
    QVERIFY(notFound == nullptr);
}

void TestAnnotationManager::testGetAnnotationAt()
{
    auto highlight = std::make_unique<HighlightAnnotation>();
    highlight->setPageNumber(1);
    highlight->addQuad(QRectF(10, 20, 100, 15));
    Annotation* highlightPtr = highlight.get();
    
    auto note = std::make_unique<NoteAnnotation>();
    note->setPageNumber(1);
    note->setPosition(QPointF(200, 300));
    Annotation* notePtr = note.get();
    
    m_manager->addAnnotation(std::move(highlight));
    m_manager->addAnnotation(std::move(note));
    
    // Test finding annotation at specific points
    Annotation* foundHighlight = m_manager->getAnnotationAt(QPointF(50, 25), 1);
    QCOMPARE(foundHighlight, highlightPtr);
    
    Annotation* foundNote = m_manager->getAnnotationAt(QPointF(200, 300), 1);
    QCOMPARE(foundNote, notePtr);
    
    // Test point not on any annotation
    Annotation* notFound = m_manager->getAnnotationAt(QPointF(500, 500), 1);
    QVERIFY(notFound == nullptr);
    
    // Test wrong page
    Annotation* wrongPage = m_manager->getAnnotationAt(QPointF(50, 25), 2);
    QVERIFY(wrongPage == nullptr);
}

void TestAnnotationManager::testSelectAnnotation()
{
    auto highlight = std::make_unique<HighlightAnnotation>();
    Annotation* annotationPtr = highlight.get();
    QString annotationId = highlight->getId();
    
    m_manager->addAnnotation(std::move(highlight));
    
    // Test selection by pointer
    m_manager->selectAnnotation(annotationPtr);
    
    QCOMPARE(m_manager->getSelectedAnnotations().size(), 1);
    QCOMPARE(m_manager->getSelectedAnnotation(), annotationPtr);
    QCOMPARE(m_selectedSpy->count(), 1);
    
    // Test selection by ID
    m_manager->deselectAll();
    m_selectedSpy->clear();
    
    m_manager->selectAnnotation(annotationId);
    
    QCOMPARE(m_manager->getSelectedAnnotations().size(), 1);
    QCOMPARE(m_manager->getSelectedAnnotation(), annotationPtr);
    QCOMPARE(m_selectedSpy->count(), 1);
}

void TestAnnotationManager::testDeselectAll()
{
    // Add and select multiple annotations
    auto highlight = std::make_unique<HighlightAnnotation>();
    Annotation* highlightPtr = highlight.get();
    m_manager->addAnnotation(std::move(highlight));
    
    auto note = std::make_unique<NoteAnnotation>();
    Annotation* notePtr = note.get();
    m_manager->addAnnotation(std::move(note));
    
    m_manager->selectAnnotation(highlightPtr);
    m_manager->selectAnnotation(notePtr);
    
    QCOMPARE(m_manager->getSelectedAnnotations().size(), 2);
    
    // Deselect all
    m_manager->deselectAll();
    
    QCOMPARE(m_manager->getSelectedAnnotations().size(), 0);
    QVERIFY(m_manager->getSelectedAnnotation() == nullptr);
    QCOMPARE(m_deselectedSpy->count(), 2);
}

void TestAnnotationManager::testMultipleSelection()
{
    // Add multiple annotations
    auto highlight = std::make_unique<HighlightAnnotation>();
    Annotation* highlightPtr = highlight.get();
    m_manager->addAnnotation(std::move(highlight));
    
    auto note = std::make_unique<NoteAnnotation>();
    Annotation* notePtr = note.get();
    m_manager->addAnnotation(std::move(note));
    
    auto shape = std::make_unique<ShapeAnnotation>(ShapeAnnotation::ShapeType::Rectangle);
    Annotation* shapePtr = shape.get();
    m_manager->addAnnotation(std::move(shape));
    
    // Select multiple annotations
    m_manager->selectAnnotation(highlightPtr);
    m_manager->selectAnnotation(notePtr);
    m_manager->selectAnnotation(shapePtr);
    
    QList<Annotation*> selected = m_manager->getSelectedAnnotations();
    QCOMPARE(selected.size(), 3);
    QVERIFY(selected.contains(highlightPtr));
    QVERIFY(selected.contains(notePtr));
    QVERIFY(selected.contains(shapePtr));
}

void TestAnnotationManager::testCreateHighlight()
{
    QList<QRectF> quads;
    quads.append(QRectF(10, 20, 100, 15));
    quads.append(QRectF(10, 35, 80, 15));
    
    HighlightAnnotation* highlight = m_manager->createHighlight(1, quads);
    
    QVERIFY(highlight != nullptr);
    QCOMPARE(highlight->getType(), AnnotationType::Highlight);
    QCOMPARE(highlight->getPageNumber(), 1);
    QCOMPARE(highlight->getQuads().size(), 2);
    QCOMPARE(m_manager->getAnnotationCount(), 1);
}

void TestAnnotationManager::testCreateNote()
{
    QPointF position(150, 250);
    
    NoteAnnotation* note = m_manager->createNote(2, position);
    
    QVERIFY(note != nullptr);
    QCOMPARE(note->getType(), AnnotationType::Note);
    QCOMPARE(note->getPageNumber(), 2);
    QCOMPARE(note->getPosition(), position);
    QCOMPARE(m_manager->getAnnotationCount(), 1);
}

void TestAnnotationManager::testCreateShape()
{
    QRectF bounds(50, 100, 200, 150);

    ShapeAnnotation* shape = m_manager->createShape(3, ShapeAnnotation::ShapeType::Circle, bounds);

    QVERIFY(shape != nullptr);
    QCOMPARE(shape->getType(), AnnotationType::Circle);
    QCOMPARE(shape->getPageNumber(), 3);
    QCOMPARE(shape->getShapeType(), ShapeAnnotation::ShapeType::Circle);
    QCOMPARE(shape->getRect(), bounds);
    QCOMPARE(m_manager->getAnnotationCount(), 1);
}

void TestAnnotationManager::testCreateText()
{
    QRectF bounds(25, 50, 300, 100);
    QString text = "Test text annotation";
    
    TextAnnotation* textAnnotation = m_manager->createText(4, bounds, text);
    
    QVERIFY(textAnnotation != nullptr);
    QCOMPARE(textAnnotation->getType(), AnnotationType::Text);
    QCOMPARE(textAnnotation->getPageNumber(), 4);
    QCOMPARE(textAnnotation->getRect(), bounds);
    QCOMPARE(textAnnotation->getText(), text);
    QCOMPARE(m_manager->getAnnotationCount(), 1);
}

void TestAnnotationManager::testCreateDrawing()
{
    DrawingAnnotation* drawing = m_manager->createDrawing(5);

    QVERIFY(drawing != nullptr);
    QCOMPARE(drawing->getType(), AnnotationType::Drawing);
    QCOMPARE(drawing->getPageNumber(), 5);
    QVERIFY(drawing->getPath().isEmpty());
    QCOMPARE(m_manager->getAnnotationCount(), 1);
}

void TestAnnotationManager::testSaveLoadAnnotations()
{
    // Create test annotations
    auto highlight = std::make_unique<HighlightAnnotation>();
    highlight->setPageNumber(1);
    highlight->setContent("Test highlight");
    highlight->addQuad(QRectF(10, 20, 100, 15));
    
    auto note = std::make_unique<NoteAnnotation>();
    note->setPageNumber(2);
    note->setContent("Test note");
    note->setPosition(QPointF(150, 250));
    
    m_manager->addAnnotation(std::move(highlight));
    m_manager->addAnnotation(std::move(note));
    
    // Save to temporary file
    QTemporaryFile tempFile;
    QVERIFY(tempFile.open());
    QString filePath = tempFile.fileName();
    tempFile.close();
    
    bool saveResult = m_manager->saveAnnotations(filePath);
    QVERIFY(saveResult);
    
    // Clear annotations and load from file
    m_manager->clearAnnotations();
    QCOMPARE(m_manager->getAnnotationCount(), 0);
    
    bool loadResult = m_manager->loadAnnotations(filePath);
    QVERIFY(loadResult);
    
    // Verify loaded annotations
    QCOMPARE(m_manager->getAnnotationCount(), 2);
    
    QList<Annotation*> page1Annotations = m_manager->getAnnotationsForPage(1);
    QList<Annotation*> page2Annotations = m_manager->getAnnotationsForPage(2);
    
    QCOMPARE(page1Annotations.size(), 1);
    QCOMPARE(page2Annotations.size(), 1);
    
    QCOMPARE(page1Annotations[0]->getType(), AnnotationType::Highlight);
    QCOMPARE(page2Annotations[0]->getType(), AnnotationType::Note);
}

void TestAnnotationManager::testJsonSerialization()
{
    // Create test annotations
    auto highlight = std::make_unique<HighlightAnnotation>();
    highlight->setPageNumber(1);
    highlight->setContent("Test highlight");
    highlight->addQuad(QRectF(10, 20, 100, 15));
    
    auto note = std::make_unique<NoteAnnotation>();
    note->setPageNumber(2);
    note->setContent("Test note");
    note->setPosition(QPointF(150, 250));
    
    m_manager->addAnnotation(std::move(highlight));
    m_manager->addAnnotation(std::move(note));
    
    // Serialize to JSON
    QJsonObject json = m_manager->toJson();
    
    QVERIFY(json.contains("version"));
    QVERIFY(json.contains("annotations"));
    
    QJsonArray annotationsArray = json["annotations"].toArray();
    QCOMPARE(annotationsArray.size(), 2);
    
    // Clear and deserialize
    m_manager->clearAnnotations();
    m_manager->fromJson(json);
    
    // Verify deserialized annotations
    QCOMPARE(m_manager->getAnnotationCount(), 2);
}

void TestAnnotationManager::testAnnotationFilePath()
{
    QString pdfPath = "/path/to/document.pdf";
    QString annotationPath = m_manager->generateAnnotationFilePath(pdfPath);
    
    QVERIFY(annotationPath.endsWith(".annotations.json"));
    QVERIFY(annotationPath.contains("document"));
}

void TestAnnotationManager::testAnnotationCounts()
{
    // Add annotations on different pages
    auto highlight1 = std::make_unique<HighlightAnnotation>();
    highlight1->setPageNumber(1);
    m_manager->addAnnotation(std::move(highlight1));
    
    auto highlight2 = std::make_unique<HighlightAnnotation>();
    highlight2->setPageNumber(1);
    m_manager->addAnnotation(std::move(highlight2));
    
    auto note = std::make_unique<NoteAnnotation>();
    note->setPageNumber(2);
    m_manager->addAnnotation(std::move(note));
    
    // Test total count
    QCOMPARE(m_manager->getAnnotationCount(), 3);
    
    // Test page-specific counts
    QCOMPARE(m_manager->getAnnotationCountForPage(1), 2);
    QCOMPARE(m_manager->getAnnotationCountForPage(2), 1);
    QCOMPARE(m_manager->getAnnotationCountForPage(3), 0);
}

void TestAnnotationManager::testAnnotationCountsByPage()
{
    // Add annotations on different pages
    for (int page = 1; page <= 3; ++page) {
        for (int i = 0; i < page; ++i) {
            auto highlight = std::make_unique<HighlightAnnotation>();
            highlight->setPageNumber(page);
            m_manager->addAnnotation(std::move(highlight));
        }
    }
    
    QHash<int, int> countsByPage = m_manager->getAnnotationCountsByPage();
    
    QCOMPARE(countsByPage[1], 1);
    QCOMPARE(countsByPage[2], 2);
    QCOMPARE(countsByPage[3], 3);
    QCOMPARE(countsByPage.value(4, 0), 0); // Page 4 should not exist
}

void TestAnnotationManager::testAnnotationSignals()
{
    auto highlight = std::make_unique<HighlightAnnotation>();
    QString annotationId = highlight->getId();
    
    // Test add signal
    m_manager->addAnnotation(std::move(highlight));
    QCOMPARE(m_addedSpy->count(), 1);
    
    // Test remove signal
    m_manager->removeAnnotation(annotationId);
    QCOMPARE(m_removedSpy->count(), 1);
    
    // Test clear signal
    auto note = std::make_unique<NoteAnnotation>();
    m_manager->addAnnotation(std::move(note));
    m_manager->clearAnnotations();
    QCOMPARE(m_clearedSpy->count(), 1);
}

void TestAnnotationManager::testSelectionSignals()
{
    auto highlight = std::make_unique<HighlightAnnotation>();
    Annotation* annotationPtr = highlight.get();
    
    m_manager->addAnnotation(std::move(highlight));
    
    // Test selection signal
    m_manager->selectAnnotation(annotationPtr);
    QCOMPARE(m_selectedSpy->count(), 1);
    
    // Test deselection signal
    m_manager->deselectAll();
    QCOMPARE(m_deselectedSpy->count(), 1);
}

void TestAnnotationManager::testUndoRedoSupport()
{
    QUndoStack* undoStack = m_manager->getUndoStack();
    QVERIFY(undoStack != nullptr);
    
    // Initially, no undo/redo available
    QVERIFY(!m_manager->canUndo());
    QVERIFY(!m_manager->canRedo());
    
    // Note: Full undo/redo testing would require implementing actual commands
    // This test just verifies the undo stack is available
}

QTEST_MAIN(TestAnnotationManager)
#include "test_annotation_manager.moc"
