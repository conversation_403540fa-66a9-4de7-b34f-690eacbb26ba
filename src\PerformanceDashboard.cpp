#include "PerformanceDashboard.h"
#include "StyleHelper.h"
#include "DesignSystem.h"
#include <QApplication>
#include <QFileDialog>
#include <QMessageBox>
#include <QSplitter>
#include <QTabWidget>
#include <QDateTime>
#include <QJsonDocument>

PerformanceDashboard::PerformanceDashboard(PerformanceMonitor* monitor, QWidget* parent)
    : QWidget(parent)
    , m_monitor(monitor)
    , m_updateTimer(new QTimer(this))
    , m_updateInterval(1000)
    , m_chartTimeRange(10) // 10 minutes
    , m_lastUpdate(QDateTime::currentDateTime())
{
    setupUI();
    
    // Connect to monitor signals
    connect(m_monitor, &PerformanceMonitor::metricsUpdated, this, &PerformanceDashboard::onMetricsUpdated);
    connect(m_monitor, &PerformanceMonitor::performanceAlert, this, &PerformanceDashboard::onPerformanceAlert);
    connect(m_monitor, &PerformanceMonitor::optimizationRecommended, this, &PerformanceDashboard::onOptimizationRecommended);
    
    // Setup update timer
    m_updateTimer->setSingleShot(false);
    connect(m_updateTimer, &QTimer::timeout, this, &PerformanceDashboard::updateRealTimeData);
    
    // Initial data load
    refreshData();
}

PerformanceDashboard::~PerformanceDashboard()
{
    stopRealTimeUpdates();
}

void PerformanceDashboard::setupUI()
{
    setWindowTitle(tr("Performance Dashboard"));
    setMinimumSize(1000, 700);
    
    // Main layout
    m_mainLayout = new QVBoxLayout(this);
    m_topLayout = new QHBoxLayout();
    m_bottomLayout = new QHBoxLayout();
    
    m_mainLayout->addLayout(m_topLayout);
    m_mainLayout->addLayout(m_bottomLayout);
    
    // Create sections
    createOverviewSection();
    createChartsSection();
    createStatsSection();
    createRecommendationsSection();
    createControlsSection();
    
    // Layout sections
    QSplitter* topSplitter = new QSplitter(Qt::Horizontal);
    topSplitter->addWidget(m_overviewGroup);
    topSplitter->addWidget(m_chartsGroup);
    topSplitter->setSizes({300, 700});
    
    QSplitter* bottomSplitter = new QSplitter(Qt::Horizontal);
    bottomSplitter->addWidget(m_statsGroup);
    bottomSplitter->addWidget(m_recommendationsGroup);
    bottomSplitter->setSizes({500, 500});
    
    m_topLayout->addWidget(topSplitter);
    m_bottomLayout->addWidget(bottomSplitter);
    m_mainLayout->addWidget(m_controlsGroup);
    
    // Apply styling
    applyDashboardStyling();
}

void PerformanceDashboard::createOverviewSection()
{
    m_overviewGroup = new QGroupBox(tr("System Overview"));
    m_overviewLayout = new QGridLayout(m_overviewGroup);
    
    // Memory usage
    m_overviewLayout->addWidget(new QLabel(tr("Memory Usage:")), 0, 0);
    m_memoryProgress = new QProgressBar();
    m_memoryProgress->setRange(0, 100);
    m_memoryLabel = new QLabel(tr("0 MB / 0 MB"));
    m_overviewLayout->addWidget(m_memoryProgress, 0, 1);
    m_overviewLayout->addWidget(m_memoryLabel, 0, 2);
    
    // CPU usage
    m_overviewLayout->addWidget(new QLabel(tr("CPU Usage:")), 1, 0);
    m_cpuProgress = new QProgressBar();
    m_cpuProgress->setRange(0, 100);
    m_cpuLabel = new QLabel(tr("0%"));
    m_overviewLayout->addWidget(m_cpuProgress, 1, 1);
    m_overviewLayout->addWidget(m_cpuLabel, 1, 2);
    
    // Thread count
    m_overviewLayout->addWidget(new QLabel(tr("Active Threads:")), 2, 0);
    m_threadsLabel = new QLabel(tr("0"));
    m_overviewLayout->addWidget(m_threadsLabel, 2, 1, 1, 2);
    
    // Cache hit ratio
    m_overviewLayout->addWidget(new QLabel(tr("Cache Hit Ratio:")), 3, 0);
    m_cacheHitRatioLabel = new QLabel(tr("0%"));
    m_overviewLayout->addWidget(m_cacheHitRatioLabel, 3, 1, 1, 2);
    
    StyleHelper::applyCard(m_overviewGroup);
}

void PerformanceDashboard::createChartsSection()
{
    m_chartsGroup = new QGroupBox(tr("Performance Charts"));
    m_chartsLayout = new QVBoxLayout(m_chartsGroup);
    
    // Create tab widget for different charts
    QTabWidget* chartTabs = new QTabWidget();
    
    // Memory chart
    createMemoryChart();
    m_memoryChartView = new QChartView(m_memoryChart);
    m_memoryChartView->setRenderHint(QPainter::Antialiasing);
    chartTabs->addTab(m_memoryChartView, tr("Memory"));
    
    // CPU chart
    createCpuChart();
    m_cpuChartView = new QChartView(m_cpuChart);
    m_cpuChartView->setRenderHint(QPainter::Antialiasing);
    chartTabs->addTab(m_cpuChartView, tr("CPU"));
    
    // Rendering chart
    createRenderingChart();
    m_renderingChartView = new QChartView(m_renderingChart);
    m_renderingChartView->setRenderHint(QPainter::Antialiasing);
    chartTabs->addTab(m_renderingChartView, tr("Rendering"));
    
    // Cache chart
    createCacheChart();
    m_cacheChartView = new QChartView(m_cacheChart);
    m_cacheChartView->setRenderHint(QPainter::Antialiasing);
    chartTabs->addTab(m_cacheChartView, tr("Cache"));
    
    m_chartsLayout->addWidget(chartTabs);
    StyleHelper::applyCard(m_chartsGroup);
    StyleHelper::applyTabWidget(chartTabs);
}

void PerformanceDashboard::createStatsSection()
{
    m_statsGroup = new QGroupBox(tr("Detailed Statistics"));
    m_statsLayout = new QVBoxLayout(m_statsGroup);
    
    m_statsText = new QTextEdit();
    m_statsText->setReadOnly(true);
    m_statsText->setMaximumHeight(200);
    m_statsLayout->addWidget(m_statsText);
    
    StyleHelper::applyCard(m_statsGroup);
    StyleHelper::applyTextArea(m_statsText);
}

void PerformanceDashboard::createRecommendationsSection()
{
    m_recommendationsGroup = new QGroupBox(tr("Optimization Recommendations"));
    m_recommendationsLayout = new QVBoxLayout(m_recommendationsGroup);
    
    m_recommendationsList = new QListWidget();
    m_recommendationsList->setMaximumHeight(150);
    m_recommendationsLayout->addWidget(m_recommendationsList);
    
    m_optimizeButton = new QPushButton(tr("Apply Optimizations"));
    connect(m_optimizeButton, &QPushButton::clicked, this, &PerformanceDashboard::optimizePerformance);
    m_recommendationsLayout->addWidget(m_optimizeButton);
    
    StyleHelper::applyCard(m_recommendationsGroup);
    StyleHelper::applyPrimaryButton(m_optimizeButton);
}

void PerformanceDashboard::createControlsSection()
{
    m_controlsGroup = new QGroupBox(tr("Controls"));
    m_controlsLayout = new QHBoxLayout(m_controlsGroup);
    
    m_exportButton = new QPushButton(tr("Export Data"));
    m_clearButton = new QPushButton(tr("Clear Metrics"));
    m_refreshButton = new QPushButton(tr("Refresh"));
    m_detailsButton = new QPushButton(tr("Detailed View"));
    
    connect(m_exportButton, &QPushButton::clicked, this, &PerformanceDashboard::exportMetrics);
    connect(m_clearButton, &QPushButton::clicked, this, &PerformanceDashboard::clearMetrics);
    connect(m_refreshButton, &QPushButton::clicked, this, &PerformanceDashboard::refreshData);
    connect(m_detailsButton, &QPushButton::clicked, this, &PerformanceDashboard::showDetailedStats);
    
    m_controlsLayout->addWidget(m_exportButton);
    m_controlsLayout->addWidget(m_clearButton);
    m_controlsLayout->addWidget(m_refreshButton);
    m_controlsLayout->addWidget(m_detailsButton);
    m_controlsLayout->addStretch();
    
    StyleHelper::applySecondaryButton(m_exportButton);
    StyleHelper::applySecondaryButton(m_clearButton);
    StyleHelper::applyPrimaryButton(m_refreshButton);
    StyleHelper::applySecondaryButton(m_detailsButton);
    StyleHelper::applyToolbar(m_controlsGroup);
}

void PerformanceDashboard::createMemoryChart()
{
    m_memoryChart = new QChart();
    m_memoryChart->setTitle(tr("Memory Usage Over Time"));
    m_memoryChart->setAnimationOptions(QChart::SeriesAnimations);
    
    m_memorySeries = new QLineSeries();
    m_memorySeries->setName(tr("Memory (MB)"));
    m_memorySeries->setColor(DesignSystem::Colors::Primary);
    
    m_memoryChart->addSeries(m_memorySeries);
    m_memoryChart->createDefaultAxes();
    
    // Configure axes
    QValueAxis* yAxis = qobject_cast<QValueAxis*>(m_memoryChart->axes(Qt::Vertical).first());
    if (yAxis) {
        yAxis->setTitleText(tr("Memory (MB)"));
        yAxis->setLabelFormat("%.0f");
    }
    
    QDateTimeAxis* xAxis = qobject_cast<QDateTimeAxis*>(m_memoryChart->axes(Qt::Horizontal).first());
    if (xAxis) {
        xAxis->setTitleText(tr("Time"));
        xAxis->setFormat("hh:mm:ss");
    }
}

void PerformanceDashboard::createCpuChart()
{
    m_cpuChart = new QChart();
    m_cpuChart->setTitle(tr("CPU Usage Over Time"));
    m_cpuChart->setAnimationOptions(QChart::SeriesAnimations);
    
    m_cpuSeries = new QLineSeries();
    m_cpuSeries->setName(tr("CPU (%)"));
    m_cpuSeries->setColor(DesignSystem::Colors::Warning);
    
    m_cpuChart->addSeries(m_cpuSeries);
    m_cpuChart->createDefaultAxes();
    
    // Configure axes
    QValueAxis* yAxis = qobject_cast<QValueAxis*>(m_cpuChart->axes(Qt::Vertical).first());
    if (yAxis) {
        yAxis->setTitleText(tr("CPU Usage (%)"));
        yAxis->setRange(0, 100);
        yAxis->setLabelFormat("%.0f");
    }
    
    QDateTimeAxis* xAxis = qobject_cast<QDateTimeAxis*>(m_cpuChart->axes(Qt::Horizontal).first());
    if (xAxis) {
        xAxis->setTitleText(tr("Time"));
        xAxis->setFormat("hh:mm:ss");
    }
}

void PerformanceDashboard::createRenderingChart()
{
    m_renderingChart = new QChart();
    m_renderingChart->setTitle(tr("Rendering Performance"));
    m_renderingChart->setAnimationOptions(QChart::SeriesAnimations);
    
    m_renderTimeSeries = new QLineSeries();
    m_renderTimeSeries->setName(tr("Render Time (ms)"));
    m_renderTimeSeries->setColor(DesignSystem::Colors::Success);
    
    m_renderingChart->addSeries(m_renderTimeSeries);
    m_renderingChart->createDefaultAxes();
    
    // Configure axes
    QValueAxis* yAxis = qobject_cast<QValueAxis*>(m_renderingChart->axes(Qt::Vertical).first());
    if (yAxis) {
        yAxis->setTitleText(tr("Render Time (ms)"));
        yAxis->setLabelFormat("%.0f");
    }
    
    QDateTimeAxis* xAxis = qobject_cast<QDateTimeAxis*>(m_renderingChart->axes(Qt::Horizontal).first());
    if (xAxis) {
        xAxis->setTitleText(tr("Time"));
        xAxis->setFormat("hh:mm:ss");
    }
}

void PerformanceDashboard::createCacheChart()
{
    m_cacheChart = new QChart();
    m_cacheChart->setTitle(tr("Cache Performance"));
    m_cacheChart->setAnimationOptions(QChart::SeriesAnimations);
    
    m_cacheHitSeries = new QLineSeries();
    m_cacheHitSeries->setName(tr("Cache Hit Ratio (%)"));
    m_cacheHitSeries->setColor(DesignSystem::Colors::Info);
    
    m_cacheChart->addSeries(m_cacheHitSeries);
    m_cacheChart->createDefaultAxes();
    
    // Configure axes
    QValueAxis* yAxis = qobject_cast<QValueAxis*>(m_cacheChart->axes(Qt::Vertical).first());
    if (yAxis) {
        yAxis->setTitleText(tr("Hit Ratio (%)"));
        yAxis->setRange(0, 100);
        yAxis->setLabelFormat("%.1f");
    }
    
    QDateTimeAxis* xAxis = qobject_cast<QDateTimeAxis*>(m_cacheChart->axes(Qt::Horizontal).first());
    if (xAxis) {
        xAxis->setTitleText(tr("Time"));
        xAxis->setFormat("hh:mm:ss");
    }
}

void PerformanceDashboard::applyDashboardStyling()
{
    // Apply design system styling
    QString dashboardStyle = QString(
        "QGroupBox {"
        "    font-family: %1;"
        "    font-size: %2px;"
        "    font-weight: %3;"
        "    color: %4;"
        "    border: 1px solid %5;"
        "    border-radius: %6px;"
        "    margin-top: %7px;"
        "    padding-top: %8px;"
        "}"
        "QGroupBox::title {"
        "    subcontrol-origin: margin;"
        "    left: %9px;"
        "    padding: 0 %10px 0 %10px;"
        "    background-color: %11;"
        "}"
        "QProgressBar {"
        "    border: 1px solid %5;"
        "    border-radius: %12px;"
        "    text-align: center;"
        "    background-color: %13;"
        "}"
        "QProgressBar::chunk {"
        "    background-color: %14;"
        "    border-radius: %12px;"
        "}"
    ).arg(DesignSystem::Typography::PrimaryFont)
     .arg(DesignSystem::Typography::BodyMedium)
     .arg(DesignSystem::Typography::WeightMedium)
     .arg(DesignSystem::Colors::TextPrimary.name())
     .arg(DesignSystem::Colors::BorderPrimary.name())
     .arg(DesignSystem::BorderRadius::Medium)
     .arg(DesignSystem::Spacing::Small)
     .arg(DesignSystem::Spacing::Medium)
     .arg(DesignSystem::Spacing::Small)
     .arg(DesignSystem::Spacing::XSmall)
     .arg(DesignSystem::Colors::BackgroundPrimary.name())
     .arg(DesignSystem::BorderRadius::Small)
     .arg(DesignSystem::Colors::BackgroundTertiary.name())
     .arg(DesignSystem::Colors::Primary.name());
    
    setStyleSheet(dashboardStyle);
}

void PerformanceDashboard::startRealTimeUpdates()
{
    m_updateTimer->start(m_updateInterval);
}

void PerformanceDashboard::stopRealTimeUpdates()
{
    m_updateTimer->stop();
}

void PerformanceDashboard::refreshData()
{
    updateOverviewMetrics();
    updateCharts();
    updateStatistics();
    updateRecommendations();
    m_lastUpdate = QDateTime::currentDateTime();
}

void PerformanceDashboard::setUpdateInterval(int milliseconds)
{
    m_updateInterval = milliseconds;
    if (m_updateTimer->isActive()) {
        m_updateTimer->setInterval(milliseconds);
    }
}

void PerformanceDashboard::setChartTimeRange(int minutes)
{
    m_chartTimeRange = minutes;
    updateCharts();
}

void PerformanceDashboard::onMetricsUpdated()
{
    // Update in real-time if enabled
    if (m_updateTimer->isActive()) {
        updateRealTimeData();
    }
}

void PerformanceDashboard::onPerformanceAlert(const QString& message, const QString& category)
{
    // Add alert to recommendations list with special formatting
    QString alertText = QString("[ALERT] %1: %2").arg(category, message);
    QListWidgetItem* item = new QListWidgetItem(alertText);
    item->setForeground(QColor(DesignSystem::Colors::Error));
    item->setIcon(QIcon(":/icons/warning.png"));
    m_recommendationsList->insertItem(0, item);
}

void PerformanceDashboard::onOptimizationRecommended(const QString& recommendation)
{
    QListWidgetItem* item = new QListWidgetItem(recommendation);
    item->setForeground(QColor(DesignSystem::Colors::Info));
    item->setIcon(QIcon(":/icons/lightbulb.png"));
    m_recommendationsList->addItem(item);
}

void PerformanceDashboard::updateRealTimeData()
{
    updateOverviewMetrics();
    updateCharts();

    // Update statistics less frequently
    if (m_lastUpdate.secsTo(QDateTime::currentDateTime()) > 5) {
        updateStatistics();
        m_lastUpdate = QDateTime::currentDateTime();
    }
}

void PerformanceDashboard::updateOverviewMetrics()
{
    if (!m_monitor) return;

    SystemMetrics metrics = m_monitor->getSystemMetrics();
    RenderingStats renderStats = m_monitor->getRenderingStats();

    // Update memory display
    double memoryMB = metrics.memoryUsage / (1024.0 * 1024.0);
    double availableMB = metrics.availableMemory / (1024.0 * 1024.0);
    double totalMB = memoryMB + availableMB;

    if (totalMB > 0) {
        int memoryPercent = static_cast<int>((memoryMB / totalMB) * 100);
        m_memoryProgress->setValue(memoryPercent);
        m_memoryLabel->setText(QString("%1 MB / %2 MB (%3%)")
                              .arg(memoryMB, 0, 'f', 1)
                              .arg(totalMB, 0, 'f', 1)
                              .arg(memoryPercent));
    }

    // Update CPU display
    int cpuPercent = static_cast<int>(metrics.cpuUsage);
    m_cpuProgress->setValue(cpuPercent);
    m_cpuLabel->setText(QString("%1%").arg(cpuPercent));

    // Update threads display
    m_threadsLabel->setText(QString("%1").arg(renderStats.activeThreads));

    // Update cache hit ratio
    double hitRatio = renderStats.getCacheHitRatio();
    m_cacheHitRatioLabel->setText(QString("%1%").arg(hitRatio, 0, 'f', 1));
}

void PerformanceDashboard::updateCharts()
{
    if (!m_monitor) return;

    QList<PerformanceMetric> recentMetrics = m_monitor->getRecentMetrics(m_chartTimeRange);

    // Clear existing data
    m_memorySeries->clear();
    m_cpuSeries->clear();
    m_renderTimeSeries->clear();
    m_cacheHitSeries->clear();

    // Group metrics by type and add to appropriate series
    for (const auto& metric : recentMetrics) {
        qint64 timestamp = metric.timestamp.toMSecsSinceEpoch();

        if (metric.name == "Memory" && metric.category == "System") {
            m_memorySeries->append(timestamp, metric.value);
        } else if (metric.name == "CPU" && metric.category == "System") {
            m_cpuSeries->append(timestamp, metric.value);
        } else if (metric.name == "RenderTime" && metric.category == "Rendering") {
            m_renderTimeSeries->append(timestamp, metric.value);
        }
    }

    // Calculate and add cache hit ratio points
    RenderingStats stats = m_monitor->getRenderingStats();
    if (stats.cacheHits + stats.cacheMisses > 0) {
        qint64 now = QDateTime::currentDateTime().toMSecsSinceEpoch();
        m_cacheHitSeries->append(now, stats.getCacheHitRatio());
    }

    // Update chart axes ranges
    updateChartAxes();
}

void PerformanceDashboard::updateChartAxes()
{
    QDateTime now = QDateTime::currentDateTime();
    QDateTime startTime = now.addSecs(-m_chartTimeRange * 60);

    // Update all chart x-axes
    for (QChart* chart : {m_memoryChart, m_cpuChart, m_renderingChart, m_cacheChart}) {
        QDateTimeAxis* xAxis = qobject_cast<QDateTimeAxis*>(chart->axes(Qt::Horizontal).first());
        if (xAxis) {
            xAxis->setRange(startTime, now);
        }
    }
}

void PerformanceDashboard::updateStatistics()
{
    if (!m_monitor) return;

    QString statsText;
    QTextStream stream(&statsText);

    // System metrics
    SystemMetrics sysMetrics = m_monitor->getSystemMetrics();
    stream << "=== System Metrics ===\n";
    stream << QString("Memory Usage: %1 MB\n").arg(sysMetrics.memoryUsage / (1024.0 * 1024.0), 0, 'f', 1);
    stream << QString("CPU Usage: %1%\n").arg(sysMetrics.cpuUsage, 0, 'f', 1);
    stream << QString("Thread Count: %1\n").arg(sysMetrics.threadCount);
    stream << "\n";

    // Rendering statistics
    RenderingStats renderStats = m_monitor->getRenderingStats();
    stream << "=== Rendering Statistics ===\n";
    stream << QString("Total Pages Rendered: %1\n").arg(renderStats.totalPagesRendered);
    stream << QString("Average Render Time: %1 ms\n").arg(renderStats.averageRenderTime, 0, 'f', 1);
    stream << QString("Cache Hits: %1\n").arg(renderStats.cacheHits);
    stream << QString("Cache Misses: %1\n").arg(renderStats.cacheMisses);
    stream << QString("Cache Hit Ratio: %1%\n").arg(renderStats.getCacheHitRatio(), 0, 'f', 1);
    stream << QString("Peak Memory Usage: %1 MB\n").arg(renderStats.peakMemoryUsage, 0, 'f', 1);
    stream << "\n";

    // Performance averages
    stream << "=== Performance Averages (Last 5 minutes) ===\n";
    stream << QString("Average Memory: %1 MB\n").arg(m_monitor->getAverageMetric("Memory", 5), 0, 'f', 1);
    stream << QString("Average CPU: %1%\n").arg(m_monitor->getAverageMetric("CPU", 5), 0, 'f', 1);
    stream << QString("Average Render Time: %1 ms\n").arg(m_monitor->getAverageMetric("RenderTime", 5), 0, 'f', 1);

    m_statsText->setPlainText(statsText);
}

void PerformanceDashboard::updateRecommendations()
{
    if (!m_monitor) return;

    // Clear old recommendations (keep alerts)
    for (int i = m_recommendationsList->count() - 1; i >= 0; --i) {
        QListWidgetItem* item = m_recommendationsList->item(i);
        if (item && !item->text().startsWith("[ALERT]")) {
            delete m_recommendationsList->takeItem(i);
        }
    }

    // Add current recommendations
    QStringList recommendations = m_monitor->getOptimizationRecommendations();
    for (const QString& recommendation : recommendations) {
        QListWidgetItem* item = new QListWidgetItem(recommendation);
        item->setIcon(QIcon(":/icons/lightbulb.png"));
        m_recommendationsList->addItem(item);
    }
}

void PerformanceDashboard::exportMetrics()
{
    QString fileName = QFileDialog::getSaveFileName(this,
        tr("Export Performance Metrics"),
        QString("performance_metrics_%1.json").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")),
        tr("JSON Files (*.json);;CSV Files (*.csv)"));

    if (fileName.isEmpty()) return;

    QString data;
    if (fileName.endsWith(".json")) {
        data = m_monitor->exportMetricsToJson();
    } else {
        data = m_monitor->exportMetricsToCsv();
    }

    QFile file(fileName);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        out << data;
        QMessageBox::information(this, tr("Export Complete"),
                               tr("Performance metrics exported successfully to:\n%1").arg(fileName));
    } else {
        QMessageBox::warning(this, tr("Export Failed"),
                           tr("Failed to write to file:\n%1").arg(fileName));
    }
}

void PerformanceDashboard::clearMetrics()
{
    int ret = QMessageBox::question(this, tr("Clear Metrics"),
                                   tr("Are you sure you want to clear all performance metrics?\nThis action cannot be undone."),
                                   QMessageBox::Yes | QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        // Note: We would need to add a clearMetrics method to PerformanceMonitor
        m_recommendationsList->clear();
        refreshData();
        QMessageBox::information(this, tr("Metrics Cleared"), tr("All performance metrics have been cleared."));
    }
}

void PerformanceDashboard::optimizePerformance()
{
    // Apply automatic optimizations based on current recommendations
    QStringList recommendations = m_monitor->getOptimizationRecommendations();

    if (recommendations.isEmpty()) {
        QMessageBox::information(this, tr("No Optimizations"),
                               tr("No optimization recommendations are currently available."));
        return;
    }

    QString message = tr("The following optimizations will be applied:\n\n");
    for (const QString& rec : recommendations) {
        message += "• " + rec + "\n";
    }
    message += tr("\nDo you want to proceed?");

    int ret = QMessageBox::question(this, tr("Apply Optimizations"), message,
                                   QMessageBox::Yes | QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        // Here we would implement the actual optimization logic
        // For now, just show a success message
        QMessageBox::information(this, tr("Optimizations Applied"),
                               tr("Performance optimizations have been applied successfully."));

        // Clear recommendations since they've been addressed
        m_recommendationsList->clear();
    }
}

void PerformanceDashboard::showDetailedStats()
{
    // Create a detailed statistics dialog
    QDialog* dialog = new QDialog(this);
    dialog->setWindowTitle(tr("Detailed Performance Statistics"));
    dialog->setMinimumSize(600, 400);

    QVBoxLayout* layout = new QVBoxLayout(dialog);
    QTextEdit* detailsText = new QTextEdit();
    detailsText->setReadOnly(true);

    // Generate detailed statistics
    QString details;
    QTextStream stream(&details);

    stream << "=== Detailed Performance Report ===\n";
    stream << QString("Generated: %1\n\n").arg(QDateTime::currentDateTime().toString());

    // Add comprehensive statistics here
    QMap<QString, double> categorySummary = m_monitor->getCategorySummary();
    stream << "=== Category Summary ===\n";
    for (auto it = categorySummary.begin(); it != categorySummary.end(); ++it) {
        stream << QString("%1: %2\n").arg(it.key()).arg(it.value(), 0, 'f', 2);
    }

    detailsText->setPlainText(details);
    layout->addWidget(detailsText);

    QPushButton* closeButton = new QPushButton(tr("Close"));
    connect(closeButton, &QPushButton::clicked, dialog, &QDialog::accept);
    layout->addWidget(closeButton);

    StyleHelper::applyTextArea(detailsText);
    StyleHelper::applyPrimaryButton(closeButton);

    dialog->exec();
}
