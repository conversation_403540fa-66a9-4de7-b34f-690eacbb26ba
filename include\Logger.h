#ifndef LOGGER_H
#define LOGGER_H

#include <QObject>
#include <QFile>
#include <QTextStream>
#include <QDateTime>
#include <QMutex>
#include <QDebug>

enum class LogLevel {
    Debug = 0,
    Info = 1,
    Warning = 2,
    Error = 3,
    Critical = 4
};

class Logger : public QObject
{
    Q_OBJECT

public:
    static Logger* instance();
    
    // Logging methods
    void debug(const QString& message, const QString& category = QString());
    void info(const QString& message, const QString& category = QString());
    void warning(const QString& message, const QString& category = QString());
    void error(const QString& message, const QString& category = QString());
    void critical(const QString& message, const QString& category = QString());
    
    // Configuration
    void setLogLevel(LogLevel level);
    LogLevel getLogLevel() const;
    
    void setLogToFile(bool enabled);
    bool getLogToFile() const;
    
    void setLogToConsole(bool enabled);
    bool getLogToConsole() const;
    
    void setLogFilePath(const QString& path);
    QString getLogFilePath() const;
    
    void setMaxLogFileSize(qint64 maxSize);
    qint64 getMaxLogFileSize() const;
    
    // Log management
    void clearLog();
    void rotateLogFile();
    QStringList getRecentLogs(int count = 100) const;
    
    // Performance logging
    void startTimer(const QString& operation);
    void endTimer(const QString& operation);
    
    // Error handling integration
    void logException(const QString& exception, const QString& location);
    void logSystemInfo();

signals:
    void logMessage(LogLevel level, const QString& message, const QString& category);

private:
    explicit Logger(QObject *parent = nullptr);
    ~Logger();
    
    void writeToFile(const QString& formattedMessage);
    void writeToConsole(const QString& formattedMessage);
    QString formatMessage(LogLevel level, const QString& message, const QString& category) const;
    QString levelToString(LogLevel level) const;
    void checkLogFileSize();
    
    static Logger* s_instance;
    static QMutex s_mutex;
    
    // Configuration
    LogLevel m_logLevel;
    bool m_logToFile;
    bool m_logToConsole;
    QString m_logFilePath;
    qint64 m_maxLogFileSize;
    
    // File handling
    QFile* m_logFile;
    QTextStream* m_logStream;
    QMutex m_fileMutex;
    
    // Performance tracking
    QMap<QString, QDateTime> m_timers;
    QMutex m_timerMutex;
    
    // Constants
    static const qint64 DEFAULT_MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    static const QString DEFAULT_LOG_FILE;
};

// Convenience macros
#define LOG_DEBUG(msg) Logger::instance()->debug(msg, Q_FUNC_INFO)
#define LOG_INFO(msg) Logger::instance()->info(msg, Q_FUNC_INFO)
#define LOG_WARNING(msg) Logger::instance()->warning(msg, Q_FUNC_INFO)
#define LOG_ERROR(msg) Logger::instance()->error(msg, Q_FUNC_INFO)
#define LOG_CRITICAL(msg) Logger::instance()->critical(msg, Q_FUNC_INFO)

#define LOG_TIMER_START(op) Logger::instance()->startTimer(op)
#define LOG_TIMER_END(op) Logger::instance()->endTimer(op)

#endif // LOGGER_H
