#include "DesignSystem.h"
#include <QApplication>

// Static instance
DesignSystem* DesignSystem::s_instance = nullptr;

// Color definitions
const QColor DesignSystem::Colors::Primary = QColor(0, 120, 212);           // #0078d4
const QColor DesignSystem::Colors::PrimaryHover = QColor(16, 110, 190);     // #106ebe
const QColor DesignSystem::Colors::PrimaryPressed = QColor(0, 90, 158);     // #005a9e
const QColor DesignSystem::Colors::PrimaryLight = QColor(222, 236, 249);    // #deecf9

const QColor DesignSystem::Colors::TextPrimary = QColor(30, 30, 30);        // #1e1e1e
const QColor DesignSystem::Colors::TextSecondary = QColor(97, 97, 97);      // #616161
const QColor DesignSystem::Colors::TextTertiary = QColor(142, 142, 142);    // #8e8e8e
const QColor DesignSystem::Colors::TextDisabled = QColor(161, 159, 157);    // #a19f9d

const QColor DesignSystem::Colors::BackgroundPrimary = QColor(255, 255, 255);   // #ffffff
const QColor DesignSystem::Colors::BackgroundSecondary = QColor(248, 249, 250); // #f8f9fa
const QColor DesignSystem::Colors::BackgroundTertiary = QColor(241, 243, 244);  // #f1f3f4
const QColor DesignSystem::Colors::BackgroundHover = QColor(246, 248, 250);     // #f6f8fa

const QColor DesignSystem::Colors::BorderPrimary = QColor(225, 228, 232);   // #e1e4e8
const QColor DesignSystem::Colors::BorderSecondary = QColor(209, 213, 219); // #d1d5db
const QColor DesignSystem::Colors::BorderHover = QColor(3, 102, 214);       // #0366d6

const QColor DesignSystem::Colors::Success = QColor(40, 167, 69);           // #28a745
const QColor DesignSystem::Colors::Warning = QColor(255, 193, 7);           // #ffc107
const QColor DesignSystem::Colors::Error = QColor(220, 53, 69);             // #dc3545
const QColor DesignSystem::Colors::Info = QColor(23, 162, 184);             // #17a2b8

// Typography definitions
const QString DesignSystem::Typography::PrimaryFont = "Segoe UI, system-ui, -apple-system, sans-serif";
const QString DesignSystem::Typography::MonospaceFont = "Consolas, Monaco, 'Courier New', monospace";

const int DesignSystem::Typography::DisplayLarge = 36;
const int DesignSystem::Typography::DisplayMedium = 32;
const int DesignSystem::Typography::DisplaySmall = 28;

const int DesignSystem::Typography::HeadingLarge = 24;
const int DesignSystem::Typography::HeadingMedium = 20;
const int DesignSystem::Typography::HeadingSmall = 18;

const int DesignSystem::Typography::BodyLarge = 16;
const int DesignSystem::Typography::BodyMedium = 14;
const int DesignSystem::Typography::BodySmall = 12;

const int DesignSystem::Typography::CaptionLarge = 11;
const int DesignSystem::Typography::CaptionSmall = 10;

const int DesignSystem::Typography::WeightLight = 300;
const int DesignSystem::Typography::WeightRegular = 400;
const int DesignSystem::Typography::WeightMedium = 500;
const int DesignSystem::Typography::WeightSemiBold = 600;
const int DesignSystem::Typography::WeightBold = 700;

const double DesignSystem::Typography::LineHeightTight = 1.2;
const double DesignSystem::Typography::LineHeightNormal = 1.4;
const double DesignSystem::Typography::LineHeightLoose = 1.6;

// Spacing definitions
const int DesignSystem::Spacing::XSmall = 4;
const int DesignSystem::Spacing::Small = 8;
const int DesignSystem::Spacing::Medium = 16;
const int DesignSystem::Spacing::Large = 24;
const int DesignSystem::Spacing::XLarge = 32;
const int DesignSystem::Spacing::XXLarge = 48;
const int DesignSystem::Spacing::XXXLarge = 64;

const int DesignSystem::Spacing::ButtonPadding = 12;
const int DesignSystem::Spacing::CardPadding = 16;
const int DesignSystem::Spacing::SectionPadding = 24;
const int DesignSystem::Spacing::PagePadding = 32;

// Border radius definitions
const int DesignSystem::BorderRadius::None = 0;
const int DesignSystem::BorderRadius::Small = 4;
const int DesignSystem::BorderRadius::Medium = 6;
const int DesignSystem::BorderRadius::Large = 8;
const int DesignSystem::BorderRadius::XLarge = 12;
const int DesignSystem::BorderRadius::Round = 50; // Percentage handled in CSS

// Shadow definitions
const QString DesignSystem::Shadows::None = "none";
const QString DesignSystem::Shadows::Small = "0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)";
const QString DesignSystem::Shadows::Medium = "0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06)";
const QString DesignSystem::Shadows::Large = "0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)";

// Component sizes
const QSize DesignSystem::ComponentSizes::ButtonSmall = QSize(24, 24);
const QSize DesignSystem::ComponentSizes::ButtonMedium = QSize(32, 32);
const QSize DesignSystem::ComponentSizes::ButtonLarge = QSize(48, 48);

const int DesignSystem::ComponentSizes::IconSmall = 16;
const int DesignSystem::ComponentSizes::IconMedium = 20;
const int DesignSystem::ComponentSizes::IconLarge = 24;
const int DesignSystem::ComponentSizes::IconXLarge = 32;

const int DesignSystem::ComponentSizes::InputSmall = 28;
const int DesignSystem::ComponentSizes::InputMedium = 32;
const int DesignSystem::ComponentSizes::InputLarge = 40;

// Animation definitions
const int DesignSystem::Animations::DurationFast = 150;
const int DesignSystem::Animations::DurationNormal = 250;
const int DesignSystem::Animations::DurationSlow = 400;

const QString DesignSystem::Animations::EasingStandard = "cubic-bezier(0.4, 0.0, 0.2, 1)";
const QString DesignSystem::Animations::EasingDecelerate = "cubic-bezier(0.0, 0.0, 0.2, 1)";
const QString DesignSystem::Animations::EasingAccelerate = "cubic-bezier(0.4, 0.0, 1, 1)";

DesignSystem::DesignSystem()
{
}

DesignSystem* DesignSystem::instance()
{
    if (!s_instance) {
        s_instance = new DesignSystem();
    }
    return s_instance;
}

QString DesignSystem::getColorStyleSheet(const QColor& color)
{
    return QString("color: %1;").arg(color.name());
}

QString DesignSystem::getFontStyleSheet(const QString& family, int size, int weight)
{
    return QString("font-family: %1; font-size: %2px; font-weight: %3;")
           .arg(family).arg(size).arg(weight);
}

QString DesignSystem::getSpacingStyleSheet(int top, int right, int bottom, int left)
{
    return QString("padding: %1px %2px %3px %4px;").arg(top).arg(right).arg(bottom).arg(left);
}

QString DesignSystem::getBorderRadiusStyleSheet(int radius)
{
    return QString("border-radius: %1px;").arg(radius);
}

QString DesignSystem::getShadowStyleSheet(const QString& shadow)
{
    return QString("box-shadow: %1;").arg(shadow);
}

QString DesignSystem::getButtonStyleSheet(const QString& variant)
{
    if (variant == "primary") {
        return QString(
            "QPushButton {"
            "    background-color: %1;"
            "    color: white;"
            "    border: none;"
            "    border-radius: %2px;"
            "    padding: %3px %4px;"
            "    font-family: %5;"
            "    font-size: %6px;"
            "    font-weight: %7;"
            "}"
            "QPushButton:hover {"
            "    background-color: %8;"
            "}"
            "QPushButton:pressed {"
            "    background-color: %9;"
            "}"
        ).arg(Colors::Primary.name())
         .arg(BorderRadius::Medium)
         .arg(Spacing::Small).arg(Spacing::ButtonPadding)
         .arg(Typography::PrimaryFont)
         .arg(Typography::BodyMedium)
         .arg(Typography::WeightMedium)
         .arg(Colors::PrimaryHover.name())
         .arg(Colors::PrimaryPressed.name());
    }
    
    // Default/secondary button style
    return QString(
        "QPushButton {"
        "    background-color: %1;"
        "    color: %2;"
        "    border: 1px solid %3;"
        "    border-radius: %4px;"
        "    padding: %5px %6px;"
        "    font-family: %7;"
        "    font-size: %8px;"
        "    font-weight: %9;"
        "}"
        "QPushButton:hover {"
        "    background-color: %10;"
        "    border-color: %11;"
        "}"
    ).arg(Colors::BackgroundPrimary.name())
     .arg(Colors::TextPrimary.name())
     .arg(Colors::BorderPrimary.name())
     .arg(BorderRadius::Medium)
     .arg(Spacing::Small).arg(Spacing::ButtonPadding)
     .arg(Typography::PrimaryFont)
     .arg(Typography::BodyMedium)
     .arg(Typography::WeightMedium)
     .arg(Colors::BackgroundHover.name())
     .arg(Colors::BorderHover.name());
}

QString DesignSystem::getCardStyleSheet()
{
    return QString(
        "QWidget {"
        "    background-color: %1;"
        "    border: 1px solid %2;"
        "    border-radius: %3px;"
        "    padding: %4px;"
        "}"
    ).arg(Colors::BackgroundPrimary.name())
     .arg(Colors::BorderPrimary.name())
     .arg(BorderRadius::Large)
     .arg(Spacing::CardPadding);
}

QString DesignSystem::getInputStyleSheet()
{
    return QString(
        "QLineEdit, QTextEdit, QComboBox {"
        "    background-color: %1;"
        "    color: %2;"
        "    border: 1px solid %3;"
        "    border-radius: %4px;"
        "    padding: %5px %6px;"
        "    font-family: %7;"
        "    font-size: %8px;"
        "    min-height: %9px;"
        "}"
        "QLineEdit:focus, QTextEdit:focus, QComboBox:focus {"
        "    border-color: %10;"
        "    outline: none;"
        "}"
    ).arg(Colors::BackgroundPrimary.name())
     .arg(Colors::TextPrimary.name())
     .arg(Colors::BorderPrimary.name())
     .arg(BorderRadius::Medium)
     .arg(Spacing::Small).arg(Spacing::Medium)
     .arg(Typography::PrimaryFont)
     .arg(Typography::BodyMedium)
     .arg(ComponentSizes::InputMedium)
     .arg(Colors::Primary.name());
}

QString DesignSystem::getTextStyleSheet(const QString& variant)
{
    if (variant == "display-large") {
        return getFontStyleSheet(Typography::PrimaryFont, Typography::DisplayLarge, Typography::WeightBold);
    } else if (variant == "heading-large") {
        return getFontStyleSheet(Typography::PrimaryFont, Typography::HeadingLarge, Typography::WeightSemiBold);
    } else if (variant == "heading-medium") {
        return getFontStyleSheet(Typography::PrimaryFont, Typography::HeadingMedium, Typography::WeightSemiBold);
    } else if (variant == "body-large") {
        return getFontStyleSheet(Typography::PrimaryFont, Typography::BodyLarge, Typography::WeightRegular);
    } else if (variant == "body-small") {
        return getFontStyleSheet(Typography::PrimaryFont, Typography::BodySmall, Typography::WeightRegular);
    } else if (variant == "caption") {
        return getFontStyleSheet(Typography::PrimaryFont, Typography::CaptionLarge, Typography::WeightRegular) +
               getColorStyleSheet(Colors::TextSecondary);
    }
    
    // Default: body-medium
    return getFontStyleSheet(Typography::PrimaryFont, Typography::BodyMedium, Typography::WeightRegular);
}
