<!-- HTML footer for doxygen $doxygenversion-->
<!-- start footer part -->
<!--BEGIN GENERATE_TREEVIEW-->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    $navpath
    <li class="footer">$generatedby <a href="https://www.doxygen.org/index.html"><img class="footer" src="$relpath^doxygen.svg" width="104" height="31" alt="doxygen"/></a> $doxygenversion </li>
  </ul>
</div>
<!--END GENERATE_TREEVIEW-->
<!--BEGIN !GENERATE_TREEVIEW-->
<hr class="footer"/><address class="footer"><small>
$generatedby&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="$relpath^doxygen.svg" width="104" height="31" alt="doxygen"/></a> $doxygenversion
</small></address>
<!--END !GENERATE_TREEVIEW-->

<!-- Custom footer content -->
<footer class="footer">
    <div style="max-width: 1200px; margin: 0 auto; padding: 0 1rem;">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin-bottom: 2rem;">
            <!-- Project Information -->
            <div>
                <h4 style="color: var(--primary-color); margin-bottom: 1rem; font-size: 1.1rem;">Optimized PDF Viewer</h4>
                <p style="margin-bottom: 0.5rem; line-height: 1.6;">
                    A high-performance Qt6-based PDF viewer with advanced annotation capabilities, 
                    modern FluentUI-style interface, and comprehensive document management features.
                </p>
                <p style="margin-bottom: 0;">
                    <strong>Version:</strong> $projectnumber<br>
                    <strong>Built:</strong> <span id="buildDate"></span>
                </p>
            </div>
            
            <!-- Quick Links -->
            <div>
                <h4 style="color: var(--primary-color); margin-bottom: 1rem; font-size: 1.1rem;">Quick Links</h4>
                <ul style="list-style: none; padding: 0; margin: 0;">
                    <li style="margin-bottom: 0.5rem;">
                        <a href="index.html" style="color: var(--text-secondary); text-decoration: none;">
                            📖 API Overview
                        </a>
                    </li>
                    <li style="margin-bottom: 0.5rem;">
                        <a href="classes.html" style="color: var(--text-secondary); text-decoration: none;">
                            🏗️ Class List
                        </a>
                    </li>
                    <li style="margin-bottom: 0.5rem;">
                        <a href="files.html" style="color: var(--text-secondary); text-decoration: none;">
                            📁 File List
                        </a>
                    </li>
                    <li style="margin-bottom: 0.5rem;">
                        <a href="examples.html" style="color: var(--text-secondary); text-decoration: none;">
                            💡 Examples
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Resources -->
            <div>
                <h4 style="color: var(--primary-color); margin-bottom: 1rem; font-size: 1.1rem;">Resources</h4>
                <ul style="list-style: none; padding: 0; margin: 0;">
                    <li style="margin-bottom: 0.5rem;">
                        <a href="https://github.com/your-org/qt-pdf-render" target="_blank" rel="noopener" 
                           style="color: var(--text-secondary); text-decoration: none;">
                            🔗 Source Code
                        </a>
                    </li>
                    <li style="margin-bottom: 0.5rem;">
                        <a href="https://github.com/your-org/qt-pdf-render/issues" target="_blank" rel="noopener"
                           style="color: var(--text-secondary); text-decoration: none;">
                            🐛 Report Issues
                        </a>
                    </li>
                    <li style="margin-bottom: 0.5rem;">
                        <a href="https://github.com/your-org/qt-pdf-render/wiki" target="_blank" rel="noopener"
                           style="color: var(--text-secondary); text-decoration: none;">
                            📚 User Guide
                        </a>
                    </li>
                    <li style="margin-bottom: 0.5rem;">
                        <a href="https://github.com/your-org/qt-pdf-render/blob/main/CONTRIBUTING.md" target="_blank" rel="noopener"
                           style="color: var(--text-secondary); text-decoration: none;">
                            🤝 Contributing
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Technology Stack -->
            <div>
                <h4 style="color: var(--primary-color); margin-bottom: 1rem; font-size: 1.1rem;">Built With</h4>
                <ul style="list-style: none; padding: 0; margin: 0;">
                    <li style="margin-bottom: 0.5rem;">
                        <a href="https://www.qt.io/" target="_blank" rel="noopener"
                           style="color: var(--text-secondary); text-decoration: none;">
                            ⚡ Qt6 Framework
                        </a>
                    </li>
                    <li style="margin-bottom: 0.5rem;">
                        <a href="https://poppler.freedesktop.org/" target="_blank" rel="noopener"
                           style="color: var(--text-secondary); text-decoration: none;">
                            📄 Poppler PDF Library
                        </a>
                    </li>
                    <li style="margin-bottom: 0.5rem;">
                        <a href="https://github.com/Liniyous/ElaWidgetTools" target="_blank" rel="noopener"
                           style="color: var(--text-secondary); text-decoration: none;">
                            🎨 ElaWidgetTools
                        </a>
                    </li>
                    <li style="margin-bottom: 0.5rem;">
                        <a href="https://cmake.org/" target="_blank" rel="noopener"
                           style="color: var(--text-secondary); text-decoration: none;">
                            🔧 CMake Build System
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Copyright and Legal -->
        <div style="border-top: 1px solid var(--border-color); padding-top: 1.5rem; text-align: center;">
            <p style="margin: 0; color: var(--text-muted); font-size: 0.9rem;">
                © 2024 PDF Viewer Team. All rights reserved. |
                <a href="https://github.com/your-org/qt-pdf-render/blob/main/LICENSE" target="_blank" rel="noopener"
                   style="color: var(--text-muted); text-decoration: none;">License</a> |
                <a href="https://github.com/your-org/qt-pdf-render/blob/main/PRIVACY.md" target="_blank" rel="noopener"
                   style="color: var(--text-muted); text-decoration: none;">Privacy Policy</a>
            </p>
            <p style="margin: 0.5rem 0 0 0; color: var(--text-muted); font-size: 0.8rem;">
                Documentation generated with ❤️ using 
                <a href="https://www.doxygen.org/" target="_blank" rel="noopener"
                   style="color: var(--text-muted);">Doxygen</a>
            </p>
        </div>
    </div>
</footer>

<!-- Back to top button -->
<button id="backToTop" onclick="scrollToTop()" style="
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 18px;
    cursor: pointer;
    box-shadow: var(--box-shadow-lg);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
" title="Back to top" aria-label="Back to top">
    ↑
</button>

<script>
// Set build date
document.getElementById('buildDate').textContent = new Date().toLocaleDateString();

// Back to top functionality
window.addEventListener('scroll', function() {
    const backToTop = document.getElementById('backToTop');
    if (window.pageYOffset > 300) {
        backToTop.style.opacity = '1';
        backToTop.style.visibility = 'visible';
    } else {
        backToTop.style.opacity = '0';
        backToTop.style.visibility = 'hidden';
    }
});

function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// Enhanced link handling for external links
document.addEventListener('DOMContentLoaded', function() {
    // Add external link indicators
    const externalLinks = document.querySelectorAll('a[href^="http"]:not([href*="' + window.location.hostname + '"])');
    externalLinks.forEach(function(link) {
        if (!link.querySelector('.external-icon')) {
            link.innerHTML += ' <span class="external-icon" style="font-size: 0.8em; opacity: 0.7;">↗</span>';
        }
    });
    
    // Add smooth scrolling for internal links
    const internalLinks = document.querySelectorAll('a[href^="#"]');
    internalLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                e.preventDefault();
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + Home for back to top
    if ((e.ctrlKey || e.metaKey) && e.key === 'Home') {
        e.preventDefault();
        scrollToTop();
    }
    
    // Alt + F for search focus
    if (e.altKey && e.key === 'f') {
        e.preventDefault();
        const searchField = document.getElementById('MSearchField');
        if (searchField) {
            searchField.focus();
        }
    }
});

// Print optimization
window.addEventListener('beforeprint', function() {
    // Hide interactive elements when printing
    const interactiveElements = document.querySelectorAll('#backToTop, #themeToggle, .loading-overlay');
    interactiveElements.forEach(function(element) {
        element.style.display = 'none';
    });
});

window.addEventListener('afterprint', function() {
    // Restore interactive elements after printing
    const interactiveElements = document.querySelectorAll('#backToTop, #themeToggle');
    interactiveElements.forEach(function(element) {
        element.style.display = '';
    });
});

// Performance monitoring (optional)
if ('performance' in window) {
    window.addEventListener('load', function() {
        setTimeout(function() {
            const perfData = performance.getEntriesByType('navigation')[0];
            if (perfData && perfData.loadEventEnd > 0) {
                console.log('Page load time:', Math.round(perfData.loadEventEnd - perfData.fetchStart), 'ms');
            }
        }, 0);
    });
}
</script>

</body>
</html>
