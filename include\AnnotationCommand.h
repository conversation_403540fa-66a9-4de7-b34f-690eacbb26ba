#ifndef ANNOTATIONCOMMAND_H
#define ANNOTATIONCOMMAND_H

#include <QObject>
#include <QUndoCommand>
#include <QJsonObject>
#include <QRectF>
#include <QPointF>
#include <QColor>
#include <memory>
#include "Annotation.h"

class AnnotationManager;

// Base class for all annotation commands
class AnnotationCommand : public QUndoCommand
{
public:
    explicit AnnotationCommand(AnnotationManager* manager, QUndoCommand* parent = nullptr);
    virtual ~AnnotationCommand() = default;

protected:
    AnnotationManager* m_annotationManager;
};

// Command for creating annotations
class CreateAnnotationCommand : public AnnotationCommand
{
public:
    CreateAnnotationCommand(AnnotationManager* manager, std::unique_ptr<Annotation> annotation, QUndoCommand* parent = nullptr);
    
    void undo() override;
    void redo() override;

private:
    std::unique_ptr<Annotation> m_annotation;
    QString m_annotationId;
    bool m_isFirstRedo = true;
};

// Command for deleting annotations
class DeleteAnnotationCommand : public AnnotationCommand
{
public:
    DeleteAnnotationCommand(AnnotationManager* manager, const QString& annotationId, QUndoCommand* parent = nullptr);
    
    void undo() override;
    void redo() override;

private:
    QString m_annotationId;
    QJsonObject m_annotationData;
    std::unique_ptr<Annotation> m_annotation;
};

// Command for moving annotations
class MoveAnnotationCommand : public AnnotationCommand
{
public:
    MoveAnnotationCommand(AnnotationManager* manager, const QString& annotationId, 
                         const QPointF& oldPosition, const QPointF& newPosition, QUndoCommand* parent = nullptr);
    
    void undo() override;
    void redo() override;

private:
    QString m_annotationId;
    QPointF m_oldPosition;
    QPointF m_newPosition;
};

// Command for resizing annotations
class ResizeAnnotationCommand : public AnnotationCommand
{
public:
    ResizeAnnotationCommand(AnnotationManager* manager, const QString& annotationId, 
                           const QRectF& oldBounds, const QRectF& newBounds, QUndoCommand* parent = nullptr);
    
    void undo() override;
    void redo() override;

private:
    QString m_annotationId;
    QRectF m_oldBounds;
    QRectF m_newBounds;
};

// Command for changing annotation properties
class ChangeAnnotationPropertyCommand : public AnnotationCommand
{
public:
    enum class PropertyType {
        Color,
        Opacity,
        LineWidth,
        Content,
        Font
    };
    
    ChangeAnnotationPropertyCommand(AnnotationManager* manager, const QString& annotationId, 
                                   PropertyType propertyType, const QVariant& oldValue, 
                                   const QVariant& newValue, QUndoCommand* parent = nullptr);
    
    void undo() override;
    void redo() override;

private:
    QString m_annotationId;
    PropertyType m_propertyType;
    QVariant m_oldValue;
    QVariant m_newValue;
    
    void applyProperty(const QVariant& value);
};

// Macro command for batch operations
class BatchAnnotationCommand : public AnnotationCommand
{
public:
    BatchAnnotationCommand(AnnotationManager* manager, const QString& text, QUndoCommand* parent = nullptr);
    ~BatchAnnotationCommand();

    void addCommand(std::unique_ptr<AnnotationCommand> command);

    void undo() override;
    void redo() override;

private:
    QList<AnnotationCommand*> m_commands;
};

// Helper function
QString getAnnotationTypeName(AnnotationType type);

#endif // ANNOTATIONCOMMAND_H
