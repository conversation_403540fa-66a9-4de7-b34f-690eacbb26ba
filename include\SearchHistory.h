#ifndef SEARCHHISTORY_H
#define SEARCHHISTORY_H

#include <QObject>
#include <QStringList>
#include <QDateTime>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QSettings>

/**
 * @brief Search history entry containing search parameters and metadata
 */
struct SearchHistoryEntry {
    QString searchTerm;
    bool caseSensitive = false;
    bool wholeWords = false;
    QDateTime timestamp;
    int resultCount = 0;
    QString documentPath;
    
    SearchHistoryEntry() : timestamp(QDateTime::currentDateTime()) {}
    
    SearchHistoryEntry(const QString& term, bool caseSens, bool wholeW, 
                      int results = 0, const QString& docPath = QString())
        : searchTerm(term)
        , caseSensitive(caseSens)
        , wholeWords(wholeW)
        , timestamp(QDateTime::currentDateTime())
        , resultCount(results)
        , documentPath(docPath) {}
    
    // Serialization
    QJsonObject toJson() const;
    static SearchHistoryEntry fromJson(const QJsonObject& json);
    
    // Comparison for uniqueness
    bool operator==(const SearchHistoryEntry& other) const {
        return searchTerm == other.searchTerm && 
               caseSensitive == other.caseSensitive && 
               wholeWords == other.wholeWords &&
               documentPath == other.documentPath;
    }
};

/**
 * @brief Manages search history with persistence and filtering capabilities
 */
class SearchHistory : public QObject
{
    Q_OBJECT

public:
    explicit SearchHistory(QObject *parent = nullptr);
    ~SearchHistory();

    // History management
    void addSearch(const QString& searchTerm, bool caseSensitive, bool wholeWords, 
                   int resultCount = 0, const QString& documentPath = QString());
    void addSearch(const SearchHistoryEntry& entry);
    
    // Retrieval
    QList<SearchHistoryEntry> getHistory() const;
    QList<SearchHistoryEntry> getRecentHistory(int maxCount = 10) const;
    QList<SearchHistoryEntry> getHistoryForDocument(const QString& documentPath) const;
    QStringList getRecentSearchTerms(int maxCount = 10) const;
    QStringList getPopularSearchTerms(int maxCount = 10) const;
    
    // Search and filtering
    QList<SearchHistoryEntry> searchHistory(const QString& query) const;
    QList<SearchHistoryEntry> getHistoryByDateRange(const QDateTime& from, const QDateTime& to) const;
    
    // Statistics
    int getHistoryCount() const;
    int getSearchCount(const QString& searchTerm) const;
    QDateTime getLastSearchTime() const;
    QStringList getMostSearchedTerms(int maxCount = 5) const;
    
    // Management
    void clearHistory();
    void removeEntry(int index);
    void removeEntriesForDocument(const QString& documentPath);
    void removeOldEntries(int daysOld = 30);
    
    // Settings
    void setMaxHistorySize(int maxSize);
    int getMaxHistorySize() const;
    void setAutoSaveEnabled(bool enabled);
    bool isAutoSaveEnabled() const;
    
    // Persistence
    void saveToSettings();
    void loadFromSettings();
    void exportToFile(const QString& filePath) const;
    void importFromFile(const QString& filePath);

signals:
    void historyChanged();
    void entryAdded(const SearchHistoryEntry& entry);
    void entryRemoved(int index);
    void historyCleared();

private:
    void trimHistory();
    void removeDuplicates();
    QString getSettingsKey() const;
    
    QList<SearchHistoryEntry> m_history;
    int m_maxHistorySize;
    bool m_autoSaveEnabled;
    QSettings* m_settings;
    
    static const int DEFAULT_MAX_HISTORY_SIZE = 100;
    static const QString SETTINGS_GROUP;
    static const QString HISTORY_KEY;
};

// Make SearchHistoryEntry available to QVariant system
Q_DECLARE_METATYPE(SearchHistoryEntry)

#endif // SEARCHHISTORY_H
