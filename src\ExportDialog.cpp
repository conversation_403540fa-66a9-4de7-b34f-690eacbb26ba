#include "ExportDialog.h"
#include <QFileDialog>
#include <QMessageBox>
#include <QApplication>
#include <QStandardPaths>
#include <QDir>
#include <QRegularExpression>
#include <QPainter>

ExportDialog::ExportDialog(PdfController* controller, ExportType type, int currentPage, int totalPages, QWidget *parent)
    : QDialog(parent)
    , m_controller(controller)
    , m_exportType(type)
    , m_currentPage(currentPage)
    , m_totalPages(totalPages)
    , m_progressDialog(nullptr)
{
    setWindowTitle(tr("Export Pages"));
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);
    resize(500, 450);
    
    setupUi();
    
    // Set default directory to Pictures
    QString defaultDir = QStandardPaths::writableLocation(QStandardPaths::PicturesLocation);
    m_directoryEdit->setText(defaultDir);
    
    updateFileNamePreview();
}

void ExportDialog::setupUi()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(12);
    m_mainLayout->setContentsMargins(16, 16, 16, 16);

    // Export type group
    m_typeGroup = new QGroupBox(tr("Export Type"), this);
    QVBoxLayout* typeLayout = new QVBoxLayout(m_typeGroup);
    
    QString typeText;
    switch (m_exportType) {
        case CurrentPage:
            typeText = tr("Current Page (Page %1)").arg(m_currentPage + 1);
            break;
        case AllPages:
            typeText = tr("All Pages (%1 pages)").arg(m_totalPages);
            break;
        case PageRange:
            typeText = tr("Page Range");
            break;
    }
    
    m_typeLabel = new QLabel(typeText, this);
    m_typeLabel->setStyleSheet("QLabel { font-weight: bold; color: #333; }");
    typeLayout->addWidget(m_typeLabel);
    
    m_mainLayout->addWidget(m_typeGroup);

    // Page range group (only for PageRange type)
    if (m_exportType == PageRange) {
        m_rangeGroup = new QGroupBox(tr("Page Range"), this);
        QVBoxLayout* rangeLayout = new QVBoxLayout(m_rangeGroup);
        
        m_rangeEdit = new QLineEdit(this);
        m_rangeEdit->setPlaceholderText(tr("e.g., 1-5, 8, 10-12"));
        m_rangeEdit->setText(QString("1-%1").arg(m_totalPages));
        connect(m_rangeEdit, &QLineEdit::textChanged, this, &ExportDialog::onPageRangeChanged);
        rangeLayout->addWidget(m_rangeEdit);
        
        m_rangeHelpLabel = new QLabel(tr("Enter page numbers separated by commas. Use hyphens for ranges."), this);
        m_rangeHelpLabel->setStyleSheet("QLabel { color: #666; font-size: 10px; }");
        rangeLayout->addWidget(m_rangeHelpLabel);
        
        m_mainLayout->addWidget(m_rangeGroup);
    }

    // Format settings group
    m_formatGroup = new QGroupBox(tr("Format Settings"), this);
    QGridLayout* formatLayout = new QGridLayout(m_formatGroup);
    
    formatLayout->addWidget(new QLabel(tr("Format:")), 0, 0);
    m_formatCombo = new QComboBox(this);
    m_formatCombo->addItems({"PNG", "JPEG", "TIFF", "BMP"});
    m_formatCombo->setCurrentText("PNG");
    connect(m_formatCombo, &QComboBox::currentTextChanged, this, &ExportDialog::onFormatChanged);
    formatLayout->addWidget(m_formatCombo, 0, 1);
    
    formatLayout->addWidget(new QLabel(tr("Quality:")), 1, 0);
    m_qualitySlider = new QSlider(Qt::Horizontal, this);
    m_qualitySlider->setRange(1, 100);
    m_qualitySlider->setValue(95);
    connect(m_qualitySlider, &QSlider::valueChanged, this, &ExportDialog::onQualityChanged);
    formatLayout->addWidget(m_qualitySlider, 1, 1);
    
    m_qualityLabel = new QLabel("95%", this);
    formatLayout->addWidget(m_qualityLabel, 1, 2);
    
    formatLayout->addWidget(new QLabel(tr("DPI:")), 2, 0);
    m_dpiSpinBox = new QSpinBox(this);
    m_dpiSpinBox->setRange(72, 600);
    m_dpiSpinBox->setValue(300);
    m_dpiSpinBox->setSuffix(" dpi");
    connect(m_dpiSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &ExportDialog::onDpiChanged);
    formatLayout->addWidget(m_dpiSpinBox, 2, 1);
    
    m_dpiLabel = new QLabel(tr("High quality"), this);
    m_dpiLabel->setStyleSheet("QLabel { color: #666; font-size: 10px; }");
    formatLayout->addWidget(m_dpiLabel, 2, 2);
    
    m_transparentBackgroundCheck = new QCheckBox(tr("Transparent background (PNG only)"), this);
    formatLayout->addWidget(m_transparentBackgroundCheck, 3, 0, 1, 3);
    
    m_mainLayout->addWidget(m_formatGroup);

    // Output settings group
    m_outputGroup = new QGroupBox(tr("Output Settings"), this);
    QGridLayout* outputLayout = new QGridLayout(m_outputGroup);
    
    outputLayout->addWidget(new QLabel(tr("Directory:")), 0, 0);
    m_directoryEdit = new QLineEdit(this);
    outputLayout->addWidget(m_directoryEdit, 0, 1);
    
    m_browseButton = new QPushButton(tr("Browse..."), this);
    connect(m_browseButton, &QPushButton::clicked, this, &ExportDialog::onBrowseClicked);
    outputLayout->addWidget(m_browseButton, 0, 2);
    
    outputLayout->addWidget(new QLabel(tr("File name:")), 1, 0);
    m_fileNameEdit = new QLineEdit(this);
    if (m_exportType == CurrentPage) {
        m_fileNameEdit->setText(QString("page_%1").arg(m_currentPage + 1, 3, 10, QChar('0')));
    } else {
        m_fileNameEdit->setText("page_###");
    }
    connect(m_fileNameEdit, &QLineEdit::textChanged, this, &ExportDialog::updateFileNamePreview);
    outputLayout->addWidget(m_fileNameEdit, 1, 1, 1, 2);
    
    m_previewLabel = new QLabel(this);
    m_previewLabel->setStyleSheet("QLabel { color: #666; font-size: 10px; margin-top: 4px; }");
    outputLayout->addWidget(m_previewLabel, 2, 0, 1, 3);
    
    m_mainLayout->addWidget(m_outputGroup);

    // Buttons
    m_buttonLayout = new QHBoxLayout();
    m_buttonLayout->addStretch();
    
    m_exportButton = new QPushButton(tr("Export"), this);
    m_exportButton->setDefault(true);
    m_exportButton->setStyleSheet(
        "QPushButton { "
        "    padding: 8px 16px; "
        "    border: 1px solid #28a745; "
        "    border-radius: 4px; "
        "    background: #28a745; "
        "    color: white; "
        "    font-weight: bold; "
        "} "
        "QPushButton:hover { "
        "    background: #218838; "
        "    border: 1px solid #218838; "
        "} "
        "QPushButton:pressed { "
        "    background: #1e7e34; "
        "}"
    );
    connect(m_exportButton, &QPushButton::clicked, this, &ExportDialog::onExportClicked);
    
    m_cancelButton = new QPushButton(tr("Cancel"), this);
    m_cancelButton->setStyleSheet(
        "QPushButton { "
        "    padding: 8px 16px; "
        "    border: 1px solid #6c757d; "
        "    border-radius: 4px; "
        "    background: #6c757d; "
        "    color: white; "
        "} "
        "QPushButton:hover { "
        "    background: #545b62; "
        "    border: 1px solid #545b62; "
        "}"
    );
    connect(m_cancelButton, &QPushButton::clicked, this, &QDialog::reject);
    
    m_buttonLayout->addWidget(m_exportButton);
    m_buttonLayout->addWidget(m_cancelButton);
    
    m_mainLayout->addLayout(m_buttonLayout);
    
    // Initial updates
    onFormatChanged();
    updateQualityLabel();
    updateDpiLabel();
}

void ExportDialog::onFormatChanged()
{
    QString format = m_formatCombo->currentText();
    
    // Enable/disable quality slider based on format
    bool supportsQuality = (format == "JPEG");
    m_qualitySlider->setEnabled(supportsQuality);
    m_qualityLabel->setEnabled(supportsQuality);
    
    // Enable/disable transparent background based on format
    bool supportsTransparency = (format == "PNG");
    m_transparentBackgroundCheck->setEnabled(supportsTransparency);
    
    updateFileNamePreview();
}

void ExportDialog::onQualityChanged(int value)
{
    Q_UNUSED(value);
    updateQualityLabel();
}

void ExportDialog::onDpiChanged(int value)
{
    Q_UNUSED(value);
    updateDpiLabel();
}

void ExportDialog::onBrowseClicked()
{
    QString dir = QFileDialog::getExistingDirectory(this, tr("Select Export Directory"), m_directoryEdit->text());
    if (!dir.isEmpty()) {
        m_directoryEdit->setText(dir);
        updateFileNamePreview();
    }
}

void ExportDialog::onExportClicked()
{
    if (m_exportType == PageRange && !validatePageRange()) {
        return;
    }
    
    QString directory = m_directoryEdit->text();
    if (directory.isEmpty() || !QDir(directory).exists()) {
        QMessageBox::warning(this, tr("Invalid Directory"), tr("Please select a valid export directory."));
        return;
    }
    
    performExport();
}

void ExportDialog::onPageRangeChanged()
{
    updateFileNamePreview();
}

void ExportDialog::updateQualityLabel()
{
    m_qualityLabel->setText(QString("%1%").arg(m_qualitySlider->value()));
}

void ExportDialog::updateDpiLabel()
{
    int dpi = m_dpiSpinBox->value();
    QString quality;
    if (dpi <= 150) quality = tr("Standard quality");
    else if (dpi <= 300) quality = tr("High quality");
    else quality = tr("Very high quality");
    
    m_dpiLabel->setText(quality);
}

void ExportDialog::updateFileNamePreview()
{
    QString format = m_formatCombo->currentText().toLower();
    QString fileName = m_fileNameEdit->text();
    QString directory = m_directoryEdit->text();
    
    QString preview;
    if (m_exportType == CurrentPage) {
        preview = QString("%1/%2.%3").arg(directory).arg(fileName).arg(format);
    } else {
        QString example = fileName;
        example.replace("###", "001");
        preview = tr("Example: %1/%2.%3").arg(directory).arg(example).arg(format);
    }
    
    m_previewLabel->setText(preview);
}

bool ExportDialog::validatePageRange()
{
    if (m_exportType != PageRange) return true;

    QString range = m_rangeEdit->text().trimmed();
    if (range.isEmpty()) {
        QMessageBox::warning(this, tr("Invalid Range"), tr("Please enter a page range."));
        return false;
    }

    QList<int> pages = parsePageRange(range);
    if (pages.isEmpty()) {
        QMessageBox::warning(this, tr("Invalid Range"), tr("Invalid page range format."));
        return false;
    }

    return true;
}

QList<int> ExportDialog::parsePageRange(const QString& range)
{
    QList<int> pages;
    QStringList parts = range.split(',', Qt::SkipEmptyParts);

    for (const QString& part : parts) {
        QString trimmed = part.trimmed();

        if (trimmed.contains('-')) {
            // Range like "1-5"
            QStringList rangeParts = trimmed.split('-');
            if (rangeParts.size() == 2) {
                bool ok1, ok2;
                int start = rangeParts[0].trimmed().toInt(&ok1);
                int end = rangeParts[1].trimmed().toInt(&ok2);

                if (ok1 && ok2 && start >= 1 && end <= m_totalPages && start <= end) {
                    for (int i = start; i <= end; ++i) {
                        if (!pages.contains(i - 1)) { // Convert to 0-based
                            pages.append(i - 1);
                        }
                    }
                }
            }
        } else {
            // Single page like "3"
            bool ok;
            int page = trimmed.toInt(&ok);
            if (ok && page >= 1 && page <= m_totalPages) {
                if (!pages.contains(page - 1)) { // Convert to 0-based
                    pages.append(page - 1);
                }
            }
        }
    }

    std::sort(pages.begin(), pages.end());
    return pages;
}

void ExportDialog::performExport()
{
    QList<int> pagesToExport;

    switch (m_exportType) {
        case CurrentPage:
            pagesToExport.append(m_currentPage);
            break;
        case AllPages:
            for (int i = 0; i < m_totalPages; ++i) {
                pagesToExport.append(i);
            }
            break;
        case PageRange:
            pagesToExport = parsePageRange(m_rangeEdit->text());
            break;
    }

    if (pagesToExport.isEmpty()) {
        QMessageBox::warning(this, tr("No Pages"), tr("No pages to export."));
        return;
    }

    // Create progress dialog
    m_progressDialog = new QProgressDialog(tr("Exporting pages..."), tr("Cancel"), 0, pagesToExport.size(), this);
    m_progressDialog->setWindowModality(Qt::WindowModal);
    m_progressDialog->setMinimumDuration(0);

    QString directory = m_directoryEdit->text();
    QString baseFileName = m_fileNameEdit->text();
    QString format = m_formatCombo->currentText().toLower();

    int exportedCount = 0;
    for (int i = 0; i < pagesToExport.size(); ++i) {
        m_progressDialog->setValue(i);
        if (m_progressDialog->wasCanceled()) {
            break;
        }

        int pageNum = pagesToExport[i];
        QString fileName;

        if (m_exportType == CurrentPage) {
            fileName = QString("%1.%2").arg(baseFileName).arg(format);
        } else {
            QString pageFileName = baseFileName;
            pageFileName.replace("###", QString("%1").arg(pageNum + 1, 3, 10, QChar('0')));
            fileName = QString("%1.%2").arg(pageFileName).arg(format);
        }

        QString fullPath = QDir(directory).filePath(fileName);

        try {
            exportPage(pageNum, fullPath);
            exportedCount++;
        } catch (const std::exception& e) {
            QMessageBox::warning(this, tr("Export Error"),
                tr("Failed to export page %1: %2").arg(pageNum + 1).arg(e.what()));
        }

        QApplication::processEvents();
    }

    m_progressDialog->setValue(pagesToExport.size());
    delete m_progressDialog;
    m_progressDialog = nullptr;

    if (exportedCount > 0) {
        QMessageBox::information(this, tr("Export Complete"),
            tr("Successfully exported %1 page(s) to %2").arg(exportedCount).arg(directory));
        accept();
    }
}

void ExportDialog::exportPage(int pageNumber, const QString& filePath)
{
    if (!m_controller) {
        throw std::runtime_error("PDF controller not available");
    }

    double dpi = m_dpiSpinBox->value();
    QImage image = m_controller->renderPageToImage(pageNumber, dpi);

    if (image.isNull()) {
        throw std::runtime_error("Failed to render page");
    }

    // Apply format-specific settings
    QString format = m_formatCombo->currentText();
    int quality = -1;

    if (format == "JPEG") {
        quality = m_qualitySlider->value();
        // Convert to RGB if JPEG (no alpha channel)
        if (image.hasAlphaChannel()) {
            QImage rgbImage(image.size(), QImage::Format_RGB888);
            rgbImage.fill(Qt::white);
            QPainter painter(&rgbImage);
            painter.drawImage(0, 0, image);
            image = rgbImage;
        }
    } else if (format == "PNG" && m_transparentBackgroundCheck->isChecked()) {
        // Keep alpha channel for transparent background
    }

    if (!image.save(filePath, format.toUtf8().constData(), quality)) {
        throw std::runtime_error("Failed to save image file");
    }
}
