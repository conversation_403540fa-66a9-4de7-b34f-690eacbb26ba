#include <QtTest/QtTest>
#include <QSignalSpy>
#include <QPixmap>
#include <QTemporaryFile>
#include <QStandardPaths>
#include <QDir>

#include "PdfController.h"
#include "AnnotationManager.h"

class TestPdfController : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Basic functionality tests
    void testControllerCreation();
    void testLoadDocument();
    void testLoadInvalidDocument();
    void testPageCount();
    void testPageSize();
    void testCacheOperations();
    void testMemoryUsage();

    // Page rendering tests
    void testRequestPage();
    void testRequestPreview();
    void testRequestPageWithAnnotations();
    void testPreloadAdjacentPages();

    // Search functionality tests
    void testSearchText();
    void testSearchCaseSensitive();
    void testGetPageText();

    // Outline tests
    void testGetDocumentOutline();

    // Annotation integration tests
    void testAnnotationManager();
    void testRenderWithAnnotations();

    // Performance tests
    void testCachePerformance();
    void testConcurrentRequests();

    // Signal tests
    void testDocumentLoadedSignal();
    void testPageReadySignal();
    void testSearchSignals();

private:
    PdfController* m_controller;
    AnnotationManager* m_annotationManager;
    QString m_testPdfPath;
    
    // Signal spies
    QSignalSpy* m_documentLoadedSpy;
    QSignalSpy* m_pageReadySpy;
    QSignalSpy* m_searchCompletedSpy;
    QSignalSpy* m_memoryUsageSpy;
    
    void createTestPdf();
    void waitForSignal(QSignalSpy* spy, int timeout = 5000);
};

void TestPdfController::initTestCase()
{
    // Create a simple test PDF file
    createTestPdf();
}

void TestPdfController::cleanupTestCase()
{
    // Clean up test files
    if (!m_testPdfPath.isEmpty() && QFile::exists(m_testPdfPath)) {
        QFile::remove(m_testPdfPath);
    }
}

void TestPdfController::init()
{
    m_controller = new PdfController(this);
    m_annotationManager = new AnnotationManager(this);
    m_controller->setAnnotationManager(m_annotationManager);
    
    // Setup signal spies
    m_documentLoadedSpy = new QSignalSpy(m_controller, &PdfController::documentLoaded);
    m_pageReadySpy = new QSignalSpy(m_controller, &PdfController::pageReady);
    m_searchCompletedSpy = new QSignalSpy(m_controller, &PdfController::searchCompleted);
    m_memoryUsageSpy = new QSignalSpy(m_controller, &PdfController::memoryUsageChanged);
}

void TestPdfController::cleanup()
{
    delete m_documentLoadedSpy;
    delete m_pageReadySpy;
    delete m_searchCompletedSpy;
    delete m_memoryUsageSpy;
    delete m_controller;
    delete m_annotationManager;
}

void TestPdfController::testControllerCreation()
{
    QVERIFY(m_controller != nullptr);
    QVERIFY(!m_controller->pageCount().has_value());
    QCOMPARE(m_controller->getCacheMemoryUsage(), 0);
    QCOMPARE(m_controller->getCachedPagesCount(), 0);
}

void TestPdfController::testLoadDocument()
{
    // Skip if no test PDF available
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    QCOMPARE(m_documentLoadedSpy->count(), 1);
    QList<QVariant> arguments = m_documentLoadedSpy->takeFirst();
    bool success = arguments.at(0).toBool();
    QString errorString = arguments.at(1).toString();
    
    QVERIFY(success);
    QVERIFY(errorString.isEmpty());
    QVERIFY(m_controller->pageCount().has_value());
    QVERIFY(m_controller->pageCount().value() > 0);
}

void TestPdfController::testLoadInvalidDocument()
{
    QString invalidPath = "/path/to/nonexistent.pdf";
    
    m_controller->loadDocument(invalidPath);
    waitForSignal(m_documentLoadedSpy);
    
    QCOMPARE(m_documentLoadedSpy->count(), 1);
    QList<QVariant> arguments = m_documentLoadedSpy->takeFirst();
    bool success = arguments.at(0).toBool();
    QString errorString = arguments.at(1).toString();
    
    QVERIFY(!success);
    QVERIFY(!errorString.isEmpty());
    QVERIFY(!m_controller->pageCount().has_value());
}

void TestPdfController::testPageCount()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    // Before loading document
    QVERIFY(!m_controller->pageCount().has_value());
    
    // Load document
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    // After loading document
    QVERIFY(m_controller->pageCount().has_value());
    int pageCount = m_controller->pageCount().value();
    QVERIFY(pageCount > 0);
    QVERIFY(pageCount <= 100); // Reasonable upper bound for test PDF
}

void TestPdfController::testPageSize()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    if (m_controller->pageCount().has_value() && m_controller->pageCount().value() > 0) {
        QSizeF pageSize = m_controller->getPageSize(0);
        QVERIFY(pageSize.width() > 0);
        QVERIFY(pageSize.height() > 0);
        
        // Test invalid page number
        QSizeF invalidSize = m_controller->getPageSize(-1);
        QVERIFY(invalidSize.isEmpty());
        
        invalidSize = m_controller->getPageSize(1000);
        QVERIFY(invalidSize.isEmpty());
    }
}

void TestPdfController::testCacheOperations()
{
    // Initial cache should be empty
    QCOMPARE(m_controller->getCacheMemoryUsage(), 0);
    QCOMPARE(m_controller->getCachedPagesCount(), 0);
    
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    // Request a page to populate cache
    m_controller->requestPage(0, 1.0);
    waitForSignal(m_pageReadySpy);
    
    // Cache should now have some content
    QVERIFY(m_controller->getCacheMemoryUsage() > 0);
    QVERIFY(m_controller->getCachedPagesCount() > 0);
    
    // Clear cache
    m_controller->clearCache();
    
    // Cache should be empty again
    QCOMPARE(m_controller->getCacheMemoryUsage(), 0);
    QCOMPARE(m_controller->getCachedPagesCount(), 0);
}

void TestPdfController::testMemoryUsage()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    qint64 initialMemory = m_controller->getCacheMemoryUsage();
    
    // Request multiple pages
    for (int i = 0; i < qMin(3, m_controller->pageCount().value_or(0)); ++i) {
        m_controller->requestPage(i, 1.0);
        waitForSignal(m_pageReadySpy);
    }
    
    qint64 finalMemory = m_controller->getCacheMemoryUsage();
    
    // Memory usage should have increased
    QVERIFY(finalMemory >= initialMemory);
    
    // Memory usage should be reasonable (less than 100MB for test)
    QVERIFY(finalMemory < 100 * 1024 * 1024);
}

void TestPdfController::testRequestPage()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    if (m_controller->pageCount().has_value() && m_controller->pageCount().value() > 0) {
        m_controller->requestPage(0, 1.0);
        waitForSignal(m_pageReadySpy);
        
        QCOMPARE(m_pageReadySpy->count(), 1);
        QList<QVariant> arguments = m_pageReadySpy->takeFirst();
        int pageNum = arguments.at(0).toInt();
        QPixmap pixmap = arguments.at(1).value<QPixmap>();
        bool isHighQuality = arguments.at(2).toBool();
        
        QCOMPARE(pageNum, 0);
        QVERIFY(!pixmap.isNull());
        QVERIFY(pixmap.width() > 0);
        QVERIFY(pixmap.height() > 0);
        QVERIFY(isHighQuality);
    }
}

void TestPdfController::testRequestPreview()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    if (m_controller->pageCount().has_value() && m_controller->pageCount().value() > 0) {
        m_controller->requestPreview(0);
        waitForSignal(m_pageReadySpy);
        
        QCOMPARE(m_pageReadySpy->count(), 1);
        QList<QVariant> arguments = m_pageReadySpy->takeFirst();
        int pageNum = arguments.at(0).toInt();
        QPixmap pixmap = arguments.at(1).value<QPixmap>();
        bool isHighQuality = arguments.at(2).toBool();
        
        QCOMPARE(pageNum, 0);
        QVERIFY(!pixmap.isNull());
        QVERIFY(!isHighQuality); // Preview should be low quality
    }
}

void TestPdfController::testRequestPageWithAnnotations()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    // Add a test annotation
    auto highlight = std::make_unique<HighlightAnnotation>();
    highlight->setPageNumber(0);
    highlight->addQuad(QRectF(10, 20, 100, 15));
    m_annotationManager->addAnnotation(std::move(highlight));
    
    if (m_controller->pageCount().has_value() && m_controller->pageCount().value() > 0) {
        QSignalSpy annotationPageSpy(m_controller, &PdfController::pageWithAnnotationsReady);
        
        m_controller->requestPageWithAnnotations(0, 1.0);
        waitForSignal(&annotationPageSpy);
        
        QCOMPARE(annotationPageSpy.count(), 1);
        QList<QVariant> arguments = annotationPageSpy.takeFirst();
        int pageNum = arguments.at(0).toInt();
        QPixmap pixmap = arguments.at(1).value<QPixmap>();
        
        QCOMPARE(pageNum, 0);
        QVERIFY(!pixmap.isNull());
    }
}

void TestPdfController::testPreloadAdjacentPages()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    if (m_controller->pageCount().has_value() && m_controller->pageCount().value() > 2) {
        int initialCachedPages = m_controller->getCachedPagesCount();
        
        m_controller->preloadAdjacentPages(1, 1.0);
        
        // Wait a bit for preloading to complete
        QTest::qWait(1000);
        
        int finalCachedPages = m_controller->getCachedPagesCount();
        
        // Should have preloaded some pages
        QVERIFY(finalCachedPages >= initialCachedPages);
    }
}

void TestPdfController::testSearchText()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    // Perform search
    m_controller->performSearch("test", false);
    waitForSignal(m_searchCompletedSpy);
    
    QCOMPARE(m_searchCompletedSpy->count(), 1);
    QList<QVariant> arguments = m_searchCompletedSpy->takeFirst();
    QList<SearchResult> results = arguments.at(0).value<QList<SearchResult>>();
    
    // Results should be a valid list (may be empty if "test" not found)
    QVERIFY(results.size() >= 0);
}

void TestPdfController::testSearchCaseSensitive()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    // Search case-insensitive
    m_controller->performSearch("TEST", false);
    waitForSignal(m_searchCompletedSpy);
    QList<SearchResult> caseInsensitiveResults = 
        m_searchCompletedSpy->takeFirst().at(0).value<QList<SearchResult>>();
    
    // Search case-sensitive
    m_controller->performSearch("TEST", true);
    waitForSignal(m_searchCompletedSpy);
    QList<SearchResult> caseSensitiveResults = 
        m_searchCompletedSpy->takeFirst().at(0).value<QList<SearchResult>>();
    
    // Case-insensitive should find same or more results
    QVERIFY(caseInsensitiveResults.size() >= caseSensitiveResults.size());
}

void TestPdfController::testGetPageText()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    if (m_controller->pageCount().has_value() && m_controller->pageCount().value() > 0) {
        QString pageText = m_controller->getPageText(0);
        
        // Page text should be a valid string (may be empty for image-only PDFs)
        QVERIFY(!pageText.isNull());
        
        // Test invalid page number
        QString invalidText = m_controller->getPageText(-1);
        QVERIFY(invalidText.isEmpty());
        
        invalidText = m_controller->getPageText(1000);
        QVERIFY(invalidText.isEmpty());
    }
}

void TestPdfController::testGetDocumentOutline()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    QList<OutlineItem> outline = m_controller->getDocumentOutline();
    
    // Outline should be a valid list (may be empty if no outline)
    QVERIFY(outline.size() >= 0);
    
    // If outline exists, verify structure
    for (const OutlineItem& item : outline) {
        QVERIFY(!item.title.isEmpty());
        QVERIFY(item.pageNumber >= -1); // -1 is valid for items without page reference
        QVERIFY(item.level >= 0);
    }
}

void TestPdfController::testAnnotationManager()
{
    QCOMPARE(m_controller->getAnnotationManager(), m_annotationManager);
    
    // Test setting different annotation manager
    AnnotationManager* newManager = new AnnotationManager(this);
    m_controller->setAnnotationManager(newManager);
    QCOMPARE(m_controller->getAnnotationManager(), newManager);
    
    delete newManager;
}

void TestPdfController::testRenderWithAnnotations()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    // Add annotation
    auto highlight = std::make_unique<HighlightAnnotation>();
    highlight->setPageNumber(0);
    highlight->setColor(QColor(255, 255, 0, 128));
    highlight->addQuad(QRectF(10, 20, 100, 15));
    m_annotationManager->addAnnotation(std::move(highlight));
    
    if (m_controller->pageCount().has_value() && m_controller->pageCount().value() > 0) {
        QSignalSpy annotationPageSpy(m_controller, &PdfController::pageWithAnnotationsReady);
        
        m_controller->requestPageWithAnnotations(0, 1.0);
        waitForSignal(&annotationPageSpy);
        
        QCOMPARE(annotationPageSpy.count(), 1);
        
        // Also test regular page rendering for comparison
        m_controller->requestPage(0, 1.0);
        waitForSignal(m_pageReadySpy);
        
        QCOMPARE(m_pageReadySpy->count(), 1);
    }
}

void TestPdfController::testCachePerformance()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    if (m_controller->pageCount().has_value() && m_controller->pageCount().value() > 0) {
        // First request - should be slower (not cached)
        QElapsedTimer timer;
        timer.start();
        
        m_controller->requestPage(0, 1.0);
        waitForSignal(m_pageReadySpy);
        
        qint64 firstRequestTime = timer.elapsed();
        
        // Second request - should be faster (cached)
        timer.restart();
        
        m_controller->requestPage(0, 1.0);
        waitForSignal(m_pageReadySpy);
        
        qint64 secondRequestTime = timer.elapsed();
        
        // Second request should be significantly faster
        QVERIFY(secondRequestTime <= firstRequestTime);
        
        // Verify cache contains the page
        QVERIFY(m_controller->getCachedPagesCount() > 0);
    }
}

void TestPdfController::testConcurrentRequests()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    if (m_controller->pageCount().has_value() && m_controller->pageCount().value() > 2) {
        // Request multiple pages concurrently
        m_controller->requestPage(0, 1.0);
        m_controller->requestPage(1, 1.0);
        m_controller->requestPage(2, 1.0);
        
        // Wait for all requests to complete
        int expectedSignals = qMin(3, m_controller->pageCount().value());
        for (int i = 0; i < expectedSignals; ++i) {
            waitForSignal(m_pageReadySpy);
        }
        
        QCOMPARE(m_pageReadySpy->count(), expectedSignals);
    }
}

void TestPdfController::testDocumentLoadedSignal()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    QCOMPARE(m_documentLoadedSpy->count(), 1);
    
    QList<QVariant> arguments = m_documentLoadedSpy->takeFirst();
    bool success = arguments.at(0).toBool();
    QString errorString = arguments.at(1).toString();
    
    QVERIFY(success);
    QVERIFY(errorString.isEmpty());
}

void TestPdfController::testPageReadySignal()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    if (m_controller->pageCount().has_value() && m_controller->pageCount().value() > 0) {
        m_controller->requestPage(0, 1.5, 90); // Test with zoom and rotation
        waitForSignal(m_pageReadySpy);
        
        QCOMPARE(m_pageReadySpy->count(), 1);
        
        QList<QVariant> arguments = m_pageReadySpy->takeFirst();
        int pageNum = arguments.at(0).toInt();
        QPixmap pixmap = arguments.at(1).value<QPixmap>();
        bool isHighQuality = arguments.at(2).toBool();
        
        QCOMPARE(pageNum, 0);
        QVERIFY(!pixmap.isNull());
        QVERIFY(isHighQuality);
        
        // Verify zoom was applied (pixmap should be larger than default)
        QVERIFY(pixmap.width() > 100); // Reasonable minimum for zoomed page
        QVERIFY(pixmap.height() > 100);
    }
}

void TestPdfController::testSearchSignals()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    QSignalSpy searchProgressSpy(m_controller, &PdfController::searchProgress);
    
    m_controller->performSearch("test", false);
    waitForSignal(m_searchCompletedSpy);
    
    QCOMPARE(m_searchCompletedSpy->count(), 1);
    
    // Search progress signals may or may not be emitted depending on document size
    QVERIFY(searchProgressSpy.count() >= 0);
}

void TestPdfController::createTestPdf()
{
    // For this test, we'll try to find an existing PDF or skip tests that require one
    // In a real test environment, you would create or provide a test PDF file
    
    QStringList possiblePaths = {
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/test.pdf",
        QDir::currentPath() + "/test.pdf",
        "test.pdf"
    };
    
    for (const QString& path : possiblePaths) {
        if (QFile::exists(path)) {
            m_testPdfPath = path;
            break;
        }
    }
    
    // If no test PDF found, tests will be skipped
    if (m_testPdfPath.isEmpty()) {
        qWarning() << "No test PDF file found. PDF-dependent tests will be skipped.";
    }
}

void TestPdfController::waitForSignal(QSignalSpy* spy, int timeout)
{
    if (spy->count() == 0) {
        spy->wait(timeout);
    }
}

QTEST_MAIN(TestPdfController)
#include "test_pdf_controller.moc"
