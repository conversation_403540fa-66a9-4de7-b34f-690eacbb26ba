#include "DocumentInfoDialog.h"
#include "PdfController.h"
#include <QFileInfo>
#include <QDateTime>
#include <QClipboard>
#include <QApplication>
#include <QMessageBox>
#include <QHeaderView>
#include <QSplitter>
#include <poppler-qt6.h>

DocumentInfoDialog::DocumentInfoDialog(PdfController* controller, const QString& filePath, QWidget *parent)
    : ElaContentDialog(parent)
    , m_controller(controller)
    , m_filePath(filePath)
{
    setupUi();
    populateInfo();

    // Configure ElaContentDialog buttons
    setLeftButtonText(tr("Copy"));
    setRightButtonText(tr("Close"));

    // Connect dialog buttons
    connect(this, &ElaContentDialog::leftButtonClicked, this, &DocumentInfoDialog::copyToClipboard);
    connect(this, &ElaContentDialog::rightButtonClicked, this, &DocumentInfoDialog::accept);

    resize(700, 600);
}

void DocumentInfoDialog::setupUi()
{
    // Create main content widget for ElaContentDialog
    QWidget* mainWidget = new QWidget();
    m_mainLayout = new QVBoxLayout(mainWidget);

    // Create scroll area for content using Ela components
    m_scrollArea = new ElaScroll();
    m_scrollArea->setWidgetResizable(true);
    m_scrollArea->setFrameStyle(QFrame::NoFrame);

    m_contentWidget = new QWidget();
    m_gridLayout = new QGridLayout(m_contentWidget);
    m_gridLayout->setColumnStretch(1, 1);
    m_gridLayout->setVerticalSpacing(8);
    m_gridLayout->setHorizontalSpacing(12);
    
    // Document metadata section
    QGroupBox* metadataGroup = new QGroupBox(tr("Document Metadata"));
    QGridLayout* metadataLayout = new QGridLayout(metadataGroup);
    
    int row = 0;
    
    // Title
    metadataLayout->addWidget(new ElaText(tr("Title:"), this), row, 0);
    m_titleLabel = new ElaLabel();
    m_titleLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
    m_titleLabel->setWordWrap(true);
    metadataLayout->addWidget(m_titleLabel, row++, 1);

    // Author
    metadataLayout->addWidget(new ElaText(tr("Author:"), this), row, 0);
    m_authorLabel = new ElaLabel();
    m_authorLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
    m_authorLabel->setWordWrap(true);
    metadataLayout->addWidget(m_authorLabel, row++, 1);

    // Subject
    metadataLayout->addWidget(new ElaText(tr("Subject:"), this), row, 0);
    m_subjectLabel = new ElaLabel();
    m_subjectLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
    m_subjectLabel->setWordWrap(true);
    metadataLayout->addWidget(m_subjectLabel, row++, 1);

    // Keywords
    metadataLayout->addWidget(new ElaText(tr("Keywords:"), this), row, 0);
    m_keywordsLabel = new ElaLabel();
    m_keywordsLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
    m_keywordsLabel->setWordWrap(true);
    metadataLayout->addWidget(m_keywordsLabel, row++, 1);
    
    // Creator
    metadataLayout->addWidget(new ElaText(tr("Creator:"), this), row, 0);
    m_creatorLabel = new ElaLabel();
    m_creatorLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
    m_creatorLabel->setWordWrap(true);
    metadataLayout->addWidget(m_creatorLabel, row++, 1);

    // Producer
    metadataLayout->addWidget(new ElaText(tr("Producer:"), this), row, 0);
    m_producerLabel = new ElaLabel();
    m_producerLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
    m_producerLabel->setWordWrap(true);
    metadataLayout->addWidget(m_producerLabel, row++, 1);

    // Creation Date
    metadataLayout->addWidget(new ElaText(tr("Creation Date:"), this), row, 0);
    m_creationDateLabel = new ElaLabel();
    m_creationDateLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
    metadataLayout->addWidget(m_creationDateLabel, row++, 1);

    // Modification Date
    metadataLayout->addWidget(new ElaText(tr("Modification Date:"), this), row, 0);
    m_modificationDateLabel = new ElaLabel();
    m_modificationDateLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
    metadataLayout->addWidget(m_modificationDateLabel, row++, 1);
    
    m_gridLayout->addWidget(metadataGroup, 0, 0, 1, 2);
    
    // File information section
    QGroupBox* fileGroup = new QGroupBox(tr("File Information"));
    QGridLayout* fileLayout = new QGridLayout(fileGroup);
    
    row = 0;
    
    // File Path
    fileLayout->addWidget(new ElaText(tr("File Path:"), this), row, 0);
    m_filePathLabel = new ElaLabel();
    m_filePathLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
    m_filePathLabel->setWordWrap(true);
    fileLayout->addWidget(m_filePathLabel, row++, 1);

    // File Size
    fileLayout->addWidget(new ElaText(tr("File Size:"), this), row, 0);
    m_fileSizeLabel = new ElaLabel();
    m_fileSizeLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
    fileLayout->addWidget(m_fileSizeLabel, row++, 1);

    // Page Count
    fileLayout->addWidget(new ElaText(tr("Page Count:"), this), row, 0);
    m_pageCountLabel = new ElaLabel();
    m_pageCountLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
    fileLayout->addWidget(m_pageCountLabel, row++, 1);

    // Page Size
    fileLayout->addWidget(new ElaText(tr("Page Size:"), this), row, 0);
    m_pageSizeLabel = new ElaLabel();
    m_pageSizeLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
    fileLayout->addWidget(m_pageSizeLabel, row++, 1);

    // PDF Version
    fileLayout->addWidget(new ElaText(tr("PDF Version:"), this), row, 0);
    m_versionLabel = new ElaLabel();
    m_versionLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
    fileLayout->addWidget(m_versionLabel, row++, 1);

    // Encrypted
    fileLayout->addWidget(new ElaText(tr("Encrypted:"), this), row, 0);
    m_encryptedLabel = new ElaLabel();
    m_encryptedLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
    fileLayout->addWidget(m_encryptedLabel, row++, 1);

    // Linearized
    fileLayout->addWidget(new ElaText(tr("Linearized:"), this), row, 0);
    m_linearizedLabel = new ElaLabel();
    m_linearizedLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
    fileLayout->addWidget(m_linearizedLabel, row++, 1);
    
    m_gridLayout->addWidget(fileGroup, 1, 0, 1, 2);
    
    m_scrollArea->setWidget(m_contentWidget);
    m_mainLayout->addWidget(m_scrollArea);

    // Set the main widget as central widget for ElaContentDialog
    setCentralWidget(mainWidget);
}

void DocumentInfoDialog::populateInfo()
{
    QFileInfo fileInfo(m_filePath);
    
    // File information
    m_filePathLabel->setText(m_filePath);
    m_fileSizeLabel->setText(formatFileSize(fileInfo.size()));
    
    if (m_controller && m_controller->pageCount()) {
        int pageCount = m_controller->pageCount().value();
        m_pageCountLabel->setText(QString::number(pageCount));
        
        // Get page size from first page
        if (pageCount > 0) {
            QSizeF pageSize = m_controller->getPageSize(0);
            if (!pageSize.isEmpty()) {
                // Convert from points to inches and mm
                double widthInches = pageSize.width() / 72.0;
                double heightInches = pageSize.height() / 72.0;
                double widthMm = widthInches * 25.4;
                double heightMm = heightInches * 25.4;
                
                QString sizeText = tr("%1 × %2 points (%3 × %4 inches, %5 × %6 mm)")
                    .arg(pageSize.width(), 0, 'f', 1)
                    .arg(pageSize.height(), 0, 'f', 1)
                    .arg(widthInches, 0, 'f', 2)
                    .arg(heightInches, 0, 'f', 2)
                    .arg(widthMm, 0, 'f', 1)
                    .arg(heightMm, 0, 'f', 1);
                m_pageSizeLabel->setText(sizeText);
            }
        }
    }
    
    // Try to get PDF metadata using Poppler directly
    auto document = Poppler::Document::load(m_filePath);
    if (document) {
        // Metadata
        m_titleLabel->setText(document->info("Title").isEmpty() ? tr("(Not specified)") : document->info("Title"));
        m_authorLabel->setText(document->info("Author").isEmpty() ? tr("(Not specified)") : document->info("Author"));
        m_subjectLabel->setText(document->info("Subject").isEmpty() ? tr("(Not specified)") : document->info("Subject"));
        m_keywordsLabel->setText(document->info("Keywords").isEmpty() ? tr("(Not specified)") : document->info("Keywords"));
        m_creatorLabel->setText(document->info("Creator").isEmpty() ? tr("(Not specified)") : document->info("Creator"));
        m_producerLabel->setText(document->info("Producer").isEmpty() ? tr("(Not specified)") : document->info("Producer"));
        
        // Dates
        QDateTime creationDate = document->date("CreationDate");
        QDateTime modDate = document->date("ModDate");
        m_creationDateLabel->setText(creationDate.isValid() ? formatDateTime(creationDate) : tr("(Not specified)"));
        m_modificationDateLabel->setText(modDate.isValid() ? formatDateTime(modDate) : tr("(Not specified)"));
        
        // PDF properties
        auto version = document->getPdfVersion();
        m_versionLabel->setText(QString("PDF %1.%2").arg(version.major).arg(version.minor));
        m_encryptedLabel->setText(document->isEncrypted() ? tr("Yes") : tr("No"));
        m_linearizedLabel->setText(document->isLinearized() ? tr("Yes") : tr("No"));
    } else {
        // Fallback values
        m_titleLabel->setText(tr("(Unable to read)"));
        m_authorLabel->setText(tr("(Unable to read)"));
        m_subjectLabel->setText(tr("(Unable to read)"));
        m_keywordsLabel->setText(tr("(Unable to read)"));
        m_creatorLabel->setText(tr("(Unable to read)"));
        m_producerLabel->setText(tr("(Unable to read)"));
        m_creationDateLabel->setText(tr("(Unable to read)"));
        m_modificationDateLabel->setText(tr("(Unable to read)"));
        m_versionLabel->setText(tr("(Unable to read)"));
        m_encryptedLabel->setText(tr("(Unable to read)"));
        m_linearizedLabel->setText(tr("(Unable to read)"));
    }
}

QString DocumentInfoDialog::formatFileSize(qint64 bytes)
{
    const qint64 KB = 1024;
    const qint64 MB = KB * 1024;
    const qint64 GB = MB * 1024;
    
    if (bytes >= GB) {
        return tr("%1 GB (%2 bytes)").arg(bytes / (double)GB, 0, 'f', 2).arg(bytes);
    } else if (bytes >= MB) {
        return tr("%1 MB (%2 bytes)").arg(bytes / (double)MB, 0, 'f', 2).arg(bytes);
    } else if (bytes >= KB) {
        return tr("%1 KB (%2 bytes)").arg(bytes / (double)KB, 0, 'f', 2).arg(bytes);
    } else {
        return tr("%1 bytes").arg(bytes);
    }
}

QString DocumentInfoDialog::formatDateTime(const QDateTime& dateTime)
{
    return dateTime.toString("dddd, MMMM d, yyyy 'at' h:mm:ss AP");
}

void DocumentInfoDialog::copyToClipboard()
{
    QString info;
    info += tr("Document Properties\n");
    info += tr("==================\n\n");
    
    info += tr("Document Metadata:\n");
    info += tr("Title: %1\n").arg(m_titleLabel->text());
    info += tr("Author: %1\n").arg(m_authorLabel->text());
    info += tr("Subject: %1\n").arg(m_subjectLabel->text());
    info += tr("Keywords: %1\n").arg(m_keywordsLabel->text());
    info += tr("Creator: %1\n").arg(m_creatorLabel->text());
    info += tr("Producer: %1\n").arg(m_producerLabel->text());
    info += tr("Creation Date: %1\n").arg(m_creationDateLabel->text());
    info += tr("Modification Date: %1\n").arg(m_modificationDateLabel->text());
    info += "\n";
    
    info += tr("File Information:\n");
    info += tr("File Path: %1\n").arg(m_filePathLabel->text());
    info += tr("File Size: %1\n").arg(m_fileSizeLabel->text());
    info += tr("Page Count: %1\n").arg(m_pageCountLabel->text());
    info += tr("Page Size: %1\n").arg(m_pageSizeLabel->text());
    info += tr("PDF Version: %1\n").arg(m_versionLabel->text());
    info += tr("Encrypted: %1\n").arg(m_encryptedLabel->text());
    info += tr("Linearized: %1\n").arg(m_linearizedLabel->text());
    
    QClipboard* clipboard = QApplication::clipboard();
    clipboard->setText(info);
    
    QMessageBox::information(this, tr("Copied"), tr("Document information copied to clipboard."));
}
