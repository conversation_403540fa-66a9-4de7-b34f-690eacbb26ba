#!/bin/bash

echo "Building and running Qt PDF Viewer tests..."
echo

# Create build directory for tests if it doesn't exist
if [ ! -d "build" ]; then
    echo "Creating build directory..."
    mkdir build
fi

cd build

# Configure with tests enabled
echo "Configuring CMake with tests enabled..."
cmake .. -DBUILD_TESTS=ON
if [ $? -ne 0 ]; then
    echo "CMake configuration failed!"
    exit 1
fi

# Build the project and tests
echo "Building project and tests..."
cmake --build . --config Debug
if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

# Run the tests
echo
echo "Running tests..."
echo "================"
ctest --output-on-failure --verbose
if [ $? -ne 0 ]; then
    echo "Some tests failed!"
    exit 1
fi

echo
echo "All tests completed successfully!"
