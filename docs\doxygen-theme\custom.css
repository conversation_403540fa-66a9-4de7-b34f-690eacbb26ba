/* Custom CSS for Optimized PDF Viewer Documentation */

/* Brand colors and theme */
:root {
    --primary-color: #0067c0;
    --primary-dark: #004c8c;
    --primary-light: #3385d1;
    --accent-color: #ff6b35;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #868e96;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    
    --border-color: #dee2e6;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Dark theme variables */
[data-theme="dark"] {
    --text-primary: #f8f9fa;
    --text-secondary: #adb5bd;
    --text-muted: #6c757d;
    
    --bg-primary: #212529;
    --bg-secondary: #343a40;
    --bg-tertiary: #495057;
    
    --border-color: #495057;
}

/* Header customization */
#top {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-bottom: 3px solid var(--accent-color);
    box-shadow: var(--box-shadow-lg);
}

#titlearea {
    background: transparent;
    border-bottom: none;
}

#projectname {
    color: white;
    font-weight: 700;
    font-size: 2rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

#projectnumber {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
}

#projectbrief {
    color: rgba(255, 255, 255, 0.9);
    font-style: italic;
    margin-top: 0.5rem;
}

/* Navigation styling */
.tabs, .tabs2, .tabs3 {
    background: var(--bg-secondary);
    border-bottom: 2px solid var(--primary-color);
}

.tablist li {
    background: transparent;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    margin-right: 2px;
}

.tablist li.current {
    background: var(--primary-color);
    color: white;
}

.tablist li.current a {
    color: white;
    text-decoration: none;
}

.tablist li:hover {
    background: var(--primary-light);
}

.tablist li:hover a {
    color: white;
}

/* Content area improvements */
.contents {
    padding: 2rem;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin: 1rem;
}

/* Code blocks and syntax highlighting */
.fragment {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin: 1rem 0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    overflow-x: auto;
}

.line {
    line-height: 1.5;
}

/* Class and member documentation */
.memitem {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin: 1rem 0;
    box-shadow: var(--box-shadow);
}

.memproto {
    background: var(--primary-color);
    color: white;
    padding: 1rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    font-weight: 600;
}

.memdoc {
    padding: 1.5rem;
    background: var(--bg-primary);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* Parameter tables */
.params {
    border-collapse: collapse;
    width: 100%;
    margin: 1rem 0;
}

.params th {
    background: var(--primary-color);
    color: white;
    padding: 0.75rem;
    text-align: left;
    font-weight: 600;
}

.params td {
    padding: 0.75rem;
    border-bottom: 1px solid var(--border-color);
}

.params tr:nth-child(even) {
    background: var(--bg-secondary);
}

/* Alerts and notices */
.note, .warning, .attention, .important {
    padding: 1rem;
    margin: 1rem 0;
    border-radius: var(--border-radius);
    border-left: 4px solid;
}

.note {
    background: rgba(23, 162, 184, 0.1);
    border-left-color: var(--info-color);
}

.warning {
    background: rgba(255, 193, 7, 0.1);
    border-left-color: var(--warning-color);
}

.attention, .important {
    background: rgba(220, 53, 69, 0.1);
    border-left-color: var(--danger-color);
}

/* Search box styling */
#MSearchBox {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.5rem;
}

#MSearchField {
    background: transparent;
    border: none;
    color: var(--text-primary);
    font-size: 0.9rem;
}

/* Footer styling */
.footer {
    background: var(--bg-secondary);
    border-top: 2px solid var(--primary-color);
    padding: 2rem;
    text-align: center;
    color: var(--text-secondary);
    margin-top: 3rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .contents {
        margin: 0.5rem;
        padding: 1rem;
    }
    
    #projectname {
        font-size: 1.5rem;
    }
    
    .memproto {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
    
    .memdoc {
        padding: 1rem;
    }
}

/* Print styles */
@media print {
    #top, .tabs, .tabs2, .tabs3 {
        display: none;
    }
    
    .contents {
        margin: 0;
        box-shadow: none;
        border: none;
    }
    
    .memitem {
        break-inside: avoid;
    }
}

/* Custom enhancements for Qt-specific elements */
.qt-signal {
    color: var(--success-color);
    font-weight: 600;
}

.qt-slot {
    color: var(--info-color);
    font-weight: 600;
}

.qt-property {
    color: var(--warning-color);
    font-weight: 600;
}

/* Inheritance diagrams */
.inherit_header {
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    font-weight: 600;
}

.inherit {
    border: 1px solid var(--border-color);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    background: var(--bg-secondary);
}

/* Directory tree styling */
.directory {
    border-collapse: collapse;
    width: 100%;
}

.directory tr.even {
    background: var(--bg-secondary);
}

.directory td {
    padding: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

/* File list improvements */
.icona, .iconb, .iconc {
    width: 24px;
    height: 24px;
    margin-right: 0.5rem;
}

/* Namespace and class list styling */
.classindex {
    column-count: 3;
    column-gap: 2rem;
    column-rule: 1px solid var(--border-color);
}

@media (max-width: 1024px) {
    .classindex {
        column-count: 2;
    }
}

@media (max-width: 768px) {
    .classindex {
        column-count: 1;
    }
}

/* Smooth transitions */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Focus states for accessibility */
a:focus, button:focus, input:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }
    
    [data-theme="dark"] {
        --border-color: #ffffff;
        --text-secondary: #ffffff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none;
    }
}
