#include "SearchHistory.h"
#include <QStandardPaths>
#include <QDir>
#include <QFile>
#include <QTextStream>
#include <QDebug>
#include <algorithm>

const QString SearchHistory::SETTINGS_GROUP = "SearchHistory";
const QString SearchHistory::HISTORY_KEY = "entries";

// SearchHistoryEntry implementation
QJsonObject SearchHistoryEntry::toJson() const
{
    QJsonObject obj;
    obj["searchTerm"] = searchTerm;
    obj["caseSensitive"] = caseSensitive;
    obj["wholeWords"] = wholeWords;
    obj["timestamp"] = timestamp.toString(Qt::ISODate);
    obj["resultCount"] = resultCount;
    obj["documentPath"] = documentPath;
    return obj;
}

SearchHistoryEntry SearchHistoryEntry::fromJson(const QJsonObject& json)
{
    SearchHistoryEntry entry;
    entry.searchTerm = json["searchTerm"].toString();
    entry.caseSensitive = json["caseSensitive"].toBool();
    entry.wholeWords = json["wholeWords"].toBool();
    entry.timestamp = QDateTime::fromString(json["timestamp"].toString(), Qt::ISODate);
    entry.resultCount = json["resultCount"].toInt();
    entry.documentPath = json["documentPath"].toString();
    return entry;
}

// SearchHistory implementation
SearchHistory::SearchHistory(QObject *parent)
    : QObject(parent)
    , m_maxHistorySize(DEFAULT_MAX_HISTORY_SIZE)
    , m_autoSaveEnabled(true)
    , m_settings(new QSettings(this))
{
    // Register the custom type
    qRegisterMetaType<SearchHistoryEntry>("SearchHistoryEntry");
    
    // Load existing history
    loadFromSettings();
}

SearchHistory::~SearchHistory()
{
    if (m_autoSaveEnabled) {
        saveToSettings();
    }
}

void SearchHistory::addSearch(const QString& searchTerm, bool caseSensitive, bool wholeWords, 
                             int resultCount, const QString& documentPath)
{
    SearchHistoryEntry entry(searchTerm, caseSensitive, wholeWords, resultCount, documentPath);
    addSearch(entry);
}

void SearchHistory::addSearch(const SearchHistoryEntry& entry)
{
    if (entry.searchTerm.trimmed().isEmpty()) {
        return;
    }
    
    // Remove existing duplicate if present
    auto it = std::find(m_history.begin(), m_history.end(), entry);
    if (it != m_history.end()) {
        m_history.erase(it);
    }
    
    // Add to front (most recent)
    m_history.prepend(entry);
    
    // Trim if necessary
    trimHistory();
    
    emit entryAdded(entry);
    emit historyChanged();
    
    if (m_autoSaveEnabled) {
        saveToSettings();
    }
}

QList<SearchHistoryEntry> SearchHistory::getHistory() const
{
    return m_history;
}

QList<SearchHistoryEntry> SearchHistory::getRecentHistory(int maxCount) const
{
    return m_history.mid(0, qMin(maxCount, m_history.size()));
}

QList<SearchHistoryEntry> SearchHistory::getHistoryForDocument(const QString& documentPath) const
{
    QList<SearchHistoryEntry> result;
    for (const auto& entry : m_history) {
        if (entry.documentPath == documentPath) {
            result.append(entry);
        }
    }
    return result;
}

QStringList SearchHistory::getRecentSearchTerms(int maxCount) const
{
    QStringList terms;
    QSet<QString> uniqueTerms;
    
    for (const auto& entry : m_history) {
        if (!uniqueTerms.contains(entry.searchTerm)) {
            terms.append(entry.searchTerm);
            uniqueTerms.insert(entry.searchTerm);
            if (terms.size() >= maxCount) {
                break;
            }
        }
    }
    
    return terms;
}

QStringList SearchHistory::getPopularSearchTerms(int maxCount) const
{
    QMap<QString, int> termCounts;
    
    // Count occurrences
    for (const auto& entry : m_history) {
        termCounts[entry.searchTerm]++;
    }
    
    // Sort by count
    QList<QPair<int, QString>> sortedTerms;
    for (auto it = termCounts.begin(); it != termCounts.end(); ++it) {
        sortedTerms.append(qMakePair(it.value(), it.key()));
    }
    
    std::sort(sortedTerms.begin(), sortedTerms.end(), 
              [](const QPair<int, QString>& a, const QPair<int, QString>& b) {
                  return a.first > b.first;
              });
    
    QStringList result;
    for (int i = 0; i < qMin(maxCount, sortedTerms.size()); ++i) {
        result.append(sortedTerms[i].second);
    }
    
    return result;
}

QList<SearchHistoryEntry> SearchHistory::searchHistory(const QString& query) const
{
    QList<SearchHistoryEntry> result;
    QString lowerQuery = query.toLower();
    
    for (const auto& entry : m_history) {
        if (entry.searchTerm.toLower().contains(lowerQuery)) {
            result.append(entry);
        }
    }
    
    return result;
}

QList<SearchHistoryEntry> SearchHistory::getHistoryByDateRange(const QDateTime& from, const QDateTime& to) const
{
    QList<SearchHistoryEntry> result;
    
    for (const auto& entry : m_history) {
        if (entry.timestamp >= from && entry.timestamp <= to) {
            result.append(entry);
        }
    }
    
    return result;
}

int SearchHistory::getHistoryCount() const
{
    return m_history.size();
}

int SearchHistory::getSearchCount(const QString& searchTerm) const
{
    int count = 0;
    for (const auto& entry : m_history) {
        if (entry.searchTerm == searchTerm) {
            count++;
        }
    }
    return count;
}

QDateTime SearchHistory::getLastSearchTime() const
{
    if (m_history.isEmpty()) {
        return QDateTime();
    }
    return m_history.first().timestamp;
}

QStringList SearchHistory::getMostSearchedTerms(int maxCount) const
{
    return getPopularSearchTerms(maxCount);
}

void SearchHistory::clearHistory()
{
    m_history.clear();
    emit historyCleared();
    emit historyChanged();
    
    if (m_autoSaveEnabled) {
        saveToSettings();
    }
}

void SearchHistory::removeEntry(int index)
{
    if (index >= 0 && index < m_history.size()) {
        m_history.removeAt(index);
        emit entryRemoved(index);
        emit historyChanged();
        
        if (m_autoSaveEnabled) {
            saveToSettings();
        }
    }
}

void SearchHistory::removeEntriesForDocument(const QString& documentPath)
{
    auto it = std::remove_if(m_history.begin(), m_history.end(),
                            [&documentPath](const SearchHistoryEntry& entry) {
                                return entry.documentPath == documentPath;
                            });
    
    if (it != m_history.end()) {
        m_history.erase(it, m_history.end());
        emit historyChanged();
        
        if (m_autoSaveEnabled) {
            saveToSettings();
        }
    }
}

void SearchHistory::removeOldEntries(int daysOld)
{
    QDateTime cutoffDate = QDateTime::currentDateTime().addDays(-daysOld);
    
    auto it = std::remove_if(m_history.begin(), m_history.end(),
                            [&cutoffDate](const SearchHistoryEntry& entry) {
                                return entry.timestamp < cutoffDate;
                            });
    
    if (it != m_history.end()) {
        m_history.erase(it, m_history.end());
        emit historyChanged();
        
        if (m_autoSaveEnabled) {
            saveToSettings();
        }
    }
}

void SearchHistory::setMaxHistorySize(int maxSize)
{
    m_maxHistorySize = qMax(1, maxSize);
    trimHistory();
}

int SearchHistory::getMaxHistorySize() const
{
    return m_maxHistorySize;
}

void SearchHistory::setAutoSaveEnabled(bool enabled)
{
    m_autoSaveEnabled = enabled;
}

bool SearchHistory::isAutoSaveEnabled() const
{
    return m_autoSaveEnabled;
}

void SearchHistory::trimHistory()
{
    while (m_history.size() > m_maxHistorySize) {
        m_history.removeLast();
    }
}

void SearchHistory::removeDuplicates()
{
    QList<SearchHistoryEntry> uniqueHistory;
    QSet<QString> seenEntries;
    
    for (const auto& entry : m_history) {
        QString key = QString("%1|%2|%3|%4")
                     .arg(entry.searchTerm)
                     .arg(entry.caseSensitive)
                     .arg(entry.wholeWords)
                     .arg(entry.documentPath);
        
        if (!seenEntries.contains(key)) {
            uniqueHistory.append(entry);
            seenEntries.insert(key);
        }
    }
    
    m_history = uniqueHistory;
}

QString SearchHistory::getSettingsKey() const
{
    return QString("%1/%2").arg(SETTINGS_GROUP, HISTORY_KEY);
}

void SearchHistory::saveToSettings()
{
    m_settings->beginGroup(SETTINGS_GROUP);

    QJsonArray historyArray;
    for (const auto& entry : m_history) {
        historyArray.append(entry.toJson());
    }

    QJsonDocument doc(historyArray);
    m_settings->setValue(HISTORY_KEY, doc.toJson(QJsonDocument::Compact));
    m_settings->setValue("maxHistorySize", m_maxHistorySize);
    m_settings->setValue("autoSaveEnabled", m_autoSaveEnabled);

    m_settings->endGroup();
}

void SearchHistory::loadFromSettings()
{
    m_settings->beginGroup(SETTINGS_GROUP);

    // Load configuration
    m_maxHistorySize = m_settings->value("maxHistorySize", DEFAULT_MAX_HISTORY_SIZE).toInt();
    m_autoSaveEnabled = m_settings->value("autoSaveEnabled", true).toBool();

    // Load history
    QByteArray historyData = m_settings->value(HISTORY_KEY).toByteArray();
    if (!historyData.isEmpty()) {
        QJsonDocument doc = QJsonDocument::fromJson(historyData);
        if (doc.isArray()) {
            QJsonArray historyArray = doc.array();
            m_history.clear();

            for (const auto& value : historyArray) {
                if (value.isObject()) {
                    SearchHistoryEntry entry = SearchHistoryEntry::fromJson(value.toObject());
                    if (!entry.searchTerm.isEmpty()) {
                        m_history.append(entry);
                    }
                }
            }
        }
    }

    m_settings->endGroup();

    // Clean up old entries and duplicates
    removeOldEntries(90); // Remove entries older than 90 days
    removeDuplicates();
    trimHistory();
}

void SearchHistory::exportToFile(const QString& filePath) const
{
    QJsonArray historyArray;
    for (const auto& entry : m_history) {
        historyArray.append(entry.toJson());
    }

    QJsonObject root;
    root["version"] = "1.0";
    root["exportDate"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    root["entryCount"] = m_history.size();
    root["history"] = historyArray;

    QJsonDocument doc(root);

    QFile file(filePath);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        file.close();
    } else {
        qWarning() << "Failed to export search history to" << filePath;
    }
}

void SearchHistory::importFromFile(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Failed to import search history from" << filePath;
        return;
    }

    QByteArray data = file.readAll();
    file.close();

    QJsonDocument doc = QJsonDocument::fromJson(data);
    if (!doc.isObject()) {
        qWarning() << "Invalid search history file format";
        return;
    }

    QJsonObject root = doc.object();
    QJsonArray historyArray = root["history"].toArray();

    for (const auto& value : historyArray) {
        if (value.isObject()) {
            SearchHistoryEntry entry = SearchHistoryEntry::fromJson(value.toObject());
            if (!entry.searchTerm.isEmpty()) {
                addSearch(entry);
            }
        }
    }
}
