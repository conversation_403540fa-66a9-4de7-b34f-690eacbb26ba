@echo off
REM Documentation Generation Script for Windows
REM Optimized PDF Viewer - Comprehensive Documentation Builder

setlocal enabledelayedexpansion

REM Script configuration
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..
set BUILD_DIR=%PROJECT_ROOT%\build
set DOCS_OUTPUT=%BUILD_DIR%\docs

echo ========================================
echo Optimized PDF Viewer - Documentation Generator
echo ========================================
echo.

REM Parse command line arguments
set CLEAN_BUILD=0
set SKIP_API=0
set OPEN_DOCS=0
set VERBOSE=0

:parse_args
if "%~1"=="" goto :args_done
if /i "%~1"=="--clean" set CLEAN_BUILD=1
if /i "%~1"=="--skip-api" set SKIP_API=1
if /i "%~1"=="--open" set OPEN_DOCS=1
if /i "%~1"=="--verbose" set VERBOSE=1
if /i "%~1"=="--help" goto :show_help
shift
goto :parse_args

:args_done

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7 or later and try again
    exit /b 1
)

REM Check if Doxygen is available
if %SKIP_API%==0 (
    doxygen --version >nul 2>&1
    if errorlevel 1 (
        echo WARNING: Doxygen not found. API documentation will be skipped.
        echo Install Doxygen from https://www.doxygen.nl/download.html
        set SKIP_API=1
    ) else (
        echo ✓ Doxygen found
    )
)

REM Check if Graphviz is available (for diagrams)
dot -V >nul 2>&1
if errorlevel 1 (
    echo WARNING: Graphviz not found. Diagrams will not be generated.
    echo Install Graphviz from https://graphviz.org/download/
) else (
    echo ✓ Graphviz found
)

REM Clean previous build if requested
if %CLEAN_BUILD%==1 (
    echo Cleaning previous documentation build...
    if exist "%DOCS_OUTPUT%" (
        rmdir /s /q "%DOCS_OUTPUT%"
        echo ✓ Cleaned output directory
    )
)

REM Create output directory
if not exist "%DOCS_OUTPUT%" (
    mkdir "%DOCS_OUTPUT%"
    echo ✓ Created output directory: %DOCS_OUTPUT%
)

REM Run the Python documentation generator
echo.
echo Generating documentation...
echo.

set PYTHON_ARGS=--project-root "%PROJECT_ROOT%"
if %SKIP_API%==1 set PYTHON_ARGS=%PYTHON_ARGS% --skip-api
if %CLEAN_BUILD%==1 set PYTHON_ARGS=%PYTHON_ARGS% --clean

python "%SCRIPT_DIR%generate_docs.py" %PYTHON_ARGS%
if errorlevel 1 (
    echo.
    echo ERROR: Documentation generation failed
    echo Check the error messages above for details
    exit /b 1
)

echo.
echo ========================================
echo Documentation Generation Complete!
echo ========================================
echo.
echo Output location: %DOCS_OUTPUT%
echo.
echo Available documentation:
echo   • API Documentation: %DOCS_OUTPUT%\api\html\index.html
echo   • User Manual: %DOCS_OUTPUT%\user\user-manual.html
echo   • Developer Guide: %DOCS_OUTPUT%\user\developer-guide.html
echo   • Installation Guide: %DOCS_OUTPUT%\user\installation.html
echo.

REM Open documentation if requested
if %OPEN_DOCS%==1 (
    echo Opening documentation in default browser...
    start "" "%DOCS_OUTPUT%\index.html"
)

echo To view the documentation, open: %DOCS_OUTPUT%\index.html
echo.
echo Build completed successfully!
goto :eof

:show_help
echo.
echo Usage: generate_docs.bat [OPTIONS]
echo.
echo Options:
echo   --clean      Clean previous build before generating
echo   --skip-api   Skip API documentation generation (useful if Doxygen not available)
echo   --open       Open documentation in browser after generation
echo   --verbose    Enable verbose output
echo   --help       Show this help message
echo.
echo Examples:
echo   generate_docs.bat                    Generate all documentation
echo   generate_docs.bat --clean --open    Clean build and open result
echo   generate_docs.bat --skip-api        Generate only user documentation
echo.
goto :eof
