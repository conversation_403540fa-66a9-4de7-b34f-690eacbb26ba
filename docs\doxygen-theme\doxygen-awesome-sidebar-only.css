/**
 * Sidebar-only theme for Doxygen Awesome CSS
 * This theme hides the top navigation and shows only the sidebar
 */

/* Hide the top navigation completely */
#top {
    display: none !important;
}

/* Adjust the main content area */
#main-nav {
    display: none !important;
}

/* Expand the sidebar to full height */
#side-nav {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    height: 100vh !important;
    width: 300px !important;
    max-width: none !important;
    z-index: 1000;
    border-right: 1px solid var(--separator-color);
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

/* Adjust the document content */
#doc-content {
    margin-left: 300px !important;
    padding-top: var(--spacing-large) !important;
    min-height: 100vh;
}

/* Responsive adjustments */
@media screen and (max-width: 1024px) {
    #side-nav {
        width: 250px !important;
    }
    
    #doc-content {
        margin-left: 250px !important;
    }
}

@media screen and (max-width: 768px) {
    /* On mobile, show a toggle button for the sidebar */
    #side-nav {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    #side-nav.open {
        transform: translateX(0);
    }
    
    #doc-content {
        margin-left: 0 !important;
    }
    
    /* Add a mobile menu button */
    body::before {
        content: "☰";
        position: fixed;
        top: 10px;
        left: 10px;
        z-index: 1001;
        background: var(--primary-color);
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 18px;
        box-shadow: var(--box-shadow);
    }
}

/* Enhanced sidebar styling */
#nav-tree {
    padding-top: var(--spacing-large);
    height: calc(100vh - var(--spacing-large));
    overflow-y: auto;
}

/* Add a header to the sidebar */
#side-nav::before {
    content: "Navigation";
    display: block;
    padding: var(--spacing-large);
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    font-size: var(--navigation-font-size);
    border-bottom: 1px solid var(--separator-color);
    position: sticky;
    top: 0;
    z-index: 10;
}

/* Improve the navigation tree appearance */
#nav-tree .item {
    padding: var(--spacing-small) var(--spacing-medium);
    border-radius: var(--border-radius-small);
    margin: 2px var(--spacing-small);
}

#nav-tree .item:hover {
    background: var(--odd-color);
}

#nav-tree .selected {
    background: var(--primary-light-color) !important;
    color: white !important;
}

#nav-tree .selected a {
    color: white !important;
}

/* Add search functionality to sidebar */
#side-nav-search {
    padding: var(--spacing-medium);
    border-bottom: 1px solid var(--separator-color);
    background: var(--side-nav-background);
    position: sticky;
    top: 0;
    z-index: 10;
}

#side-nav-search input {
    width: 100%;
    padding: var(--spacing-small);
    border: 1px solid var(--separator-color);
    border-radius: var(--border-radius-small);
    background: var(--page-background-color);
    color: var(--page-foreground-color);
    font-size: var(--navigation-font-size);
}

#side-nav-search input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(23, 121, 196, 0.2);
}

/* Collapsible sections in sidebar */
.nav-section {
    margin: var(--spacing-medium) 0;
}

.nav-section-header {
    padding: var(--spacing-small) var(--spacing-medium);
    background: var(--code-background);
    color: var(--page-foreground-color);
    font-weight: 600;
    cursor: pointer;
    border-radius: var(--border-radius-small);
    margin: 0 var(--spacing-small);
    user-select: none;
}

.nav-section-header:hover {
    background: var(--odd-color);
}

.nav-section-header::before {
    content: "▶";
    display: inline-block;
    margin-right: var(--spacing-small);
    transition: transform 0.2s ease;
}

.nav-section.open .nav-section-header::before {
    transform: rotate(90deg);
}

.nav-section-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.nav-section.open .nav-section-content {
    max-height: 1000px;
}

/* Footer adjustments */
#nav-path {
    margin-left: 300px;
}

@media screen and (max-width: 1024px) {
    #nav-path {
        margin-left: 250px;
    }
}

@media screen and (max-width: 768px) {
    #nav-path {
        margin-left: 0;
    }
}

/* Print styles */
@media print {
    #side-nav {
        display: none !important;
    }
    
    #doc-content {
        margin-left: 0 !important;
    }
    
    #nav-path {
        margin-left: 0 !important;
    }
}

/* High contrast mode adjustments */
@media (prefers-contrast: high) {
    #side-nav {
        border-right-width: 2px;
    }
    
    #nav-tree .selected {
        border: 2px solid var(--primary-color);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    #side-nav {
        transition: none;
    }
    
    .nav-section-content {
        transition: none;
    }
    
    .nav-section-header::before {
        transition: none;
    }
}
