---
Checks: >
  *,
  -abseil-*,
  -altera-*,
  -android-*,
  -fuchsia-*,
  -google-*,
  -llvm-*,
  -llvmlibc-*,
  -zircon-*,
  -readability-magic-numbers,
  -cppcoreguidelines-avoid-magic-numbers,
  -cppcoreguidelines-pro-bounds-pointer-arithmetic,
  -cppcoreguidelines-pro-type-reinterpret-cast,
  -modernize-use-trailing-return-type,
  -readability-named-parameter,
  -cert-err58-cpp,
  -misc-non-private-member-variables-in-classes,
  -cppcoreguidelines-non-private-member-variables-in-classes

WarningsAsErrors: ''
HeaderFilterRegex: '(src|include)/.*\.(h|hpp)$'
AnalyzeTemporaryDtors: false
FormatStyle: file
CheckOptions:
  - key: readability-identifier-naming.NamespaceCase
    value: lower_case
  - key: readability-identifier-naming.ClassCase
    value: CamelCase
  - key: readability-identifier-naming.StructCase
    value: CamelCase
  - key: readability-identifier-naming.TemplateParameterCase
    value: CamelCase
  - key: readability-identifier-naming.FunctionCase
    value: camelBack
  - key: readability-identifier-naming.VariableCase
    value: camelBack
  - key: readability-identifier-naming.ClassMemberCase
    value: camelBack
  - key: readability-identifier-naming.ClassMemberPrefix
    value: m_
  - key: readability-identifier-naming.PrivateMemberPrefix
    value: m_
  - key: readability-identifier-naming.ProtectedMemberPrefix
    value: m_
  - key: readability-identifier-naming.EnumConstantCase
    value: CamelCase
  - key: readability-identifier-naming.ConstantCase
    value: UPPER_CASE
  - key: readability-identifier-naming.StaticConstantCase
    value: UPPER_CASE
  - key: readability-identifier-naming.GlobalConstantCase
    value: UPPER_CASE
  - key: readability-identifier-naming.MacroDefinitionCase
    value: UPPER_CASE
  - key: readability-function-cognitive-complexity.Threshold
    value: 25
  - key: readability-function-size.LineThreshold
    value: 80
  - key: readability-function-size.StatementThreshold
    value: 800
  - key: readability-function-size.BranchThreshold
    value: 20
  - key: readability-function-size.ParameterThreshold
    value: 8
  - key: readability-function-size.NestingThreshold
    value: 4
  - key: readability-function-size.VariableThreshold
    value: 20
