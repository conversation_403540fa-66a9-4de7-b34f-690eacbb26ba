#ifndef ANNOTATIONSEARCHDIALOG_H
#define ANNOTATIONSEARCHDIALOG_H

#include <QDialog>
#include <QColor>
#include <QDateTime>
#include "Annotation.h"

class QLineEdit;
class QComboBox;
class QCheckBox;
class QDateEdit;
class QListWidget;
class QListWidgetItem;
class QPushButton;
class QLabel;
class AnnotationManager;

struct AnnotationSearchCriteria
{
    QString searchText;
    AnnotationType annotationType = AnnotationType::Highlight; // Default, but will be ignored if typeFilter is false
    bool typeFilter = false;
    QString author;
    bool authorFilter = false;
    QColor color;
    bool colorFilter = false;
    QDateTime createdAfter;
    QDateTime createdBefore;
    bool dateFilter = false;
    int pageNumber = -1; // -1 means all pages
    bool pageFilter = false;
    bool caseSensitive = false;
    bool wholeWords = false;
};

class AnnotationSearchDialog : public QDialog
{
    Q_OBJECT

public:
    explicit AnnotationSearchDialog(AnnotationManager* manager, QWidget *parent = nullptr);
    ~AnnotationSearchDialog();

    // Search results
    QList<Annotation*> getSearchResults() const { return m_searchResults; }
    Annotation* getCurrentSelectedAnnotation() const;

signals:
    void annotationSelected(Annotation* annotation);
    void navigateToAnnotation(Annotation* annotation);

private slots:
    void performSearch();
    void clearSearch();
    void onSearchTextChanged();
    void onResultItemClicked(QListWidgetItem* item);
    void onResultItemDoubleClicked(QListWidgetItem* item);
    void onNavigateToAnnotation();
    void onSelectAnnotation();
    void onExportResults();

private:
    void setupUi();
    void setupSearchCriteria();
    void setupResultsList();
    void setupButtons();
    
    void updateResultsList();
    void updateResultsCount();
    void updateColorButton();
    AnnotationSearchCriteria getCriteria() const;
    bool matchesCriteria(Annotation* annotation, const AnnotationSearchCriteria& criteria) const;
    QString formatAnnotationForDisplay(Annotation* annotation) const;
    QIcon getAnnotationIcon(AnnotationType type) const;

    // UI components
    QLineEdit* m_searchEdit;
    QComboBox* m_typeComboBox;
    QCheckBox* m_typeFilterCheckBox;
    QLineEdit* m_authorEdit;
    QCheckBox* m_authorFilterCheckBox;
    QPushButton* m_colorButton;
    QCheckBox* m_colorFilterCheckBox;
    QDateEdit* m_createdAfterEdit;
    QDateEdit* m_createdBeforeEdit;
    QCheckBox* m_dateFilterCheckBox;
    QComboBox* m_pageComboBox;
    QCheckBox* m_pageFilterCheckBox;
    QCheckBox* m_caseSensitiveCheckBox;
    QCheckBox* m_wholeWordsCheckBox;
    
    QListWidget* m_resultsList;
    QLabel* m_resultsCountLabel;
    
    QPushButton* m_searchButton;
    QPushButton* m_clearButton;
    QPushButton* m_navigateButton;
    QPushButton* m_selectButton;
    QPushButton* m_exportButton;
    QPushButton* m_closeButton;

    // Data
    AnnotationManager* m_annotationManager;
    QList<Annotation*> m_searchResults;
    QColor m_selectedColor;
};

#endif // ANNOTATIONSEARCHDIALOG_H
