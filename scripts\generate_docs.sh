#!/bin/bash
# Documentation Generation Script for Linux/macOS
# Optimized PDF Viewer - Comprehensive Documentation Builder

set -e  # Exit on any error

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_ROOT/build"
DOCS_OUTPUT="$BUILD_DIR/docs"

# Default options
CLEAN_BUILD=0
SKIP_API=0
OPEN_DOCS=0
VERBOSE=0

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

log_success() {
    echo -e "${GREEN}✓${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

log_error() {
    echo -e "${RED}✗${NC} $1"
}

show_help() {
    cat << EOF

Usage: $0 [OPTIONS]

Options:
  --clean      Clean previous build before generating
  --skip-api   Skip API documentation generation (useful if Doxygen not available)
  --open       Open documentation in browser after generation
  --verbose    Enable verbose output
  --help       Show this help message

Examples:
  $0                    Generate all documentation
  $0 --clean --open    Clean build and open result
  $0 --skip-api        Generate only user documentation

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --clean)
            CLEAN_BUILD=1
            shift
            ;;
        --skip-api)
            SKIP_API=1
            shift
            ;;
        --open)
            OPEN_DOCS=1
            shift
            ;;
        --verbose)
            VERBOSE=1
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Header
echo "========================================"
echo "Optimized PDF Viewer - Documentation Generator"
echo "========================================"
echo

# Check dependencies
log_info "Checking dependencies..."

# Check Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        log_error "Python is not installed or not in PATH"
        log_error "Please install Python 3.7 or later and try again"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi
log_success "Python found: $($PYTHON_CMD --version)"

# Check Doxygen (if not skipping API docs)
if [[ $SKIP_API -eq 0 ]]; then
    if ! command -v doxygen &> /dev/null; then
        log_warning "Doxygen not found. API documentation will be skipped."
        log_warning "Install Doxygen: sudo apt-get install doxygen (Ubuntu) or brew install doxygen (macOS)"
        SKIP_API=1
    else
        log_success "Doxygen found: $(doxygen --version)"
    fi
fi

# Check Graphviz (for diagrams)
if ! command -v dot &> /dev/null; then
    log_warning "Graphviz not found. Diagrams will not be generated."
    log_warning "Install Graphviz: sudo apt-get install graphviz (Ubuntu) or brew install graphviz (macOS)"
else
    log_success "Graphviz found: $(dot -V 2>&1 | head -n1)"
fi

# Clean previous build if requested
if [[ $CLEAN_BUILD -eq 1 ]]; then
    log_info "Cleaning previous documentation build..."
    if [[ -d "$DOCS_OUTPUT" ]]; then
        rm -rf "$DOCS_OUTPUT"
        log_success "Cleaned output directory"
    fi
fi

# Create output directory
if [[ ! -d "$DOCS_OUTPUT" ]]; then
    mkdir -p "$DOCS_OUTPUT"
    log_success "Created output directory: $DOCS_OUTPUT"
fi

# Prepare Python arguments
PYTHON_ARGS="--project-root \"$PROJECT_ROOT\""
if [[ $SKIP_API -eq 1 ]]; then
    PYTHON_ARGS="$PYTHON_ARGS --skip-api"
fi
if [[ $CLEAN_BUILD -eq 1 ]]; then
    PYTHON_ARGS="$PYTHON_ARGS --clean"
fi

# Run the Python documentation generator
echo
log_info "Generating documentation..."
echo

if [[ $VERBOSE -eq 1 ]]; then
    eval "$PYTHON_CMD \"$SCRIPT_DIR/generate_docs.py\" $PYTHON_ARGS"
else
    eval "$PYTHON_CMD \"$SCRIPT_DIR/generate_docs.py\" $PYTHON_ARGS" 2>&1 | grep -E "(✓|✗|⚠|ERROR|WARNING|Generating|Building)"
fi

if [[ $? -ne 0 ]]; then
    echo
    log_error "Documentation generation failed"
    log_error "Check the error messages above for details"
    exit 1
fi

# Success message
echo
echo "========================================"
log_success "Documentation Generation Complete!"
echo "========================================"
echo
log_info "Output location: $DOCS_OUTPUT"
echo
echo "Available documentation:"
echo "  • API Documentation: $DOCS_OUTPUT/api/html/index.html"
echo "  • User Manual: $DOCS_OUTPUT/user/user-manual.html"
echo "  • Developer Guide: $DOCS_OUTPUT/user/developer-guide.html"
echo "  • Installation Guide: $DOCS_OUTPUT/user/installation.html"
echo

# Open documentation if requested
if [[ $OPEN_DOCS -eq 1 ]]; then
    log_info "Opening documentation in default browser..."
    
    # Detect platform and open browser
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        open "$DOCS_OUTPUT/index.html"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command -v xdg-open &> /dev/null; then
            xdg-open "$DOCS_OUTPUT/index.html"
        elif command -v firefox &> /dev/null; then
            firefox "$DOCS_OUTPUT/index.html" &
        elif command -v chromium-browser &> /dev/null; then
            chromium-browser "$DOCS_OUTPUT/index.html" &
        else
            log_warning "Could not detect browser to open documentation"
        fi
    else
        log_warning "Unsupported platform for auto-opening browser"
    fi
fi

echo "To view the documentation, open: $DOCS_OUTPUT/index.html"
echo
log_success "Build completed successfully!"

# Optional: Generate deployment package
if command -v tar &> /dev/null; then
    PACKAGE_NAME="pdf-viewer-docs-$(date +%Y%m%d-%H%M%S).tar.gz"
    log_info "Creating deployment package: $PACKAGE_NAME"
    
    cd "$BUILD_DIR"
    tar -czf "$PACKAGE_NAME" docs/
    log_success "Documentation package created: $BUILD_DIR/$PACKAGE_NAME"
fi
