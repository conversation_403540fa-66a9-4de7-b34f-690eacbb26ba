#ifndef FULLSCREENOVERLAY_H
#define FULLSCREENOVERLAY_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QSlider>
#include <QTimer>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>

class FullScreenOverlay : public QWidget
{
    Q_OBJECT

public:
    explicit FullScreenOverlay(QWidget *parent = nullptr);
    
    void setPageInfo(int currentPage, int totalPages);
    void setZoomLevel(double zoomFactor);
    void showOverlay();
    void hideOverlay();
    void toggleOverlay();

signals:
    void previousPageRequested();
    void nextPageRequested();
    void firstPageRequested();
    void lastPageRequested();
    void zoomInRequested();
    void zoomOutRequested();
    void fitToWindowRequested();
    void fitToWidthRequested();
    void exitFullScreenRequested();

protected:
    void enterEvent(QEnterEvent* event) override;
    void leaveEvent(QEvent* event) override;
    void mouseMoveEvent(QMouseEvent* event) override;

private slots:
    void onAutoHideTimer();
    void onFadeInFinished();
    void onFadeOutFinished();

private:
    void setupUi();
    void startAutoHideTimer();
    void stopAutoHideTimer();
    void fadeIn();
    void fadeOut();

    // UI components
    QVBoxLayout* m_mainLayout;
    QWidget* m_controlsWidget;
    QHBoxLayout* m_controlsLayout;
    
    // Navigation controls
    QPushButton* m_firstPageButton;
    QPushButton* m_prevPageButton;
    QPushButton* m_nextPageButton;
    QPushButton* m_lastPageButton;
    QLabel* m_pageInfoLabel;
    
    // Zoom controls
    QPushButton* m_zoomOutButton;
    QPushButton* m_zoomInButton;
    QPushButton* m_fitToWindowButton;
    QPushButton* m_fitToWidthButton;
    QLabel* m_zoomLabel;
    
    // Exit control
    QPushButton* m_exitButton;
    
    // Auto-hide functionality
    QTimer* m_autoHideTimer;
    QPropertyAnimation* m_fadeAnimation;
    QGraphicsOpacityEffect* m_opacityEffect;
    
    bool m_isVisible;
    bool m_isAnimating;
};

#endif // FULLSCREENOVERLAY_H
