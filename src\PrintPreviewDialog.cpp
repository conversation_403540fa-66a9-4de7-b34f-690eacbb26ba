#include "PrintPreviewDialog.h"
#include "PdfController.h"
#include <QMessageBox>
#include <QApplication>
#include <QScreen>
#include <QPainter>
#include <QPageLayout>

PrintPreviewDialog::PrintPreviewDialog(PdfController* controller, QWidget *parent)
    : QDialog(parent)
    , m_controller(controller)
    , m_printer(new QPrinter(QPrinter::HighResolution))
    , m_totalPages(0)
    , m_currentPreviewPage(0)
{
    setWindowTitle(tr("Print Preview"));
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);
    resize(1000, 700);
    
    if (m_controller && m_controller->pageCount()) {
        m_totalPages = m_controller->pageCount().value();
    }
    
    setupUi();
    setupPrintOptions();
    updatePreview();
}

void PrintPreviewDialog::setupUi()
{
    m_mainLayout = new QVBoxLayout(this);
    
    // Create splitter for preview and controls
    m_splitter = new QSplitter(Qt::Horizontal, this);
    
    // Create print preview widget
    m_previewWidget = new QPrintPreviewWidget(m_printer, this);
    m_previewWidget->setMinimumWidth(600);
    connect(m_previewWidget, &QPrintPreviewWidget::paintRequested, this, &PrintPreviewDialog::printDocument);
    
    // Create controls panel
    m_controlsPanel = new QWidget();
    m_controlsPanel->setFixedWidth(280);
    m_controlsLayout = new QVBoxLayout(m_controlsPanel);
    
    // Print actions group
    m_printGroup = new QGroupBox(tr("Print Actions"));
    QVBoxLayout* printLayout = new QVBoxLayout(m_printGroup);
    
    m_printButton = new QPushButton(tr("Print..."));
    m_printButton->setIcon(style()->standardIcon(QStyle::SP_DialogApplyButton));
    connect(m_printButton, &QPushButton::clicked, this, &PrintPreviewDialog::onPrintClicked);
    
    m_pageSetupButton = new QPushButton(tr("Page Setup..."));
    connect(m_pageSetupButton, &QPushButton::clicked, this, &PrintPreviewDialog::onPageSetupClicked);
    
    m_closeButton = new QPushButton(tr("Close"));
    connect(m_closeButton, &QPushButton::clicked, this, &QDialog::reject);
    
    printLayout->addWidget(m_printButton);
    printLayout->addWidget(m_pageSetupButton);
    printLayout->addWidget(m_closeButton);
    
    // Page range group
    m_pageRangeGroup = new QGroupBox(tr("Page Range"));
    QVBoxLayout* rangeLayout = new QVBoxLayout(m_pageRangeGroup);
    
    m_allPagesRadio = new QCheckBox(tr("All pages"));
    m_allPagesRadio->setChecked(true);
    connect(m_allPagesRadio, &QCheckBox::toggled, this, &PrintPreviewDialog::onPageRangeChanged);
    
    m_currentPageRadio = new QCheckBox(tr("Current page"));
    connect(m_currentPageRadio, &QCheckBox::toggled, this, &PrintPreviewDialog::onPageRangeChanged);
    
    m_pageRangeRadio = new QCheckBox(tr("Page range:"));
    connect(m_pageRangeRadio, &QCheckBox::toggled, this, &PrintPreviewDialog::onPageRangeChanged);
    
    QHBoxLayout* rangeInputLayout = new QHBoxLayout();
    rangeInputLayout->addWidget(new QLabel(tr("From:")));
    m_fromPageSpinBox = new QSpinBox();
    m_fromPageSpinBox->setRange(1, m_totalPages);
    m_fromPageSpinBox->setValue(1);
    m_fromPageSpinBox->setEnabled(false);
    connect(m_fromPageSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &PrintPreviewDialog::onPageRangeChanged);
    
    rangeInputLayout->addWidget(m_fromPageSpinBox);
    rangeInputLayout->addWidget(new QLabel(tr("To:")));
    m_toPageSpinBox = new QSpinBox();
    m_toPageSpinBox->setRange(1, m_totalPages);
    m_toPageSpinBox->setValue(m_totalPages);
    m_toPageSpinBox->setEnabled(false);
    connect(m_toPageSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &PrintPreviewDialog::onPageRangeChanged);
    
    rangeInputLayout->addWidget(m_toPageSpinBox);
    
    m_pageRangeLabel = new QLabel();
    
    rangeLayout->addWidget(m_allPagesRadio);
    rangeLayout->addWidget(m_currentPageRadio);
    rangeLayout->addWidget(m_pageRangeRadio);
    rangeLayout->addLayout(rangeInputLayout);
    rangeLayout->addWidget(m_pageRangeLabel);
    
    // Layout options group
    m_layoutGroup = new QGroupBox(tr("Layout Options"));
    QGridLayout* layoutGrid = new QGridLayout(m_layoutGroup);
    
    layoutGrid->addWidget(new QLabel(tr("Orientation:")), 0, 0);
    m_orientationCombo = new QComboBox();
    m_orientationCombo->addItem(tr("Portrait"), static_cast<int>(QPageLayout::Portrait));
    m_orientationCombo->addItem(tr("Landscape"), static_cast<int>(QPageLayout::Landscape));
    connect(m_orientationCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &PrintPreviewDialog::onOrientationChanged);
    layoutGrid->addWidget(m_orientationCombo, 0, 1);
    
    layoutGrid->addWidget(new QLabel(tr("Scaling:")), 1, 0);
    m_scalingCombo = new QComboBox();
    m_scalingCombo->addItem(tr("Fit to page"), 0);
    m_scalingCombo->addItem(tr("Actual size"), 1);
    m_scalingCombo->addItem(tr("Custom"), 2);
    connect(m_scalingCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &PrintPreviewDialog::onScalingChanged);
    layoutGrid->addWidget(m_scalingCombo, 1, 1);
    
    m_fitToPageCheckBox = new QCheckBox(tr("Fit to page"));
    m_fitToPageCheckBox->setChecked(true);
    layoutGrid->addWidget(m_fitToPageCheckBox, 2, 0, 1, 2);
    
    m_centerOnPageCheckBox = new QCheckBox(tr("Center on page"));
    m_centerOnPageCheckBox->setChecked(true);
    layoutGrid->addWidget(m_centerOnPageCheckBox, 3, 0, 1, 2);
    
    // Preview controls group
    m_previewGroup = new QGroupBox(tr("Preview Controls"));
    QVBoxLayout* previewLayout = new QVBoxLayout(m_previewGroup);
    
    QHBoxLayout* zoomLayout = new QHBoxLayout();
    zoomLayout->addWidget(new QLabel(tr("Zoom:")));
    m_zoomCombo = new QComboBox();
    m_zoomCombo->addItem(tr("Fit Width"), QPrintPreviewWidget::FitToWidth);
    m_zoomCombo->addItem(tr("Fit Page"), QPrintPreviewWidget::FitInView);
    m_zoomCombo->addItem(tr("25%"), 0.25);
    m_zoomCombo->addItem(tr("50%"), 0.5);
    m_zoomCombo->addItem(tr("75%"), 0.75);
    m_zoomCombo->addItem(tr("100%"), 1.0);
    m_zoomCombo->addItem(tr("125%"), 1.25);
    m_zoomCombo->addItem(tr("150%"), 1.5);
    m_zoomCombo->addItem(tr("200%"), 2.0);
    m_zoomCombo->setCurrentIndex(1); // Fit Page
    connect(m_zoomCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &PrintPreviewDialog::onZoomChanged);
    zoomLayout->addWidget(m_zoomCombo);
    
    QHBoxLayout* navLayout = new QHBoxLayout();
    m_firstPageButton = new QPushButton(tr("First"));
    m_prevPageButton = new QPushButton(tr("Previous"));
    m_nextPageButton = new QPushButton(tr("Next"));
    m_lastPageButton = new QPushButton(tr("Last"));
    
    navLayout->addWidget(m_firstPageButton);
    navLayout->addWidget(m_prevPageButton);
    navLayout->addWidget(m_nextPageButton);
    navLayout->addWidget(m_lastPageButton);
    
    m_pageInfoLabel = new QLabel();
    
    previewLayout->addLayout(zoomLayout);
    previewLayout->addLayout(navLayout);
    previewLayout->addWidget(m_pageInfoLabel);
    
    // Add groups to controls layout
    m_controlsLayout->addWidget(m_printGroup);
    m_controlsLayout->addWidget(m_pageRangeGroup);
    m_controlsLayout->addWidget(m_layoutGroup);
    m_controlsLayout->addWidget(m_previewGroup);
    m_controlsLayout->addStretch();
    
    // Add to splitter
    m_splitter->addWidget(m_previewWidget);
    m_splitter->addWidget(m_controlsPanel);
    m_splitter->setStretchFactor(0, 1);
    m_splitter->setStretchFactor(1, 0);
    
    m_mainLayout->addWidget(m_splitter);
}

void PrintPreviewDialog::setupPrintOptions()
{
    // Set default printer settings
    m_printer->setPageOrientation(QPageLayout::Portrait);
    m_printer->setPageSize(QPageSize::A4);
    m_printer->setResolution(300); // 300 DPI for good quality
    
    updatePageRangeControls();
}

void PrintPreviewDialog::updatePageRangeControls()
{
    m_pageRangeLabel->setText(tr("Total pages: %1").arg(m_totalPages));
    
    bool rangeEnabled = m_pageRangeRadio->isChecked();
    m_fromPageSpinBox->setEnabled(rangeEnabled);
    m_toPageSpinBox->setEnabled(rangeEnabled);
    
    if (rangeEnabled) {
        // Ensure valid range
        if (m_fromPageSpinBox->value() > m_toPageSpinBox->value()) {
            m_toPageSpinBox->setValue(m_fromPageSpinBox->value());
        }
    }
}

QString PrintPreviewDialog::getPageRangeText() const
{
    if (m_allPagesRadio->isChecked()) {
        return tr("All pages (1-%1)").arg(m_totalPages);
    } else if (m_currentPageRadio->isChecked()) {
        return tr("Current page (%1)").arg(m_currentPreviewPage + 1);
    } else if (m_pageRangeRadio->isChecked()) {
        return tr("Pages %1-%2").arg(m_fromPageSpinBox->value()).arg(m_toPageSpinBox->value());
    }
    return QString();
}

void PrintPreviewDialog::onPrintClicked()
{
    QPrintDialog printDialog(m_printer, this);
    printDialog.setWindowTitle(tr("Print Document"));
    
    if (printDialog.exec() == QDialog::Accepted) {
        // Print the document
        printDocument(m_printer);
        accept();
    }
}

void PrintPreviewDialog::onPageSetupClicked()
{
    QPageSetupDialog pageSetupDialog(m_printer, this);
    if (pageSetupDialog.exec() == QDialog::Accepted) {
        updatePreview();
    }
}

void PrintPreviewDialog::onZoomChanged()
{
    QVariant zoomData = m_zoomCombo->currentData();
    if (zoomData.typeId() == QMetaType::Double) {
        m_previewWidget->setZoomFactor(zoomData.toDouble());
    } else {
        QPrintPreviewWidget::ZoomMode mode = static_cast<QPrintPreviewWidget::ZoomMode>(zoomData.toInt());
        m_previewWidget->setZoomMode(mode);
    }
}

void PrintPreviewDialog::onOrientationChanged()
{
    QPageLayout::Orientation orientation = static_cast<QPageLayout::Orientation>(m_orientationCombo->currentData().toInt());
    m_printer->setPageOrientation(orientation);
    updatePreview();
}

void PrintPreviewDialog::onPageRangeChanged()
{
    // Make radio buttons mutually exclusive
    QCheckBox* sender = qobject_cast<QCheckBox*>(this->sender());
    if (sender && sender->isChecked()) {
        if (sender != m_allPagesRadio) m_allPagesRadio->setChecked(false);
        if (sender != m_currentPageRadio) m_currentPageRadio->setChecked(false);
        if (sender != m_pageRangeRadio) m_pageRangeRadio->setChecked(false);
        sender->setChecked(true);
    }
    
    updatePageRangeControls();
    updatePreview();
}

void PrintPreviewDialog::onScalingChanged()
{
    updatePreview();
}

void PrintPreviewDialog::updatePreview()
{
    m_previewWidget->updatePreview();
    m_pageInfoLabel->setText(getPageRangeText());
}

void PrintPreviewDialog::printDocument(QPrinter* printer)
{
    if (!m_controller || !m_controller->pageCount()) {
        return;
    }
    
    QPainter painter(printer);
    if (!painter.isActive()) {
        QMessageBox::warning(this, tr("Print Error"), tr("Could not start printing."));
        return;
    }
    
    // Determine which pages to print
    QList<int> pagesToPrint;
    if (m_allPagesRadio->isChecked()) {
        for (int i = 0; i < m_totalPages; ++i) {
            pagesToPrint.append(i);
        }
    } else if (m_currentPageRadio->isChecked()) {
        pagesToPrint.append(m_currentPreviewPage);
    } else if (m_pageRangeRadio->isChecked()) {
        for (int i = m_fromPageSpinBox->value() - 1; i < m_toPageSpinBox->value(); ++i) {
            if (i >= 0 && i < m_totalPages) {
                pagesToPrint.append(i);
            }
        }
    }
    
    if (pagesToPrint.isEmpty()) {
        return;
    }
    
    // Print each page
    for (int i = 0; i < pagesToPrint.size(); ++i) {
        if (i > 0) {
            printer->newPage();
        }
        
        int pageNum = pagesToPrint[i];
        
        // Get page size and calculate scaling
        QSizeF pageSize = m_controller->getPageSize(pageNum);
        if (pageSize.isEmpty()) continue;
        
        // Render page to image at high resolution
        double dpi = 300.0; // High quality for printing
        QImage pageImage = m_controller->renderPageToImage(pageNum, dpi, 0);
        
        if (pageImage.isNull()) continue;
        
        // Calculate scaling and positioning
        QRectF pageRectF = printer->pageRect(QPrinter::DevicePixel);
        QRect pageRect = pageRectF.toRect();
        QSize imageSize = pageImage.size();
        
        if (m_fitToPageCheckBox->isChecked()) {
            // Scale to fit page
            imageSize.scale(pageRect.size(), Qt::KeepAspectRatio);
        }
        
        // Center on page if requested
        QPoint drawPos(0, 0);
        if (m_centerOnPageCheckBox->isChecked()) {
            drawPos.setX((pageRect.width() - imageSize.width()) / 2);
            drawPos.setY((pageRect.height() - imageSize.height()) / 2);
        }
        
        // Draw the page
        QRect drawRect(drawPos, imageSize);
        painter.drawImage(drawRect, pageImage);
    }
}
