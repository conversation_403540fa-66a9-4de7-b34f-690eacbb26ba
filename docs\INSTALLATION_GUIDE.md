# PDF Viewer - Installation Guide

## Table of Contents

1. [System Requirements](#system-requirements)
2. [Pre-built Binaries](#pre-built-binaries)
3. [Building from Source](#building-from-source)
4. [Dependencies](#dependencies)
5. [Platform-Specific Instructions](#platform-specific-instructions)
6. [Troubleshooting](#troubleshooting)
7. [Uninstallation](#uninstallation)

## System Requirements

### Minimum Requirements

- **Operating System**:
  - Windows 10 (1903) or later
  - macOS 10.15 (Catalina) or later
  - Linux (Ubuntu 20.04 LTS or equivalent)
- **Memory**: 4 GB RAM
- **Storage**: 200 MB free disk space
- **Graphics**: DirectX 11 compatible graphics card
- **Display**: 1024x768 resolution minimum

### Recommended Requirements

- **Memory**: 8 GB RAM or more
- **Storage**: 1 GB free disk space (for cache and documents)
- **Graphics**: Dedicated graphics card with hardware acceleration
- **Display**: 1920x1080 resolution or higher

## Pre-built Binaries

### Windows

1. **Download**: Get the latest Windows installer from the releases page
   - `PDF-Viewer-Setup-x64.exe` (64-bit, recommended)
   - `PDF-Viewer-Setup-x86.exe` (32-bit, legacy systems)

2. **Installation**:

   ```cmd
   # Run the installer as administrator
   PDF-Viewer-Setup-x64.exe
   
   # Silent installation (optional)
   PDF-Viewer-Setup-x64.exe /S
   ```

3. **Portable Version**:
   - Download `PDF-Viewer-Portable-x64.zip`
   - Extract to desired location
   - Run `optimized-pdf-viewer.exe`

### macOS

1. **Download**: Get the macOS disk image
   - `PDF-Viewer-macOS.dmg` (Universal binary for Intel and Apple Silicon)

2. **Installation**:

   ```bash
   # Mount the disk image
   open PDF-Viewer-macOS.dmg
   
   # Drag PDF Viewer to Applications folder
   # Or use command line:
   cp -R "/Volumes/PDF Viewer/PDF Viewer.app" /Applications/
   ```

3. **First Launch**:

   ```bash
   # If you get a security warning, allow the app:
   sudo xattr -rd com.apple.quarantine "/Applications/PDF Viewer.app"
   ```

### Linux

1. **AppImage** (Universal):

   ```bash
   # Download AppImage
   wget https://github.com/user/pdf-viewer/releases/latest/download/PDF-Viewer-x86_64.AppImage
   
   # Make executable
   chmod +x PDF-Viewer-x86_64.AppImage
   
   # Run
   ./PDF-Viewer-x86_64.AppImage
   ```

2. **Debian/Ubuntu Package**:

   ```bash
   # Download .deb package
   wget https://github.com/user/pdf-viewer/releases/latest/download/pdf-viewer_1.0_amd64.deb
   
   # Install
   sudo dpkg -i pdf-viewer_1.0_amd64.deb
   sudo apt-get install -f  # Fix dependencies if needed
   ```

3. **RPM Package** (Fedora/CentOS):

   ```bash
   # Download .rpm package
   wget https://github.com/user/pdf-viewer/releases/latest/download/pdf-viewer-1.0.x86_64.rpm
   
   # Install
   sudo rpm -i pdf-viewer-1.0.x86_64.rpm
   ```

## Building from Source

### Prerequisites

- **CMake**: Version 3.16 or later
- **Qt6**: Complete development package
- **C++ Compiler**: GCC 9+, Clang 10+, or MSVC 2019+
- **Git**: For cloning the repository

### Quick Build

```bash
# Clone repository
git clone https://github.com/user/pdf-viewer.git
cd pdf-viewer

# Create build directory
mkdir build && cd build

# Configure
cmake .. -DCMAKE_BUILD_TYPE=Release

# Build
cmake --build . --config Release -j$(nproc)

# Install (optional)
sudo cmake --install .
```

### Advanced Build Options

```bash
# Debug build with tests
cmake .. -DCMAKE_BUILD_TYPE=Debug -DBUILD_TESTS=ON

# Custom installation prefix
cmake .. -DCMAKE_INSTALL_PREFIX=/opt/pdf-viewer

# Static linking (Windows)
cmake .. -DSTATIC_BUILD=ON

# Enable all optimizations
cmake .. -DCMAKE_BUILD_TYPE=Release -DENABLE_LTO=ON
```

## Dependencies

### Core Dependencies

- **Qt6** (6.2 or later):
  - Qt6Core
  - Qt6Widgets  
  - Qt6Gui
  - Qt6PrintSupport
- **ElaWidgetTools**: Modern UI component library
- **Poppler-Qt6**: PDF rendering engine

### Optional Dependencies

- **Qt6Test**: For running unit tests
- **Qt6Concurrent**: For multi-threaded operations
- **OpenSSL**: For secure PDF handling

## Platform-Specific Instructions

### Windows

#### Visual Studio Build

```cmd
# Install Qt6 and Visual Studio 2019/2022
# Open Developer Command Prompt

git clone https://github.com/user/pdf-viewer.git
cd pdf-viewer
mkdir build && cd build

# Configure for Visual Studio
cmake .. -G "Visual Studio 16 2019" -A x64

# Build
cmake --build . --config Release
```

#### MinGW Build

```bash
# Install Qt6 with MinGW
# Add Qt6 and MinGW to PATH

mkdir build && cd build
cmake .. -G "MinGW Makefiles"
mingw32-make -j4
```

#### Dependencies Installation

```powershell
# Using vcpkg
vcpkg install qt6-base qt6-tools poppler[qt6]

# Using Qt Installer
# Download from https://www.qt.io/download-qt-installer
```

### macOS

#### Homebrew Installation

```bash
# Install dependencies
brew install qt6 cmake poppler-qt6

# Build
mkdir build && cd build
cmake .. -DCMAKE_PREFIX_PATH=$(brew --prefix qt6)
make -j$(sysctl -n hw.ncpu)
```

#### MacPorts Installation

```bash
# Install dependencies
sudo port install qt6-qtbase cmake poppler-qt6

# Build with MacPorts Qt
mkdir build && cd build
cmake .. -DCMAKE_PREFIX_PATH=/opt/local/libexec/qt6
make -j$(sysctl -n hw.ncpu)
```

### Linux

#### Ubuntu/Debian

```bash
# Install dependencies
sudo apt-get update
sudo apt-get install qt6-base-dev qt6-tools-dev cmake build-essential
sudo apt-get install libpoppler-qt6-dev git

# Build
mkdir build && cd build
cmake ..
make -j$(nproc)

# Install
sudo make install
```

#### Fedora/CentOS

```bash
# Install dependencies
sudo dnf install qt6-qtbase-devel qt6-qttools-devel cmake gcc-c++
sudo dnf install poppler-qt6-devel git

# Build
mkdir build && cd build
cmake ..
make -j$(nproc)

# Install
sudo make install
```

#### Arch Linux

```bash
# Install dependencies
sudo pacman -S qt6-base qt6-tools cmake gcc
sudo pacman -S poppler-qt6 git

# Build
mkdir build && cd build
cmake ..
make -j$(nproc)

# Install
sudo make install
```

## Troubleshooting

### Common Build Issues

#### Qt6 Not Found

```bash
# Specify Qt6 path explicitly
cmake .. -DCMAKE_PREFIX_PATH=/path/to/qt6

# Or set environment variable
export CMAKE_PREFIX_PATH=/path/to/qt6
```

#### ElaWidgetTools Missing

```bash
# Install ElaWidgetTools first
git clone https://github.com/Liniyous/ElaWidgetTools.git
cd ElaWidgetTools
mkdir build && cd build
cmake ..
make install
```

#### Poppler-Qt6 Issues

```bash
# Ubuntu/Debian
sudo apt-get install libpoppler-qt6-dev

# macOS
brew install poppler-qt6

# Windows (vcpkg)
vcpkg install poppler[qt6]
```

### Runtime Issues

#### Missing DLLs (Windows)

```cmd
# Copy Qt6 DLLs to application directory
# Or add Qt6 bin directory to PATH
set PATH=%PATH%;C:\Qt\6.x\msvc2019_64\bin
```

#### Library Loading Errors (Linux)

```bash
# Update library cache
sudo ldconfig

# Check library dependencies
ldd optimized-pdf-viewer

# Set library path if needed
export LD_LIBRARY_PATH=/usr/local/lib:$LD_LIBRARY_PATH
```

#### macOS Code Signing

```bash
# Self-sign for development
codesign --force --deep --sign - "PDF Viewer.app"

# Remove quarantine attribute
sudo xattr -rd com.apple.quarantine "PDF Viewer.app"
```

### Performance Issues

#### High Memory Usage

- Reduce cache size in settings (default: 450MB)
- Close unused document tabs
- Disable visual effects if needed

#### Slow Rendering

- Enable hardware acceleration in settings
- Update graphics drivers
- Reduce default zoom level

## Uninstallation

### Windows

```cmd
# Using Control Panel
# Programs and Features → PDF Viewer → Uninstall

# Or run uninstaller directly
"C:\Program Files\PDF Viewer\uninstall.exe"

# Manual cleanup
rmdir /s "C:\Program Files\PDF Viewer"
rmdir /s "%APPDATA%\PDF Viewer"
```

### macOS

```bash
# Remove application
rm -rf "/Applications/PDF Viewer.app"

# Remove user data
rm -rf "~/Library/Application Support/PDF Viewer"
rm -rf "~/Library/Preferences/com.pdfviewer.app.plist"
```

### Linux

```bash
# Package manager installation
sudo apt-get remove pdf-viewer  # Debian/Ubuntu
sudo dnf remove pdf-viewer      # Fedora
sudo pacman -R pdf-viewer       # Arch

# Manual installation
sudo rm -rf /usr/local/bin/optimized-pdf-viewer
sudo rm -rf /usr/local/share/pdf-viewer
rm -rf ~/.config/PDF\ Viewer
rm -rf ~/.local/share/PDF\ Viewer
```

---

*PDF Viewer Installation Guide v1.0 - Complete setup instructions for all platforms*
