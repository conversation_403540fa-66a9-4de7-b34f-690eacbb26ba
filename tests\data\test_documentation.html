<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Documentation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #2c3e50; border-bottom: 2px solid #3498db; }
        h2 { color: #34495e; margin-top: 30px; }
        code { background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
        pre { background-color: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .highlight { background-color: yellow; }
    </style>
</head>
<body>
    <h1 id="main-title">Test Documentation</h1>
    
    <h2 id="introduction">Introduction</h2>
    <p>This is a comprehensive test documentation file designed to test various features of the DocumentationViewer component.</p>
    <p>It includes multiple sections, code examples, and different types of content to ensure proper functionality.</p>
    
    <h2 id="features">Key Features</h2>
    <ul>
        <li><strong>Search Functionality:</strong> Full-text search with highlighting</li>
        <li><strong>Table of Contents:</strong> Automatic generation from headers</li>
        <li><strong>Bookmarks:</strong> Save and organize frequently accessed sections</li>
        <li><strong>Zoom Controls:</strong> Flexible zoom options for better readability</li>
        <li><strong>History Navigation:</strong> Back and forward navigation through viewed content</li>
        <li><strong>Syntax Highlighting:</strong> Code blocks with proper highlighting</li>
    </ul>
    
    <h2 id="code-examples">Code Examples</h2>
    <p>Here are some code examples to test syntax highlighting:</p>
    
    <h3 id="javascript-example">JavaScript Example</h3>
    <pre><code class="language-javascript">
function calculateSum(a, b) {
    // This is a comment
    if (typeof a !== 'number' || typeof b !== 'number') {
        throw new Error('Both parameters must be numbers');
    }
    
    const result = a + b;
    console.log(`The sum of ${a} and ${b} is ${result}`);
    return result;
}

// Usage example
const sum = calculateSum(10, 20);
    </code></pre>
    
    <h3 id="python-example">Python Example</h3>
    <pre><code class="language-python">
class DocumentProcessor:
    """A class for processing documentation files."""
    
    def __init__(self, file_path):
        self.file_path = file_path
        self.content = ""
    
    def load_content(self):
        """Load content from the file."""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as file:
                self.content = file.read()
                return True
        except FileNotFoundError:
            print(f"File not found: {self.file_path}")
            return False
    
    def search_text(self, query, case_sensitive=False):
        """Search for text in the content."""
        content = self.content if case_sensitive else self.content.lower()
        query = query if case_sensitive else query.lower()
        return query in content
    </code></pre>
    
    <h3 id="cpp-example">C++ Example</h3>
    <pre><code class="language-cpp">
#include <iostream>
#include <string>
#include <vector>

class DocumentationViewer {
private:
    std::string m_content;
    std::vector<std::string> m_bookmarks;
    int m_zoomLevel;

public:
    DocumentationViewer() : m_zoomLevel(100) {
        // Constructor implementation
    }
    
    void loadDocumentation(const std::string& content) {
        m_content = content;
        std::cout << "Documentation loaded successfully" << std::endl;
    }
    
    void setZoomLevel(int level) {
        if (level >= 50 && level <= 300) {
            m_zoomLevel = level;
        }
    }
    
    void addBookmark(const std::string& url) {
        m_bookmarks.push_back(url);
    }
};
    </code></pre>
    
    <h2 id="testing-sections">Testing Sections</h2>
    <p>This section contains various elements for testing different aspects of the viewer:</p>
    
    <h3 id="text-formatting">Text Formatting</h3>
    <p>This paragraph contains <strong>bold text</strong>, <em>italic text</em>, and <code>inline code</code>.</p>
    <p>It also includes <a href="#main-title">internal links</a> and <a href="https://example.com">external links</a>.</p>
    
    <h3 id="lists">Lists</h3>
    <h4>Ordered List</h4>
    <ol>
        <li>First item</li>
        <li>Second item with <strong>formatting</strong></li>
        <li>Third item with <code>code</code></li>
    </ol>
    
    <h4>Unordered List</h4>
    <ul>
        <li>Bullet point one</li>
        <li>Bullet point two</li>
        <li>Bullet point three</li>
    </ul>
    
    <h3 id="tables">Tables</h3>
    <table border="1" style="border-collapse: collapse; width: 100%;">
        <thead>
            <tr>
                <th>Feature</th>
                <th>Status</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Search</td>
                <td>✓ Complete</td>
                <td>Full-text search with highlighting</td>
            </tr>
            <tr>
                <td>Bookmarks</td>
                <td>✓ Complete</td>
                <td>Save and manage bookmarks</td>
            </tr>
            <tr>
                <td>TOC</td>
                <td>✓ Complete</td>
                <td>Automatic table of contents</td>
            </tr>
        </tbody>
    </table>
    
    <h2 id="search-test">Search Test Section</h2>
    <p>This section is specifically designed for testing search functionality.</p>
    <p>It contains various keywords like: documentation, viewer, search, highlight, bookmark, navigation.</p>
    <p>The search feature should be able to find and highlight these terms when searched.</p>
    <p>Case sensitivity and whole word matching should also work properly.</p>
    
    <h2 id="conclusion">Conclusion</h2>
    <p>This test documentation file provides comprehensive content for testing all aspects of the DocumentationViewer.</p>
    <p>It includes headers for TOC generation, code blocks for syntax highlighting, various text elements for search testing, and links for navigation testing.</p>
    
    <h3 id="final-notes">Final Notes</h3>
    <p>Remember to test:</p>
    <ul>
        <li>Zoom in and zoom out functionality</li>
        <li>Print and print preview</li>
        <li>Bookmark management</li>
        <li>History navigation</li>
        <li>Keyboard shortcuts</li>
        <li>Error handling</li>
    </ul>
    
    <p><em>End of test documentation.</em></p>
</body>
</html>
