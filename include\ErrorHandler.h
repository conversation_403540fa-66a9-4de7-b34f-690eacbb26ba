#ifndef ERRORHANDLER_H
#define ERRORHANDLER_H

#include <QObject>
#include <QException>
#include <QMessageBox>
#include <QTimer>
#include <QDateTime>
#include <QStandardPaths>
#include <QDir>
#include <QSysInfo>
#include <QCoreApplication>
#include <QThread>
#include <QMutex>
#include <QMutexLocker>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QProcess>
#include <memory>
#include <csignal>
// #include <cpptrace/cpptrace.hpp> // Temporarily disabled
#include "Logger.h"

// Forward declarations
class CrashLogPackager;

enum class ErrorSeverity {
    Info = 0,
    Warning = 1,
    Error = 2,
    Critical = 3,
    Fatal = 4
};

enum class ErrorCategory {
    System,
    FileIO,
    PDF,
    UI,
    Network,
    Memory,
    Unknown
};

struct CrashInfo {
    QString crashType;
    QString signal;
    QString stackTrace;
    QString systemInfo;
    QString memoryInfo;
    QString userActions;
    QDateTime timestamp;
    QString processId;
    QString threadId;
    QString buildInfo;

    CrashInfo()
        : timestamp(QDateTime::currentDateTime()) {}
};

struct ErrorInfo {
    ErrorSeverity severity;
    ErrorCategory category;
    QString title;
    QString message;
    QString details;
    QString location;
    QDateTime timestamp;
    bool userNotified;
    QString stackTrace; // Added for enhanced error reporting

    ErrorInfo()
        : severity(ErrorSeverity::Error)
        , category(ErrorCategory::Unknown)
        , timestamp(QDateTime::currentDateTime())
        , userNotified(false) {}
};

// Forward declarations (cpptrace temporarily disabled)
// namespace cpptrace {
//     class stacktrace;
// }

// Forward declarations for signal handlers
extern "C" void signalHandler(int signal);
void terminateHandler();

class ErrorHandler : public QObject
{
    Q_OBJECT

    // Friend functions for signal handling
    friend void ::signalHandler(int signal);
    friend void ::terminateHandler();

public:
    static ErrorHandler* instance();
    
    // Error reporting
    void reportError(ErrorSeverity severity, ErrorCategory category,
                    const QString& title, const QString& message,
                    const QString& details = QString(), const QString& location = QString());
    
    void reportException(const QException& exception, const QString& location = QString());
    void reportSystemError(const QString& operation, int errorCode, const QString& location = QString());
    void reportFileError(const QString& filePath, const QString& operation, const QString& location = QString());
    void reportPdfError(const QString& pdfPath, const QString& operation, const QString& location = QString());
    
    // Error handling configuration
    void setShowUserNotifications(bool enabled);
    bool getShowUserNotifications() const;
    
    void setAutoReporting(bool enabled);
    bool getAutoReporting() const;
    
    void setMaxErrorHistory(int maxErrors);
    int getMaxErrorHistory() const;
    
    // Error history
    QList<ErrorInfo> getErrorHistory() const;
    void clearErrorHistory();
    ErrorInfo getLastError() const;
    int getErrorCount(ErrorSeverity severity) const;
    
    // Recovery suggestions
    QString getRecoverySuggestion(ErrorCategory category, const QString& errorMessage) const;
    bool canAutoRecover(ErrorCategory category, const QString& errorMessage) const;
    void attemptAutoRecovery(const ErrorInfo& error);
    
    // User notification
    void showErrorDialog(const ErrorInfo& error);
    void showErrorNotification(const ErrorInfo& error);
    
    // Crash handling
    void setupCrashHandler();
    void handleCrash(const QString& crashInfo);

    // Enhanced crash handling with cpptrace
    void handleSignalCrash(int signal);
    void handleTerminate();
    void handleUnexpectedException();
    QString generateCrashReport(const CrashInfo& crashInfo);
    bool saveCrashReport(const CrashInfo& crashInfo);
    QString packageCrashLogs();

    // System information gathering
    QString getSystemInfo();
    QString getMemoryInfo();
    QString getBuildInfo();
    QString getUserActionHistory();

    // Stack trace utilities
    QString getCurrentStackTrace();
    QString formatStackTrace(const QString& fallbackTrace); // Changed signature for fallback

signals:
    void errorReported(const ErrorInfo& error);
    void criticalErrorOccurred(const ErrorInfo& error);
    void recoveryAttempted(const ErrorInfo& error, bool success);
    void crashDetected(const CrashInfo& crashInfo);
    void crashReportGenerated(const QString& reportPath);

public slots:
    void handleQtMessage(QtMsgType type, const QMessageLogContext& context, const QString& message);

private:
    explicit ErrorHandler(QObject *parent = nullptr);
    ~ErrorHandler();

    void notifyUser(const ErrorInfo& error);
    void logError(const ErrorInfo& error);
    void trimErrorHistory();
    QString severityToString(ErrorSeverity severity) const;
    QString categoryToString(ErrorCategory category) const;
    QMessageBox::Icon severityToIcon(ErrorSeverity severity) const;

    // Crash handling internals
    void installSignalHandlers();
    void uninstallSignalHandlers();
    QString getCrashLogDirectory();
    void ensureCrashLogDirectory();
    void trackUserAction(const QString& action);

    static ErrorHandler* s_instance;
    static QMutex s_crashMutex;
    static bool s_crashHandlerInstalled;
    
    // Configuration
    bool m_showUserNotifications;
    bool m_autoReporting;
    int m_maxErrorHistory;

    // Error storage
    QList<ErrorInfo> m_errorHistory;

    // UI components
    QTimer* m_notificationTimer;

    // Crash handling data
    QMutex m_userActionMutex;
    QStringList m_userActionHistory;
    QString m_crashLogDir;
    CrashLogPackager* m_crashLogPackager;

    // Constants
    static const int DEFAULT_MAX_HISTORY = 100;
    static const int NOTIFICATION_DELAY = 100; // ms
    static const int MAX_USER_ACTIONS = 50;
    static const int MAX_STACK_TRACE_DEPTH = 100;
};

// Convenience macros
#define REPORT_ERROR(severity, category, title, message) \
    ErrorHandler::instance()->reportError(severity, category, title, message, QString(), Q_FUNC_INFO)

#define REPORT_ERROR_DETAILED(severity, category, title, message, details) \
    ErrorHandler::instance()->reportError(severity, category, title, message, details, Q_FUNC_INFO)

#define REPORT_FILE_ERROR(filePath, operation) \
    ErrorHandler::instance()->reportFileError(filePath, operation, Q_FUNC_INFO)

#define REPORT_PDF_ERROR(pdfPath, operation) \
    ErrorHandler::instance()->reportPdfError(pdfPath, operation, Q_FUNC_INFO)

#define REPORT_SYSTEM_ERROR(operation, errorCode) \
    ErrorHandler::instance()->reportSystemError(operation, errorCode, Q_FUNC_INFO)

#endif // ERRORHANDLER_H
