/**
 * @file test_text_selection.cpp
 * @brief Unit tests for text selection functionality
 */

#include <QtTest/QtTest>
#include <QApplication>
#include <QSignalSpy>
#include <QLabel>
#include <QScrollArea>
#include <QTemporaryFile>
#include "TextSelectionOverlay.h"
#include "PdfController.h"

class TestTextSelection : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Basic functionality tests
    void testOverlayCreation();
    void testTextSelectionEnabled();
    void testCoordinateConversion();
    void testSelectionState();
    void testClipboardIntegration();

    // Mouse interaction tests
    void testMouseSelection();
    void testSelectionClearing();
    void testKeyboardShortcuts();

    // Integration tests
    void testWithPdfController();
    void testPageChanges();
    void testZoomChanges();

private:
    TextSelectionOverlay* m_overlay;
    PdfController* m_controller;
    QLabel* m_pageLabel;
    QScrollArea* m_scrollArea;
    QString m_testPdfPath;
    
    void createTestPdf();
    void waitForSignal(QSignalSpy* spy, int timeout = 5000);
};

void TestTextSelection::initTestCase()
{
    // Create a simple test PDF file
    createTestPdf();
}

void TestTextSelection::cleanupTestCase()
{
    // Cleanup test files
    if (!m_testPdfPath.isEmpty() && QFile::exists(m_testPdfPath)) {
        QFile::remove(m_testPdfPath);
    }
}

void TestTextSelection::init()
{
    // Create test components
    m_scrollArea = new QScrollArea();
    m_pageLabel = new QLabel();
    m_pageLabel->setMinimumSize(400, 600);
    m_scrollArea->setWidget(m_pageLabel);
    
    m_controller = new PdfController();
    m_overlay = new TextSelectionOverlay(m_scrollArea);
    m_overlay->setPageLabel(m_pageLabel);
    m_overlay->setPdfController(m_controller);
    m_overlay->setCurrentPage(0);
    m_overlay->setZoomFactor(1.0);
    m_overlay->setDpi(72.0);
}

void TestTextSelection::cleanup()
{
    delete m_overlay;
    delete m_controller;
    delete m_scrollArea; // This will also delete m_pageLabel
    
    m_overlay = nullptr;
    m_controller = nullptr;
    m_pageLabel = nullptr;
    m_scrollArea = nullptr;
}

void TestTextSelection::testOverlayCreation()
{
    QVERIFY(m_overlay != nullptr);
    QVERIFY(!m_overlay->isTextSelectionEnabled());
    QVERIFY(!m_overlay->hasSelection());
    QVERIFY(m_overlay->getSelectedText().isEmpty());
}

void TestTextSelection::testTextSelectionEnabled()
{
    // Initially disabled
    QVERIFY(!m_overlay->isTextSelectionEnabled());
    
    // Enable text selection
    m_overlay->setTextSelectionEnabled(true);
    QVERIFY(m_overlay->isTextSelectionEnabled());
    
    // Disable text selection
    m_overlay->setTextSelectionEnabled(false);
    QVERIFY(!m_overlay->isTextSelectionEnabled());
}

void TestTextSelection::testCoordinateConversion()
{
    // Test coordinate conversion methods
    QPointF pixelPoint(100, 200);
    QPointF pdfPoint = m_overlay->pixelToPdfCoordinates(pixelPoint);
    QPointF backToPixel = m_overlay->pdfToPixelCoordinates(pdfPoint);
    
    // Should be approximately equal (allowing for floating point precision)
    QVERIFY(qAbs(pixelPoint.x() - backToPixel.x()) < 1.0);
    QVERIFY(qAbs(pixelPoint.y() - backToPixel.y()) < 1.0);
}

void TestTextSelection::testSelectionState()
{
    // Initially no selection
    QVERIFY(!m_overlay->hasSelection());
    QVERIFY(m_overlay->getSelectedText().isEmpty());
    
    // Clear selection should work even when no selection exists
    m_overlay->clearSelection();
    QVERIFY(!m_overlay->hasSelection());
}

void TestTextSelection::testClipboardIntegration()
{
    // Test clipboard functionality (even without actual selection)
    m_overlay->copySelectionToClipboard();
    // Should not crash even with no selection
    QVERIFY(true);
}

void TestTextSelection::testMouseSelection()
{
    m_overlay->setTextSelectionEnabled(true);
    
    // Simulate mouse press
    QMouseEvent pressEvent(QEvent::MouseButtonPress, QPointF(50, 50), QPointF(50, 50),
                          Qt::LeftButton, Qt::LeftButton, Qt::NoModifier);
    QApplication::sendEvent(m_overlay, &pressEvent);
    
    // Simulate mouse move
    QMouseEvent moveEvent(QEvent::MouseMove, QPointF(150, 100), QPointF(150, 100),
                         Qt::LeftButton, Qt::LeftButton, Qt::NoModifier);
    QApplication::sendEvent(m_overlay, &moveEvent);
    
    // Simulate mouse release
    QMouseEvent releaseEvent(QEvent::MouseButtonRelease, QPointF(150, 100), QPointF(150, 100),
                            Qt::LeftButton, Qt::LeftButton, Qt::NoModifier);
    QApplication::sendEvent(m_overlay, &releaseEvent);
    
    // Should have processed the events without crashing
    QVERIFY(true);
}

void TestTextSelection::testSelectionClearing()
{
    QSignalSpy clearedSpy(m_overlay, &TextSelectionOverlay::selectionCleared);
    
    m_overlay->clearSelection();
    
    // Should emit signal even if no selection was present
    QVERIFY(clearedSpy.count() >= 0);
}

void TestTextSelection::testKeyboardShortcuts()
{
    m_overlay->setTextSelectionEnabled(true);
    
    // Test Escape key (should clear selection)
    QKeyEvent escapeEvent(QEvent::KeyPress, Qt::Key_Escape, Qt::NoModifier);
    QApplication::sendEvent(m_overlay, &escapeEvent);
    
    // Test Ctrl+C (should copy selection)
    QKeyEvent copyEvent(QEvent::KeyPress, Qt::Key_C, Qt::ControlModifier);
    QApplication::sendEvent(m_overlay, &copyEvent);
    
    // Should handle events without crashing
    QVERIFY(true);
}

void TestTextSelection::testWithPdfController()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    // Load document
    QSignalSpy documentLoadedSpy(m_controller, &PdfController::documentLoaded);
    m_controller->loadDocument(m_testPdfPath);
    waitForSignal(&documentLoadedSpy);
    
    if (documentLoadedSpy.count() > 0) {
        QList<QVariant> arguments = documentLoadedSpy.takeFirst();
        bool success = arguments.at(0).toBool();
        
        if (success) {
            // Test text extraction
            QString pageText = m_controller->getPageText(0);
            QVERIFY(!pageText.isNull()); // May be empty for image-only PDFs
        }
    }
}

void TestTextSelection::testPageChanges()
{
    m_overlay->setCurrentPage(0);
    QVERIFY(true); // Should not crash
    
    m_overlay->setCurrentPage(5);
    QVERIFY(true); // Should not crash
}

void TestTextSelection::testZoomChanges()
{
    m_overlay->setZoomFactor(1.0);
    QVERIFY(true); // Should not crash
    
    m_overlay->setZoomFactor(2.0);
    QVERIFY(true); // Should not crash
    
    m_overlay->setDpi(150.0);
    QVERIFY(true); // Should not crash
}

void TestTextSelection::createTestPdf()
{
    // Try to find an existing test PDF
    QStringList possiblePaths = {
        "test.pdf",
        "tests/test.pdf",
        "../test.pdf"
    };
    
    for (const QString& path : possiblePaths) {
        if (QFile::exists(path)) {
            m_testPdfPath = path;
            return;
        }
    }
    
    // If no test PDF found, leave path empty
    m_testPdfPath.clear();
}

void TestTextSelection::waitForSignal(QSignalSpy* spy, int timeout)
{
    if (spy->count() == 0) {
        spy->wait(timeout);
    }
}

QTEST_MAIN(TestTextSelection)
#include "test_text_selection.moc"
