#!/bin/bash

# Build script for Optimized PDF Viewer
# Usage: ./scripts/build.sh [Debug|Release] [clean]

set -e

# Default values
BUILD_TYPE="${1:-Release}"
CLEAN="${2}"
BUILD_DIR="build"
SOURCE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo "Building Optimized PDF Viewer..."
echo "Source directory: $SOURCE_DIR"
echo "Build type: $BUILD_TYPE"

# Clean build directory if requested
if [ "$CLEAN" = "clean" ]; then
    echo "Cleaning build directory..."
    rm -rf "$BUILD_DIR"
fi

# Create build directory
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

# Configure with CMake
echo "Configuring with CMake..."
cmake .. \
    -DCMAKE_BUILD_TYPE="$BUILD_TYPE" \
    -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
    -DBUILD_TESTS=ON

# Build
echo "Building..."
cmake --build . --config "$BUILD_TYPE" --parallel $(nproc 2>/dev/null || echo 4)

echo "Build completed successfully!"
echo "Executable: $BUILD_DIR/optimized-pdf-viewer"

# Run tests if available
if [ -f "tests/test_annotation" ]; then
    echo "Running tests..."
    ctest --output-on-failure
fi

echo "Done!"
