#include "DocumentTab.h"
#include "AnnotationManager.h"
#include "AnnotationOverlay.h"
#include "TextSelectionOverlay.h"
#include "AdvancedZoomManager.h"
#include <QLabel>
#include <QScrollArea>
#include <QVBoxLayout>
#include <QFileInfo>
#include <QApplication>
#include <QGuiApplication>
#include <QScreen>
#include <QWheelEvent>
#include <QScrollBar>
#include <QTimer>
#include <QPropertyAnimation>
#include <QEasingCurve>
#include <QDebug>

DocumentTab::DocumentTab(QWidget *parent)
    : QWidget(parent)
{
    createUi();

    // Initialize annotation manager
    m_annotationManager = new AnnotationManager(this);
    m_pdfController.setAnnotationManager(m_annotationManager);

    // Connect PDF controller signals with Qt::QueuedConnection for thread safety
    connect(&m_pdfController, &PdfController::documentLoaded, this, &DocumentTab::onDocumentLoaded, Qt::QueuedConnection);
    connect(&m_pdfController, &PdfController::loadingProgress, this, &DocumentTab::onLoadingProgress, Qt::QueuedConnection);
    connect(&m_pdfController, &PdfController::pageReady, this, &DocumentTab::onPageReady, Qt::QueuedConnection);
    connect(&m_pdfController, &PdfController::pageWithAnnotationsReady, this,
            [this](int pageNum, const QPixmap& pixmap) {
                // Check if we're still valid before processing
                if (!m_isBeingDestroyed && m_isDocumentLoaded) {
                    onPageReady(pageNum, pixmap, true); // Assume high quality for annotated pages
                }
            }, Qt::QueuedConnection);
}

DocumentTab::~DocumentTab()
{
    // Set flag to indicate destruction is in progress
    m_isBeingDestroyed = true;

    // Disconnect all signals to prevent crashes during destruction
    disconnect();

    // Stop any ongoing PDF controller operations safely with timeout
    try {
        m_pdfController.stopAllOperations();
    } catch (const std::exception& e) {
        qWarning() << "Exception while stopping PDF controller operations:" << e.what();
    } catch (...) {
        qWarning() << "Unknown exception while stopping PDF controller operations";
    }

    // Close document first to clean up resources
    try {
        closeDocument();
    } catch (const std::exception& e) {
        qWarning() << "Exception while closing document in destructor:" << e.what();
    } catch (...) {
        qWarning() << "Unknown exception while closing document in destructor";
    }

    // Clean up overlays safely with null checks and exception handling
    if (m_annotationOverlay) {
        try {
            // Disconnect any remaining signals
            m_annotationOverlay->disconnect();
            m_annotationOverlay->setParent(nullptr);
            m_annotationOverlay->deleteLater();
            m_annotationOverlay = nullptr;
        } catch (const std::exception& e) {
            qWarning() << "Exception while cleaning up annotation overlay:" << e.what();
        } catch (...) {
            qWarning() << "Unknown exception while cleaning up annotation overlay";
        }
    }

    if (m_textSelectionOverlay) {
        try {
            m_textSelectionOverlay->disconnect();
            m_textSelectionOverlay->setParent(nullptr);
            m_textSelectionOverlay->deleteLater();
            m_textSelectionOverlay = nullptr;
        } catch (const std::exception& e) {
            qWarning() << "Exception while cleaning up text selection overlay:" << e.what();
        } catch (...) {
            qWarning() << "Unknown exception while cleaning up text selection overlay";
        }
    }

    if (m_advancedZoomManager) {
        try {
            m_advancedZoomManager->disconnect();
            m_advancedZoomManager->setParent(nullptr);
            m_advancedZoomManager->deleteLater();
            m_advancedZoomManager = nullptr;
        } catch (const std::exception& e) {
            qWarning() << "Exception while cleaning up advanced zoom manager:" << e.what();
        } catch (...) {
            qWarning() << "Unknown exception while cleaning up advanced zoom manager";
        }
    }

    // Clean up annotation manager
    if (m_annotationManager) {
        try {
            m_annotationManager->disconnect();
            m_annotationManager->setParent(nullptr);
            m_annotationManager->deleteLater();
            m_annotationManager = nullptr;
        } catch (const std::exception& e) {
            qWarning() << "Exception while cleaning up annotation manager:" << e.what();
        } catch (...) {
            qWarning() << "Unknown exception while cleaning up annotation manager";
        }
    }

    // Clean up UI components safely
    if (m_pageLabel) {
        try {
            m_pageLabel->disconnect();
            m_pageLabel = nullptr; // Will be deleted by parent
        } catch (...) {
            qWarning() << "Exception while cleaning up page label";
        }
    }

    if (m_scrollArea) {
        try {
            m_scrollArea->disconnect();
            m_scrollArea = nullptr; // Will be deleted by parent
        } catch (...) {
            qWarning() << "Exception while cleaning up scroll area";
        }
    }
}

void DocumentTab::createUi()
{
    // Create the page label with improved styling
    m_pageLabel = new QLabel(this);
    m_pageLabel->setAlignment(Qt::AlignCenter);
    m_pageLabel->setStyleSheet(
        "QLabel { "
        "    background-color: white; "
        "    border: 1px solid #d0d0d0; "
        "    border-radius: 4px; "
        "    padding: 8px; "
        "    color: #666666; "
        "}"
    );
    m_pageLabel->setMinimumSize(400, 300);
    m_pageLabel->setText(tr("No document loaded"));

    // Create scroll area with improved settings and styling
    m_scrollArea = new QScrollArea(this);
    m_scrollArea->setWidget(m_pageLabel);
    m_scrollArea->setWidgetResizable(false);
    m_scrollArea->setAlignment(Qt::AlignCenter);

    // Enable smooth scrolling and better interaction
    m_scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_scrollArea->setFrameStyle(QFrame::NoFrame);
    m_scrollArea->setStyleSheet(
        "QScrollArea { "
        "    background-color: #f5f5f5; "
        "    border: none; "
        "} "
        "QScrollBar:vertical { "
        "    background: #f0f0f0; "
        "    width: 12px; "
        "    border-radius: 6px; "
        "} "
        "QScrollBar::handle:vertical { "
        "    background: #c0c0c0; "
        "    border-radius: 6px; "
        "    min-height: 20px; "
        "} "
        "QScrollBar::handle:vertical:hover { "
        "    background: #a0a0a0; "
        "} "
        "QScrollBar:horizontal { "
        "    background: #f0f0f0; "
        "    height: 12px; "
        "    border-radius: 6px; "
        "} "
        "QScrollBar::handle:horizontal { "
        "    background: #c0c0c0; "
        "    border-radius: 6px; "
        "    min-width: 20px; "
        "} "
        "QScrollBar::handle:horizontal:hover { "
        "    background: #a0a0a0; "
        "}"
    );

    // Install event filter for mouse wheel zoom
    m_scrollArea->installEventFilter(this);

    // Create annotation overlay
    m_annotationOverlay = new AnnotationOverlay(m_scrollArea);
    m_annotationOverlay->setPageLabel(m_pageLabel);
    m_annotationOverlay->setAnnotationManager(m_annotationManager);

    // Create text selection overlay
    m_textSelectionOverlay = new TextSelectionOverlay(m_scrollArea);
    m_textSelectionOverlay->setPageLabel(m_pageLabel);
    m_textSelectionOverlay->setPdfController(&m_pdfController);
    m_textSelectionOverlay->setCurrentPage(m_currentPage);
    m_textSelectionOverlay->setZoomFactor(m_zoomFactor);

    // Connect annotation overlay signals
    connect(m_annotationOverlay, &AnnotationOverlay::requestPageRefresh,
            this, &DocumentTab::requestCurrentPage);

    // Connect text selection overlay signals
    connect(m_textSelectionOverlay, &TextSelectionOverlay::selectionChanged,
            this, &DocumentTab::textSelectionChanged);
    connect(m_textSelectionOverlay, &TextSelectionOverlay::selectionCleared,
            this, &DocumentTab::textSelectionCleared);

    // Create advanced zoom manager
    m_advancedZoomManager = new AdvancedZoomManager(this);
    m_advancedZoomManager->setDocumentTab(this);
    m_advancedZoomManager->setPdfController(&m_pdfController);
    m_advancedZoomManager->setPageLabel(m_pageLabel);
    m_advancedZoomManager->setScrollArea(m_scrollArea);

    // Connect advanced zoom signals
    connect(m_advancedZoomManager, &AdvancedZoomManager::zoomChanged,
            this, &DocumentTab::zoomChanged);
    connect(m_advancedZoomManager, &AdvancedZoomManager::smartZoomSuggestion,
            this, [this](double suggestedZoom, const QString& reason) {
                emit statusMessage(tr("Smart zoom suggestion: %1% - %2")
                                 .arg(qRound(suggestedZoom * 100)).arg(reason), 3000);
            });

    // Create layout
    QVBoxLayout* layout = new QVBoxLayout(this);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->addWidget(m_scrollArea);
}

bool DocumentTab::loadDocument(const QString& filePath)
{
    m_filePath = filePath;
    m_isDocumentLoaded = false;
    m_currentPage = 0;
    m_zoomFactor = 1.0;
    m_currentRotation = 0;
    
    m_pageLabel->setText(tr("Loading document..."));
    
    // Load document asynchronously
    m_pdfController.loadDocument(filePath);
    
    return true; // Actual success will be reported via signal
}

void DocumentTab::closeDocument()
{
    // Set document state first
    m_isDocumentLoaded = false;
    m_filePath.clear();
    m_currentPage = 0;
    m_zoomFactor = 1.0;
    m_currentRotation = 0;
    m_searchResults.clear();
    m_currentSearchIndex = -1;

    // Clear the PDF controller cache safely
    try {
        m_pdfController.clearCache();
    } catch (const std::exception& e) {
        qWarning() << "Exception occurred while clearing PDF controller cache:" << e.what();
    } catch (...) {
        qWarning() << "Unknown exception occurred while clearing PDF controller cache";
    }

    // Update UI safely with additional null checks
    if (m_pageLabel && !m_isBeingDestroyed) {
        try {
            // Check if the object is still valid
            if (m_pageLabel->metaObject()) {
                m_pageLabel->setText(tr("No document loaded"));
                m_pageLabel->clear();
            }
        } catch (const std::exception& e) {
            qWarning() << "Exception while updating page label:" << e.what();
        } catch (...) {
            qWarning() << "Unknown exception while updating page label";
        }
    }

    // Clear overlay states safely with additional checks
    if (m_annotationOverlay && !m_isBeingDestroyed) {
        try {
            if (m_annotationOverlay->metaObject()) {
                // Reset overlay to clean state
                m_annotationOverlay->update();
            }
        } catch (const std::exception& e) {
            qWarning() << "Exception while updating annotation overlay:" << e.what();
        } catch (...) {
            qWarning() << "Unknown exception while updating annotation overlay";
        }
    }

    if (m_textSelectionOverlay && !m_isBeingDestroyed) {
        try {
            if (m_textSelectionOverlay->metaObject()) {
                m_textSelectionOverlay->clearSelection();
            }
        } catch (const std::exception& e) {
            qWarning() << "Exception while clearing text selection:" << e.what();
        } catch (...) {
            qWarning() << "Unknown exception while clearing text selection";
        }
    }
}

QString DocumentTab::getFileName() const
{
    if (m_filePath.isEmpty()) {
        return tr("Untitled");
    }
    return QFileInfo(m_filePath).fileName();
}

int DocumentTab::getPageCount() const
{
    return m_pdfController.pageCount().value_or(0);
}

void DocumentTab::setCurrentPage(int page)
{
    if (!m_isDocumentLoaded) return;
    
    const int pageCount = getPageCount();
    if (page >= 0 && page < pageCount) {
        m_currentPage = page;
        m_originalPageSize = m_pdfController.getPageSize(m_currentPage);

        // Update text selection overlay
        if (m_textSelectionOverlay) {
            m_textSelectionOverlay->setCurrentPage(m_currentPage);
        }

        requestCurrentPage();
        emit pageChanged(m_currentPage);
    }
}

void DocumentTab::nextPage()
{
    if (m_currentPage < getPageCount() - 1) {
        setCurrentPage(m_currentPage + 1);
    }
}

void DocumentTab::previousPage()
{
    if (m_currentPage > 0) {
        setCurrentPage(m_currentPage - 1);
    }
}

void DocumentTab::firstPage()
{
    setCurrentPage(0);
}

void DocumentTab::lastPage()
{
    const int pageCount = getPageCount();
    if (pageCount > 0) {
        setCurrentPage(pageCount - 1);
    }
}

void DocumentTab::setZoomFactor(double factor)
{
    if (!m_isDocumentLoaded) return;

    m_fitMode = FitMode::None;
    m_zoomFactor = factor;

    // Update text selection overlay
    if (m_textSelectionOverlay) {
        m_textSelectionOverlay->setZoomFactor(m_zoomFactor);
    }

    requestCurrentPage();
    emit zoomChanged(m_zoomFactor);
}

void DocumentTab::zoomIn()
{
    setZoomFactor(m_zoomFactor * 1.25);
}

void DocumentTab::zoomOut()
{
    setZoomFactor(m_zoomFactor / 1.25);
}

void DocumentTab::zoom100()
{
    setZoomFactor(1.0);
}

void DocumentTab::fitToWindow()
{
    if (!m_isDocumentLoaded || m_originalPageSize.isEmpty()) return;

    m_fitMode = FitMode::FitToWindow;
    applyFitMode();
}

void DocumentTab::fitToWidth()
{
    if (!m_isDocumentLoaded || m_originalPageSize.isEmpty()) return;

    m_fitMode = FitMode::FitToWidth;
    applyFitMode();
}

void DocumentTab::setRotation(int rotation)
{
    if (!m_isDocumentLoaded) return;

    // Normalize rotation to be in range [0, 360) first
    int normalizedRotation = rotation % 360;
    if (normalizedRotation < 0) {
        normalizedRotation += 360;
    }

    // Round to nearest multiple of 90 degrees
    normalizedRotation = ((normalizedRotation + 45) / 90) * 90;

    // Ensure the result is still in the range [0, 360)
    normalizedRotation = normalizedRotation % 360;

    m_currentRotation = normalizedRotation;
    requestCurrentPage();
}

void DocumentTab::onDocumentLoaded(bool success, const QString& errorString)
{
    // Early exit if being destroyed
    if (m_isBeingDestroyed) {
        return;
    }

    if (success) {
        m_isDocumentLoaded = true;
        m_currentPage = 0;

        try {
            m_originalPageSize = m_pdfController.getPageSize(0);
            requestCurrentPage();
            if (isValidForOperations()) {
                emit statusMessage(tr("Document loaded successfully"), 3000);
            }
        } catch (const std::exception& e) {
            qWarning() << "Exception while processing successful document load:" << e.what();
            m_isDocumentLoaded = false;
        } catch (...) {
            qWarning() << "Unknown exception while processing successful document load";
            m_isDocumentLoaded = false;
        }
    } else {
        m_isDocumentLoaded = false;
        if (m_pageLabel && !m_isBeingDestroyed) {
            try {
                if (m_pageLabel->metaObject() && m_pageLabel->parent()) {
                    m_pageLabel->setText(tr("Failed to load document"));
                }
            } catch (const std::exception& e) {
                qWarning() << "Exception while updating page label on load failure:" << e.what();
            } catch (...) {
                qWarning() << "Unknown exception while updating page label on load failure";
            }
        }
        if (isValidForOperations()) {
            emit statusMessage(tr("Failed to load document: %1").arg(errorString), 5000);
        }
    }

    if (isValidForOperations()) {
        emit documentLoaded(success, errorString);
    }
}

void DocumentTab::onLoadingProgress(int percentage, const QString& message)
{
    // Early exit if being destroyed
    if (m_isBeingDestroyed) {
        return;
    }

    // Update the page label with loading progress
    if (m_pageLabel && !m_isBeingDestroyed) {
        try {
            if (m_pageLabel->metaObject() && m_pageLabel->parent()) {
                m_pageLabel->setText(tr("%1 (%2%)").arg(message).arg(percentage));
            }
        } catch (const std::exception& e) {
            qWarning() << "Exception while updating loading progress:" << e.what();
        } catch (...) {
            qWarning() << "Unknown exception while updating loading progress";
        }
    }

    // Emit progress signal for MainWindow to update loading overlay
    if (isValidForOperations()) {
        emit loadingProgressChanged(percentage, message);
    }
}

void DocumentTab::onPageReady(int pageNum, const QPixmap& pixmap, bool isHighQuality)
{
    // Early exit if being destroyed
    if (m_isBeingDestroyed) {
        return;
    }

    // Validate input parameters
    if (pixmap.isNull()) {
        qWarning() << "Received null pixmap in onPageReady for page" << pageNum;
        return;
    }

    if (pageNum < 0) {
        qWarning() << "Received invalid page number in onPageReady:" << pageNum;
        return;
    }

    // Only process if this is the current page
    if (pageNum == m_currentPage && m_isDocumentLoaded) {
        try {
            // Set the pixmap and adjust size for high-DPI displays
            if (m_pageLabel && !m_isBeingDestroyed) {
                // Additional safety check for object validity
                if (m_pageLabel->metaObject() && m_pageLabel->parent()) {
                    m_pageLabel->setPixmap(pixmap);

                    // Calculate the display size considering device pixel ratio
                    QSize displaySize = pixmap.size();
                    if (pixmap.devicePixelRatio() > 1.0) {
                        displaySize = pixmap.size() / pixmap.devicePixelRatio();
                    }
                    m_pageLabel->resize(displaySize);
                } else {
                    qWarning() << "Page label is invalid or has no parent in onPageReady";
                }
            }

            // Update annotation overlay with current page info
            if (m_annotationOverlay && !m_isBeingDestroyed) {
                // Check all components are valid before proceeding
                if (m_annotationOverlay->metaObject() && m_annotationOverlay->parent() &&
                    m_scrollArea && m_scrollArea->metaObject() &&
                    m_scrollArea->viewport() && m_scrollArea->viewport()->metaObject()) {

                    m_annotationOverlay->setCurrentPage(m_currentPage);
                    m_annotationOverlay->setZoomFactor(m_zoomFactor);

                    // Use effective DPI for annotation overlay
                    QScreen* screen = QGuiApplication::primaryScreen();
                    const double devicePixelRatio = screen ? screen->devicePixelRatio() : 1.0;
                    const double effectiveDpi = m_pdfController.getEffectiveDpi(m_zoomFactor, devicePixelRatio);
                    m_annotationOverlay->setDpi(effectiveDpi);
                    m_annotationOverlay->setGeometry(m_scrollArea->viewport()->rect());
                } else {
                    qWarning() << "Annotation overlay or scroll area components are invalid in onPageReady";
                }
            }

            // Update text selection overlay
            if (m_textSelectionOverlay && !m_isBeingDestroyed) {
                if (m_textSelectionOverlay->metaObject() && m_textSelectionOverlay->parent()) {
                    m_textSelectionOverlay->setCurrentPage(m_currentPage);
                    m_textSelectionOverlay->setZoomFactor(m_zoomFactor);
                } else {
                    qWarning() << "Text selection overlay is invalid in onPageReady";
                }
            }

            if (isHighQuality && !m_isBeingDestroyed) {
                emit statusMessage(tr("Page %1 displayed").arg(m_currentPage + 1), 2000);
            }
        } catch (const std::exception& e) {
            qWarning() << "Exception in onPageReady for page" << pageNum << ":" << e.what();
        } catch (...) {
            qWarning() << "Unknown exception in onPageReady for page" << pageNum;
        }
    }
}

void DocumentTab::requestCurrentPage()
{
    if (!m_isDocumentLoaded || m_isBeingDestroyed) return;

    // Safely update page label
    if (m_pageLabel && m_pageLabel->metaObject() && m_pageLabel->parent()) {
        try {
            m_pageLabel->setText(tr("Rendering page %1...").arg(m_currentPage + 1));
        } catch (const std::exception& e) {
            qWarning() << "Exception while updating page label in requestCurrentPage:" << e.what();
        } catch (...) {
            qWarning() << "Unknown exception while updating page label in requestCurrentPage";
        }
    }

    // Request page rendering
    try {
        m_pdfController.requestPage(m_currentPage, m_zoomFactor, m_currentRotation);
    } catch (const std::exception& e) {
        qWarning() << "Exception while requesting page" << m_currentPage << ":" << e.what();
    } catch (...) {
        qWarning() << "Unknown exception while requesting page" << m_currentPage;
    }
}

void DocumentTab::applyFitMode()
{
    if (!m_isDocumentLoaded || m_fitMode == FitMode::None || m_originalPageSize.isEmpty()) return;

    const QSize windowSize = m_scrollArea->viewport()->size();
    if (windowSize.isEmpty()) return;

    // Get device pixel ratio for accurate calculations
    QScreen* screen = QGuiApplication::primaryScreen();
    const double devicePixelRatio = screen ? screen->devicePixelRatio() : 1.0;

    // Convert page size from points to pixels at effective DPI
    const double effectiveDpi = m_pdfController.getEffectiveDpi(1.0, devicePixelRatio);
    const double pointsToPixels = effectiveDpi / 72.0;
    const QSizeF pagePixelSize = m_originalPageSize * pointsToPixels;

    if (m_fitMode == FitMode::FitToWindow) {
        const double widthRatio = static_cast<double>(windowSize.width() * devicePixelRatio) / pagePixelSize.width();
        const double heightRatio = static_cast<double>(windowSize.height() * devicePixelRatio) / pagePixelSize.height();
        m_zoomFactor = qMin(widthRatio, heightRatio);
    } else if (m_fitMode == FitMode::FitToWidth) {
        m_zoomFactor = static_cast<double>(windowSize.width() * devicePixelRatio) / pagePixelSize.width();
    }

    requestCurrentPage();
    emit zoomChanged(m_zoomFactor);
}

void DocumentTab::setAnnotationToolbar(AnnotationToolbar* toolbar)
{
    // Disconnect previous connections if toolbar was set before
    if (m_annotationToolbar && m_annotationOverlay) {
        disconnect(m_annotationToolbar, nullptr, m_annotationOverlay, nullptr);
    }
    
    m_annotationToolbar = toolbar;

    // Verify both objects are valid before making connections
    if (!m_annotationOverlay) {
        qWarning() << "AnnotationOverlay is null in setAnnotationToolbar";
        return;
    }
    
    if (!toolbar) {
        qWarning() << "AnnotationToolbar is null in setAnnotationToolbar";
        return;
    }

    // Verify objects have valid metaObjects (not destroyed)
    if (!m_annotationOverlay || !m_annotationOverlay->metaObject() || !toolbar->metaObject()) {
        qWarning() << "One of the objects has invalid metaObject in setAnnotationToolbar";
        return;
    }

    // Connect toolbar signals to overlay with Qt::QueuedConnection for safety
    connect(toolbar, &AnnotationToolbar::toolChanged,
            m_annotationOverlay, &AnnotationOverlay::setCurrentTool, Qt::QueuedConnection);
    connect(toolbar, &AnnotationToolbar::colorChanged,
            m_annotationOverlay, &AnnotationOverlay::setAnnotationColor, Qt::QueuedConnection);
    connect(toolbar, &AnnotationToolbar::opacityChanged,
            m_annotationOverlay, &AnnotationOverlay::setAnnotationOpacity, Qt::QueuedConnection);
    connect(toolbar, &AnnotationToolbar::lineWidthChanged,
            m_annotationOverlay, &AnnotationOverlay::setAnnotationLineWidth, Qt::QueuedConnection);

    // Set initial values safely
    try {
        m_annotationOverlay->setCurrentTool(toolbar->getCurrentTool());
        m_annotationOverlay->setAnnotationColor(toolbar->getCurrentColor());
        m_annotationOverlay->setAnnotationOpacity(toolbar->getCurrentOpacity());
        m_annotationOverlay->setAnnotationLineWidth(toolbar->getCurrentLineWidth());
    } catch (...) {
        qWarning() << "Exception occurred while setting initial annotation values";
    }
}

void DocumentTab::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);

    // Update annotation overlay geometry to match scroll area viewport
    if (m_annotationOverlay && m_annotationOverlay->metaObject() &&
        m_scrollArea && m_scrollArea->viewport()) {
        try {
            m_annotationOverlay->setGeometry(m_scrollArea->viewport()->rect());
        } catch (...) {
            qWarning() << "Exception occurred while updating annotation overlay geometry";
        }
    }

    // Reapply fit mode when window is resized
    if (m_fitMode != FitMode::None) {
        try {
            applyFitMode();
        } catch (...) {
            qWarning() << "Exception occurred while applying fit mode";
        }
    }
}

bool DocumentTab::eventFilter(QObject* object, QEvent* event)
{
    if (object == m_scrollArea && event->type() == QEvent::Wheel) {
        QWheelEvent* wheelEvent = static_cast<QWheelEvent*>(event);

        // Check if Ctrl is pressed for zooming
        if (wheelEvent->modifiers() & Qt::ControlModifier) {
            if (!m_isDocumentLoaded) return false;

            // Get the current zoom factor
            double currentZoom = m_zoomFactor;
            double newZoom = currentZoom;

            // Improved zoom calculation with smoother increments
            const int wheelDelta = wheelEvent->angleDelta().y();

            if (wheelDelta > 0) {
                // Zoom in - use adaptive zoom steps
                if (currentZoom < 0.5) {
                    newZoom = currentZoom * (1.0 + 0.2); // 20% increase for very low zoom
                } else if (currentZoom < 1.0) {
                    newZoom = currentZoom * (1.0 + 0.15); // 15% increase for low zoom
                } else if (currentZoom < 2.0) {
                    newZoom = currentZoom * (1.0 + 0.12); // 12% increase for medium zoom
                } else {
                    newZoom = currentZoom * (1.0 + 0.1); // 10% increase for high zoom
                }
                newZoom = qMin(newZoom, 10.0); // Cap at 1000%
            } else {
                // Zoom out - use adaptive zoom steps
                if (currentZoom <= 0.5) {
                    newZoom = currentZoom / (1.0 + 0.2); // 20% decrease for very low zoom
                } else if (currentZoom <= 1.0) {
                    newZoom = currentZoom / (1.0 + 0.15); // 15% decrease for low zoom
                } else if (currentZoom <= 2.0) {
                    newZoom = currentZoom / (1.0 + 0.12); // 12% decrease for medium zoom
                } else {
                    newZoom = currentZoom / (1.0 + 0.1); // 10% decrease for high zoom
                }
                newZoom = qMax(newZoom, 0.1); // Minimum 10%
            }

            // Store the current scroll position and cursor position for zoom centering
            QScrollBar* hScrollBar = m_scrollArea->horizontalScrollBar();
            QScrollBar* vScrollBar = m_scrollArea->verticalScrollBar();

            // Get cursor position relative to the page
            QPoint scrollPos = wheelEvent->position().toPoint();
            QPoint viewportPos = m_scrollArea->viewport()->mapFromGlobal(m_scrollArea->mapToGlobal(scrollPos));

            // Calculate the position relative to the current page content
            int oldScrollX = hScrollBar->value();
            int oldScrollY = vScrollBar->value();
            QPoint pagePos = QPoint(oldScrollX + viewportPos.x(), oldScrollY + viewportPos.y());

            // Calculate relative position (0.0 to 1.0) for zoom centering
            double relativeX = 0.5; // Default to center
            double relativeY = 0.5;

            if (m_pageLabel->width() > 0 && m_pageLabel->height() > 0) {
                relativeX = static_cast<double>(pagePos.x()) / m_pageLabel->width();
                relativeY = static_cast<double>(pagePos.y()) / m_pageLabel->height();
                // Clamp to valid range
                relativeX = qBound(0.0, relativeX, 1.0);
                relativeY = qBound(0.0, relativeY, 1.0);
            }

            // Apply the new zoom
            m_fitMode = FitMode::None;
            setZoomFactor(newZoom);

            // Restore scroll position to keep the zoom point centered with improved accuracy
            QTimer::singleShot(50, [this, relativeX, relativeY, viewportPos]() {
                if (m_pageLabel->width() > 0 && m_pageLabel->height() > 0) {
                    QScrollBar* hScrollBar = m_scrollArea->horizontalScrollBar();
                    QScrollBar* vScrollBar = m_scrollArea->verticalScrollBar();

                    // Calculate new scroll position to keep the cursor position stable
                    int targetPageX = static_cast<int>(relativeX * m_pageLabel->width());
                    int targetPageY = static_cast<int>(relativeY * m_pageLabel->height());

                    int newScrollX = targetPageX - viewportPos.x();
                    int newScrollY = targetPageY - viewportPos.y();

                    // Apply bounds checking
                    newScrollX = qBound(hScrollBar->minimum(), newScrollX, hScrollBar->maximum());
                    newScrollY = qBound(vScrollBar->minimum(), newScrollY, vScrollBar->maximum());

                    hScrollBar->setValue(newScrollX);
                    vScrollBar->setValue(newScrollY);
                }
            });

            return true; // Event handled
        }
    }

    return QWidget::eventFilter(object, event);
}

void DocumentTab::smoothScrollTo(int targetX, int targetY, int duration)
{
    QScrollBar* hScrollBar = m_scrollArea->horizontalScrollBar();
    QScrollBar* vScrollBar = m_scrollArea->verticalScrollBar();

    // Get current positions
    int startX = hScrollBar->value();
    int startY = vScrollBar->value();

    // Clamp target positions to valid range
    targetX = qBound(hScrollBar->minimum(), targetX, hScrollBar->maximum());
    targetY = qBound(vScrollBar->minimum(), targetY, vScrollBar->maximum());

    // If already at target, no need to animate
    if (startX == targetX && startY == targetY) {
        return;
    }

    // Create animation for horizontal scrolling
    QPropertyAnimation* hAnimation = new QPropertyAnimation(hScrollBar, "value", this);
    hAnimation->setDuration(duration);
    hAnimation->setStartValue(startX);
    hAnimation->setEndValue(targetX);
    hAnimation->setEasingCurve(QEasingCurve::OutCubic);

    // Create animation for vertical scrolling
    QPropertyAnimation* vAnimation = new QPropertyAnimation(vScrollBar, "value", this);
    vAnimation->setDuration(duration);
    vAnimation->setStartValue(startY);
    vAnimation->setEndValue(targetY);
    vAnimation->setEasingCurve(QEasingCurve::OutCubic);

    // Start both animations
    hAnimation->start(QAbstractAnimation::DeleteWhenStopped);
    vAnimation->start(QAbstractAnimation::DeleteWhenStopped);
}

void DocumentTab::setSinglePageMode()
{
    m_viewMode = ViewMode::SinglePage;
    // For now, single page mode is the default behavior
    // Future enhancement: implement proper single page layout
    requestCurrentPage();
}

void DocumentTab::setContinuousPageMode()
{
    m_viewMode = ViewMode::ContinuousPage;
    // TODO: Implement continuous page mode with vertical scrolling through all pages
    // This would require a different layout approach with multiple page widgets
    requestCurrentPage();
}

void DocumentTab::setFacingPageMode()
{
    m_viewMode = ViewMode::FacingPage;
    // TODO: Implement facing page mode showing two pages side by side
    // This would require rendering two pages and arranging them horizontally
    requestCurrentPage();
}

// Text selection functionality
void DocumentTab::setTextSelectionEnabled(bool enabled)
{
    if (m_textSelectionOverlay && m_textSelectionOverlay->metaObject()) {
        m_textSelectionOverlay->setTextSelectionEnabled(enabled);
        // When enabling text selection, disable annotation overlay to avoid conflicts
        if (m_annotationOverlay && m_annotationOverlay->metaObject()) {
            m_annotationOverlay->setVisible(!enabled);
        }
    }
}

bool DocumentTab::isTextSelectionEnabled() const
{
    return m_textSelectionOverlay ? m_textSelectionOverlay->isTextSelectionEnabled() : false;
}

bool DocumentTab::hasTextSelection() const
{
    return m_textSelectionOverlay ? m_textSelectionOverlay->hasSelection() : false;
}

QString DocumentTab::getSelectedText() const
{
    return m_textSelectionOverlay ? m_textSelectionOverlay->getSelectedText() : QString();
}

void DocumentTab::copySelectedTextToClipboard()
{
    if (m_textSelectionOverlay) {
        m_textSelectionOverlay->copySelectionToClipboard();
    }
}

void DocumentTab::clearTextSelection()
{
    if (m_textSelectionOverlay) {
        m_textSelectionOverlay->clearSelection();
    }
}

// Advanced zoom functionality
void DocumentTab::enableAdvancedZoom(bool enabled)
{
    if (m_advancedZoomManager) {
        // Advanced zoom is always enabled, this could control specific features
        Q_UNUSED(enabled);
    }
}

void DocumentTab::performSmartZoom()
{
    if (m_advancedZoomManager) {
        m_advancedZoomManager->performSmartZoom();
    }
}

void DocumentTab::enableMagnifier(bool enabled)
{
    if (m_advancedZoomManager) {
        m_advancedZoomManager->enableMagnifier(enabled);
    }
}

bool DocumentTab::isMagnifierEnabled() const
{
    return m_advancedZoomManager ? m_advancedZoomManager->isMagnifierEnabled() : false;
}

void DocumentTab::zoomToRect(const QRectF& rect)
{
    if (m_advancedZoomManager) {
        m_advancedZoomManager->zoomToRect(rect);
    }
}

// Enhanced search functionality implementation
void DocumentTab::nextSearchResult()
{
    if (m_searchResults.isEmpty()) return;

    int nextIndex = m_currentSearchIndex + 1;
    if (nextIndex >= m_searchResults.size()) {
        nextIndex = 0; // Wrap around to first result
    }

    jumpToSearchResult(nextIndex);
}

void DocumentTab::previousSearchResult()
{
    if (m_searchResults.isEmpty()) return;

    int prevIndex = m_currentSearchIndex - 1;
    if (prevIndex < 0) {
        prevIndex = m_searchResults.size() - 1; // Wrap around to last result
    }

    jumpToSearchResult(prevIndex);
}

void DocumentTab::jumpToSearchResult(int index)
{
    if (index < 0 || index >= m_searchResults.size()) return;

    m_currentSearchIndex = index;
    const SearchResult& result = m_searchResults[index];

    // Navigate to the page containing the result
    if (result.pageNumber != m_currentPage) {
        setCurrentPage(result.pageNumber);
    }

    // Highlight the current search result
    highlightSearchResults(true);

    // Emit signal for UI updates
    if (isValidForOperations()) {
        emit statusMessage(tr("Search result %1 of %2").arg(index + 1).arg(m_searchResults.size()), 2000);
    }
}

void DocumentTab::highlightSearchResults(bool highlight)
{
    if (!m_isDocumentLoaded || m_searchResults.isEmpty() || m_isBeingDestroyed) return;

    // Collect highlights for current page
    QList<QRectF> highlights;
    for (int i = 0; i < m_searchResults.size(); ++i) {
        const SearchResult& result = m_searchResults[i];
        if (result.pageNumber == m_currentPage) {
            highlights.append(result.boundingBox);
        }
    }

    try {
        if (highlight && !highlights.isEmpty()) {
            // Request page with highlights
            m_pdfController.requestPageWithHighlights(m_currentPage, m_zoomFactor, highlights, m_currentRotation);
        } else {
            // Request normal page without highlights
            requestCurrentPage();
        }
    } catch (const std::exception& e) {
        qWarning() << "Exception while highlighting search results:" << e.what();
    } catch (...) {
        qWarning() << "Unknown exception while highlighting search results";
    }
}

void DocumentTab::clearSearchHighlights()
{
    m_searchResults.clear();
    m_currentSearchIndex = -1;

    // Re-render current page without highlights
    if (m_isDocumentLoaded && !m_isBeingDestroyed) {
        try {
            requestCurrentPage();
        } catch (const std::exception& e) {
            qWarning() << "Exception while clearing search highlights:" << e.what();
        } catch (...) {
            qWarning() << "Unknown exception while clearing search highlights";
        }
    }

    if (isValidForOperations()) {
        emit statusMessage(tr("Search highlights cleared"), 1000);
    }
}

void DocumentTab::searchInCurrentPage(const QString& term, bool caseSensitive)
{
    if (!m_isDocumentLoaded || term.isEmpty() || m_isBeingDestroyed) return;

    // Use PdfController to search in current page only
    try {
        QString pageText = m_pdfController.getPageText(m_currentPage);
        if (pageText.isEmpty()) {
            if (isValidForOperations()) {
                emit statusMessage(tr("No text found on current page"), 2000);
            }
            return;
        }

        // Simple text search within current page
        QList<SearchResult> pageResults;
        Qt::CaseSensitivity sensitivity = caseSensitive ? Qt::CaseSensitive : Qt::CaseInsensitive;

        int startPos = 0;
        while (true) {
            int pos = pageText.indexOf(term, startPos, sensitivity);
            if (pos == -1) break;

            SearchResult result;
            result.pageNumber = m_currentPage;
            result.startIndex = pos;
            result.length = term.length();
            result.context = pageText.mid(qMax(0, pos - 50), 100); // 50 chars before and after

            // For now, use a default bounding box - this would need PDF text analysis for exact positioning
            result.boundingBox = QRectF(0, 0, 100, 20); // Placeholder

            pageResults.append(result);
            startPos = pos + 1;
        }

        if (!pageResults.isEmpty()) {
            m_searchResults = pageResults;
            m_currentSearchIndex = 0;
            highlightSearchResults(true);
            if (isValidForOperations()) {
                emit statusMessage(tr("Found %1 matches on current page").arg(pageResults.size()), 3000);
            }
        } else {
            if (isValidForOperations()) {
                emit statusMessage(tr("No matches found on current page"), 2000);
            }
        }

    } catch (const std::exception& e) {
        qWarning() << "Exception during page search:" << e.what();
        if (isValidForOperations()) {
            emit statusMessage(tr("Error searching current page"), 2000);
        }
    } catch (...) {
        qWarning() << "Unknown exception during page search";
        if (isValidForOperations()) {
            emit statusMessage(tr("Error searching current page"), 2000);
        }
    }
}

void DocumentTab::findNext()
{
    nextSearchResult();
}

void DocumentTab::findPrevious()
{
    previousSearchResult();
}
