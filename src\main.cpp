// main.cpp
#include "MainWindow.h"
#include "ElaIntegration.h"
#include "Logger.h"
#include "ErrorHandler.h"
#include <QApplication>
#include <QScreen>
#include <QStyleHints>
#include <QTimer>

int main(int argc, char *argv[])
{
    // Enable high-DPI scaling for better display on high-resolution monitors
    QApplication::setHighDpiScaleFactorRoundingPolicy(Qt::HighDpiScaleFactorRoundingPolicy::PassThrough);

    QApplication a(argc, argv);

    // Set application properties
    a.setApplicationName("Optimized PDF Viewer");
    a.setApplicationVersion("1.0");
    a.setOrganizationName("PDF Viewer");

    // Initialize logging and error handling
    Logger* logger = Logger::instance();
    logger->setLogLevel(LogLevel::Info);
    logger->info("Application starting up", "Main");

    ErrorHandler* errorHandler = ErrorHandler::instance();
    errorHandler->setShowUserNotifications(true);
    errorHandler->setupCrashHandler();

    // Initialize ElaWidgetTools if available
    logger->info("Initializing ElaWidgetTools", "Main");
    try {
        if (ElaIntegration::isElaWidgetsAvailable()) {
            logger->info("ElaWidgetTools available, initializing...", "Main");
            ElaIntegration::initializeElaApplication();
            logger->info("ElaWidgetTools application initialized", "Main");
            
            ElaIntegration::applyElaTheme();
            logger->info("ElaWidgetTools theme applied", "Main");
        } else {
            logger->info("ElaWidgetTools not available, using fallback", "Main");
        }
    } catch (const std::exception& e) {
        logger->warning(QString("ElaWidgetTools initialization failed: %1").arg(e.what()), "Main");
    } catch (...) {
        logger->warning("ElaWidgetTools initialization failed with unknown error", "Main");
    }

    // Configure application for better rendering (Qt6 handles high-DPI pixmaps automatically)

    // Set up a timer to detect potential deadlocks
    QTimer* deadlockTimer = new QTimer();
    deadlockTimer->setSingleShot(true);
    deadlockTimer->setInterval(30000); // 30 second timeout
    
    bool mainWindowCreated = false;
    QObject::connect(deadlockTimer, &QTimer::timeout, [&]() {
        if (!mainWindowCreated) {
            logger->error("Main window creation timeout - potential deadlock detected", "Main");
            qApp->quit();
        }
    });
    
    deadlockTimer->start();

    try {
        logger->info("Creating main window", "Main");
        MainWindow w;
        mainWindowCreated = true;
        deadlockTimer->stop();
        logger->info("Main window created successfully", "Main");
        
        logger->info("Showing main window", "Main");
        w.show();
        logger->info("Main window shown successfully", "Main");
        int result = a.exec();

        logger->info("Application shutting down normally", "Main");
        return result;

    } catch (const std::exception& e) {
        errorHandler->reportException(QException(), QString("main: %1").arg(e.what()));
        return -1;
    } catch (...) {
        errorHandler->reportError(ErrorSeverity::Fatal, ErrorCategory::System,
                                 "Unknown Exception", "An unknown exception occurred during startup",
                                 QString(), "main");
        return -1;
    }
}
