// WelcomeScreen.cpp
#include "WelcomeScreen.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QPainter>
#include <QStandardItemModel>
#include <QStandardItem>
#include <QFileInfo>
#include <QDir>
#include <QPixmap>
#include <QIcon>

// ElaWidgetTools includes
#include "ElaScrollArea.h"
#include "ElaText.h"
#include "ElaPushButton.h"
#include "ElaInteractiveCard.h"
#include "ElaPopularCard.h"
#include "ElaImageCard.h"
#include "ElaFlowLayout.h"
#include "ElaListView.h"
#include "ElaTheme.h"

WelcomeScreen::WelcomeScreen(QWidget *parent)
    : ElaWidget(parent)
    , m_scrollArea(nullptr)
    , m_contentWidget(nullptr)
    , m_mainLayout(nullptr)
    , m_headerWidget(nullptr)
    , m_titleLabel(nullptr)
    , m_subtitleLabel(nullptr)
    , m_versionLabel(nullptr)
    , m_logoCard(nullptr)
    , m_quickActionsWidget(nullptr)
    , m_quickActionsTitle(nullptr)
    , m_quickActionsLayout(nullptr)
    , m_openFileCard(nullptr)
    , m_newDocumentCard(nullptr)
    , m_settingsCard(nullptr)
    , m_recentFilesWidget(nullptr)
    , m_recentFilesTitle(nullptr)
    , m_recentFilesList(nullptr)
    , m_recentFilesModel(nullptr)
    , m_clearRecentButton(nullptr)
    , m_gettingStartedWidget(nullptr)
    , m_gettingStartedTitle(nullptr)
    , m_gettingStartedLayout(nullptr)
    , m_helpCard(nullptr)
    , m_aboutCard(nullptr)
    , m_shortcutsCard(nullptr)
    , m_footerWidget(nullptr)
    , m_footerText(nullptr)
    , m_applicationName("Optimized PDF Viewer")
    , m_applicationVersion("1.0")
    , m_applicationDescription("High-performance PDF viewer with advanced annotation capabilities")
{
    setupUI();
}

WelcomeScreen::~WelcomeScreen()
{
}

void WelcomeScreen::setupUI()
{
    // Create main scroll area
    m_scrollArea = new ElaScrollArea(this);
    m_scrollArea->setWidgetResizable(true);
    m_scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    
    // Create content widget
    m_contentWidget = new QWidget();
    m_contentWidget->setObjectName("WelcomeContentWidget");
    m_scrollArea->setWidget(m_contentWidget);
    
    // Create main layout with VS Code-inspired spacing
    m_mainLayout = new QVBoxLayout(m_contentWidget);
    m_mainLayout->setContentsMargins(60, 40, 60, 40);
    m_mainLayout->setSpacing(40);
    
    // Create sections
    createHeader();
    createQuickActions();
    createRecentFiles();
    createGettingStarted();
    createFooter();
    
    // Set up main widget layout
    QVBoxLayout* mainWidgetLayout = new QVBoxLayout(this);
    mainWidgetLayout->setContentsMargins(0, 0, 0, 0);
    mainWidgetLayout->addWidget(m_scrollArea);
    
    // Apply VS Code-inspired styling
    setStyleSheet(
        "#WelcomeContentWidget { "
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 #f8f9fa, stop:1 #ffffff); "
        "    border: none; "
        "} "
        "WelcomeScreen { "
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 #f8f9fa, stop:1 #e9ecef); "
        "}"
    );
}

void WelcomeScreen::createHeader()
{
    m_headerWidget = new QWidget();
    m_headerWidget->setFixedHeight(140);
    m_headerWidget->setStyleSheet(
        "QWidget { "
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 #ffffff, stop:1 #f8f9fa); "
        "    border-radius: 12px; "
        "    border: 1px solid #e9ecef; "
        "}"
    );

    QHBoxLayout* headerLayout = new QHBoxLayout(m_headerWidget);
    headerLayout->setContentsMargins(24, 20, 24, 20);
    headerLayout->setSpacing(24);
    
    // Logo/Icon area
    m_logoCard = new ElaImageCard();
    m_logoCard->setFixedSize(80, 80);
    m_logoCard->setBorderRadius(12);
    // Set a default icon - you can replace with actual logo
    QPixmap logoPixmap(80, 80);
    logoPixmap.fill(QColor(0, 120, 212));
    QPainter painter(&logoPixmap);
    painter.setPen(QPen(Qt::white, 2));
    painter.setFont(QFont("Arial", 24, QFont::Bold));
    painter.drawText(logoPixmap.rect(), Qt::AlignCenter, "PDF");
    m_logoCard->setCardImage(logoPixmap.toImage());
    
    // Text area
    QWidget* textWidget = new QWidget();
    QVBoxLayout* textLayout = new QVBoxLayout(textWidget);
    textLayout->setContentsMargins(0, 0, 0, 0);
    textLayout->setSpacing(5);
    
    m_titleLabel = new ElaText(m_applicationName);
    m_titleLabel->setTextPixelSize(36);
    m_titleLabel->setStyleSheet(
        "color: #1e1e1e; "
        "font-weight: 600; "
        "font-family: 'Segoe UI', system-ui, sans-serif;"
    );

    m_subtitleLabel = new ElaText(m_applicationDescription);
    m_subtitleLabel->setTextPixelSize(16);
    m_subtitleLabel->setStyleSheet(
        "color: #616161; "
        "font-weight: 400; "
        "font-family: 'Segoe UI', system-ui, sans-serif;"
    );

    m_versionLabel = new ElaText(QString("Version %1").arg(m_applicationVersion));
    m_versionLabel->setTextPixelSize(13);
    m_versionLabel->setStyleSheet(
        "color: #8e8e8e; "
        "font-weight: 400; "
        "font-family: 'Segoe UI', system-ui, sans-serif;"
    );
    
    textLayout->addWidget(m_titleLabel);
    textLayout->addWidget(m_subtitleLabel);
    textLayout->addWidget(m_versionLabel);
    textLayout->addStretch();
    
    headerLayout->addWidget(m_logoCard);
    headerLayout->addWidget(textWidget);
    headerLayout->addStretch();
    
    m_mainLayout->addWidget(m_headerWidget);
}

void WelcomeScreen::createQuickActions()
{
    m_quickActionsWidget = new QWidget();
    QVBoxLayout* quickActionsMainLayout = new QVBoxLayout(m_quickActionsWidget);
    quickActionsMainLayout->setContentsMargins(0, 0, 0, 0);
    quickActionsMainLayout->setSpacing(15);
    
    m_quickActionsTitle = new ElaText("Start");
    m_quickActionsTitle->setTextPixelSize(24);
    m_quickActionsTitle->setStyleSheet(
        "color: #1e1e1e; "
        "font-weight: 600; "
        "font-family: 'Segoe UI', system-ui, sans-serif; "
        "margin-bottom: 8px;"
    );
    
    // Create flow layout for cards
    QWidget* cardsWidget = new QWidget();
    m_quickActionsLayout = new ElaFlowLayout(0, 15, 15);
    m_quickActionsLayout->setIsAnimation(true);
    cardsWidget->setLayout(m_quickActionsLayout);
    
    // Open File card - VS Code inspired
    m_openFileCard = new ElaInteractiveCard();
    m_openFileCard->setFixedSize(240, 100);
    m_openFileCard->setTitle("Open File...");
    m_openFileCard->setSubTitle("Ctrl+O");
    m_openFileCard->setBorderRadius(6);
    m_openFileCard->setStyleSheet(
        "ElaInteractiveCard { "
        "    background: #ffffff; "
        "    border: 1px solid #e1e4e8; "
        "    font-family: 'Segoe UI', system-ui, sans-serif; "
        "} "
        "ElaInteractiveCard:hover { "
        "    background: #f6f8fa; "
        "    border: 1px solid #0366d6; "
        "}"
    );
    connect(m_openFileCard, &ElaInteractiveCard::clicked, this, &WelcomeScreen::onQuickActionClicked);

    // New Document card - VS Code inspired
    m_newDocumentCard = new ElaInteractiveCard();
    m_newDocumentCard->setFixedSize(240, 100);
    m_newDocumentCard->setTitle("New File");
    m_newDocumentCard->setSubTitle("Ctrl+N");
    m_newDocumentCard->setBorderRadius(6);
    m_newDocumentCard->setStyleSheet(
        "ElaInteractiveCard { "
        "    background: #ffffff; "
        "    border: 1px solid #e1e4e8; "
        "    font-family: 'Segoe UI', system-ui, sans-serif; "
        "} "
        "ElaInteractiveCard:hover { "
        "    background: #f6f8fa; "
        "    border: 1px solid #0366d6; "
        "}"
    );
    connect(m_newDocumentCard, &ElaInteractiveCard::clicked, this, &WelcomeScreen::onQuickActionClicked);

    // Settings card - VS Code inspired
    m_settingsCard = new ElaInteractiveCard();
    m_settingsCard->setFixedSize(240, 100);
    m_settingsCard->setTitle("Settings");
    m_settingsCard->setSubTitle("Ctrl+,");
    m_settingsCard->setBorderRadius(6);
    m_settingsCard->setStyleSheet(
        "ElaInteractiveCard { "
        "    background: #ffffff; "
        "    border: 1px solid #e1e4e8; "
        "    font-family: 'Segoe UI', system-ui, sans-serif; "
        "} "
        "ElaInteractiveCard:hover { "
        "    background: #f6f8fa; "
        "    border: 1px solid #0366d6; "
        "}"
    );
    connect(m_settingsCard, &ElaInteractiveCard::clicked, this, &WelcomeScreen::onQuickActionClicked);
    
    m_quickActionsLayout->addWidget(m_openFileCard);
    m_quickActionsLayout->addWidget(m_newDocumentCard);
    m_quickActionsLayout->addWidget(m_settingsCard);
    
    quickActionsMainLayout->addWidget(m_quickActionsTitle);
    quickActionsMainLayout->addWidget(cardsWidget);
    
    m_mainLayout->addWidget(m_quickActionsWidget);
}

void WelcomeScreen::createRecentFiles()
{
    m_recentFilesWidget = new QWidget();
    QVBoxLayout* recentFilesMainLayout = new QVBoxLayout(m_recentFilesWidget);
    recentFilesMainLayout->setContentsMargins(0, 0, 0, 0);
    recentFilesMainLayout->setSpacing(15);
    
    // Title with clear button - VS Code inspired
    QHBoxLayout* titleLayout = new QHBoxLayout();
    m_recentFilesTitle = new ElaText("Recent");
    m_recentFilesTitle->setTextPixelSize(24);
    m_recentFilesTitle->setStyleSheet(
        "color: #1e1e1e; "
        "font-weight: 600; "
        "font-family: 'Segoe UI', system-ui, sans-serif; "
        "margin-bottom: 8px;"
    );

    m_clearRecentButton = new ElaPushButton("Clear All");
    m_clearRecentButton->setFixedSize(90, 32);
    m_clearRecentButton->setStyleSheet(
        "ElaPushButton { "
        "    background: transparent; "
        "    border: 1px solid #d1d5db; "
        "    border-radius: 4px; "
        "    color: #6b7280; "
        "    font-size: 12px; "
        "    font-family: 'Segoe UI', system-ui, sans-serif; "
        "} "
        "ElaPushButton:hover { "
        "    background: #f3f4f6; "
        "    border: 1px solid #9ca3af; "
        "    color: #374151; "
        "}"
    );

    titleLayout->addWidget(m_recentFilesTitle);
    titleLayout->addStretch();
    titleLayout->addWidget(m_clearRecentButton);

    // Recent files list - VS Code inspired
    m_recentFilesList = new ElaListView();
    m_recentFilesList->setFixedHeight(240);
    m_recentFilesList->setStyleSheet(
        "ElaListView { "
        "    background: #ffffff; "
        "    border: 1px solid #e1e4e8; "
        "    border-radius: 6px; "
        "    font-family: 'Segoe UI', system-ui, sans-serif; "
        "    selection-background-color: #e3f2fd; "
        "} "
        "ElaListView::item { "
        "    padding: 8px 12px; "
        "    border-bottom: 1px solid #f1f3f4; "
        "} "
        "ElaListView::item:hover { "
        "    background: #f6f8fa; "
        "} "
        "ElaListView::item:selected { "
        "    background: #e3f2fd; "
        "    color: #1565c0; "
        "}"
    );
    m_recentFilesModel = new QStandardItemModel(this);
    m_recentFilesList->setModel(m_recentFilesModel);
    
    connect(m_recentFilesList, &QListView::clicked, this, &WelcomeScreen::onRecentFileClicked);
    
    recentFilesMainLayout->addLayout(titleLayout);
    recentFilesMainLayout->addWidget(m_recentFilesList);
    
    m_mainLayout->addWidget(m_recentFilesWidget);
}

void WelcomeScreen::createGettingStarted()
{
    m_gettingStartedWidget = new QWidget();
    QVBoxLayout* gettingStartedMainLayout = new QVBoxLayout(m_gettingStartedWidget);
    gettingStartedMainLayout->setContentsMargins(0, 0, 0, 0);
    gettingStartedMainLayout->setSpacing(15);
    
    m_gettingStartedTitle = new ElaText("Learn");
    m_gettingStartedTitle->setTextPixelSize(24);
    m_gettingStartedTitle->setStyleSheet(
        "color: #1e1e1e; "
        "font-weight: 600; "
        "font-family: 'Segoe UI', system-ui, sans-serif; "
        "margin-bottom: 8px;"
    );
    
    // Create flow layout for getting started cards
    QWidget* gettingStartedCardsWidget = new QWidget();
    m_gettingStartedLayout = new ElaFlowLayout(0, 15, 15);
    m_gettingStartedLayout->setIsAnimation(true);
    gettingStartedCardsWidget->setLayout(m_gettingStartedLayout);
    
    // Help card - VS Code inspired
    m_helpCard = new ElaPopularCard();
    m_helpCard->setFixedSize(280, 120);
    m_helpCard->setTitle("Documentation");
    m_helpCard->setSubTitle("Learn how to use PDF Viewer effectively");
    m_helpCard->setInteractiveTips("Open Docs");
    m_helpCard->setStyleSheet(
        "ElaPopularCard { "
        "    background: #ffffff; "
        "    border: 1px solid #e1e4e8; "
        "    border-radius: 6px; "
        "    font-family: 'Segoe UI', system-ui, sans-serif; "
        "} "
        "ElaPopularCard:hover { "
        "    background: #f6f8fa; "
        "    border: 1px solid #0366d6; "
        "}"
    );
    connect(m_helpCard, &ElaPopularCard::popularCardButtonClicked, this, &WelcomeScreen::onGetStartedClicked);

    // About card - VS Code inspired
    m_aboutCard = new ElaPopularCard();
    m_aboutCard->setFixedSize(280, 120);
    m_aboutCard->setTitle("About PDF Viewer");
    m_aboutCard->setSubTitle("Version information and credits");
    m_aboutCard->setInteractiveTips("Show Details");
    m_aboutCard->setStyleSheet(
        "ElaPopularCard { "
        "    background: #ffffff; "
        "    border: 1px solid #e1e4e8; "
        "    border-radius: 6px; "
        "    font-family: 'Segoe UI', system-ui, sans-serif; "
        "} "
        "ElaPopularCard:hover { "
        "    background: #f6f8fa; "
        "    border: 1px solid #0366d6; "
        "}"
    );
    connect(m_aboutCard, &ElaPopularCard::popularCardButtonClicked, this, &WelcomeScreen::onGetStartedClicked);
    
    // Shortcuts card - VS Code inspired
    m_shortcutsCard = new ElaPopularCard();
    m_shortcutsCard->setFixedSize(280, 120);
    m_shortcutsCard->setTitle("Keyboard Shortcuts");
    m_shortcutsCard->setSubTitle("Boost your productivity with shortcuts");
    m_shortcutsCard->setInteractiveTips("Show All");
    m_shortcutsCard->setStyleSheet(
        "ElaPopularCard { "
        "    background: #ffffff; "
        "    border: 1px solid #e1e4e8; "
        "    border-radius: 6px; "
        "    font-family: 'Segoe UI', system-ui, sans-serif; "
        "} "
        "ElaPopularCard:hover { "
        "    background: #f6f8fa; "
        "    border: 1px solid #0366d6; "
        "}"
    );
    connect(m_shortcutsCard, &ElaPopularCard::popularCardButtonClicked, this, &WelcomeScreen::onGetStartedClicked);
    
    m_gettingStartedLayout->addWidget(m_helpCard);
    m_gettingStartedLayout->addWidget(m_aboutCard);
    m_gettingStartedLayout->addWidget(m_shortcutsCard);
    
    gettingStartedMainLayout->addWidget(m_gettingStartedTitle);
    gettingStartedMainLayout->addWidget(gettingStartedCardsWidget);
    
    m_mainLayout->addWidget(m_gettingStartedWidget);
}

void WelcomeScreen::createFooter()
{
    m_footerWidget = new QWidget();
    m_footerWidget->setFixedHeight(40);
    
    QHBoxLayout* footerLayout = new QHBoxLayout(m_footerWidget);
    footerLayout->setContentsMargins(0, 10, 0, 10);
    
    m_footerText = new ElaText("Ready to start working with PDF documents");
    m_footerText->setTextPixelSize(12);
    m_footerText->setStyleSheet("color: #6c757d;");
    
    footerLayout->addWidget(m_footerText);
    footerLayout->addStretch();
    
    m_mainLayout->addWidget(m_footerWidget);
    m_mainLayout->addStretch();
}

void WelcomeScreen::updateRecentFiles(const QStringList& recentFiles)
{
    m_recentFiles = recentFiles;
    updateRecentFilesDisplay();
}

void WelcomeScreen::setApplicationInfo(const QString& appName, const QString& version, const QString& description)
{
    m_applicationName = appName;
    m_applicationVersion = version;
    m_applicationDescription = description;
    
    if (m_titleLabel) {
        m_titleLabel->setText(appName);
    }
    if (m_subtitleLabel) {
        m_subtitleLabel->setText(description);
    }
    if (m_versionLabel) {
        m_versionLabel->setText(QString("Version %1").arg(version));
    }
}

void WelcomeScreen::updateRecentFilesDisplay()
{
    if (!m_recentFilesModel) return;
    
    m_recentFilesModel->clear();
    
    for (const QString& filePath : m_recentFiles) {
        QFileInfo fileInfo(filePath);
        QStandardItem* item = new QStandardItem();
        item->setText(fileInfo.fileName());
        item->setToolTip(filePath);
        item->setData(filePath, Qt::UserRole);
        
        // Add file icon
        item->setIcon(QIcon(":/icons/pdf.png")); // You can add appropriate icons
        
        m_recentFilesModel->appendRow(item);
    }
    
    // Show/hide recent files section based on availability
    m_recentFilesWidget->setVisible(!m_recentFiles.isEmpty());
}

void WelcomeScreen::onQuickActionClicked()
{
    ElaInteractiveCard* card = qobject_cast<ElaInteractiveCard*>(sender());
    if (!card) return;
    
    if (card == m_openFileCard) {
        emit openFileRequested();
    } else if (card == m_newDocumentCard) {
        emit newDocumentRequested();
    } else if (card == m_settingsCard) {
        emit settingsRequested();
    }
}

void WelcomeScreen::onRecentFileClicked()
{
    QModelIndex index = m_recentFilesList->currentIndex();
    if (!index.isValid()) return;
    
    QString filePath = index.data(Qt::UserRole).toString();
    if (!filePath.isEmpty()) {
        emit recentFileSelected(filePath);
    }
}

void WelcomeScreen::onGetStartedClicked()
{
    ElaPopularCard* card = qobject_cast<ElaPopularCard*>(sender());
    if (!card) return;
    
    if (card == m_helpCard) {
        emit helpRequested();
    } else if (card == m_aboutCard) {
        emit aboutRequested();
    } else if (card == m_shortcutsCard) {
        // Could emit a specific shortcuts signal or handle differently
        emit helpRequested();
    }
}

void WelcomeScreen::paintEvent(QPaintEvent* event)
{
    ElaWidget::paintEvent(event);
}
