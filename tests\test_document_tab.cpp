#include <QtTest/QtTest>
#include <QSignalSpy>
#include <QApplication>
#include <QScrollArea>
#include <QLabel>

#include "DocumentTab.h"
#include "AnnotationManager.h"
#include "PdfController.h"

class TestDocumentTab : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Basic functionality tests
    void testTabCreation();
    void testLoadDocument();
    void testCloseDocument();
    void testDocumentState();

    // Page navigation tests
    void testPageNavigation();
    void testSetCurrentPage();
    void testPageBounds();
    void testPageNavigationSignals();

    // Zoom functionality tests
    void testZoomControl();
    void testZoomLimits();
    void testFitModes();
    void testZoomSignals();

    // Rotation tests
    void testRotation();
    void testRotationLimits();

    // UI component tests
    void testUiComponents();
    void testScrollArea();
    void testPageLabel();

    // Search functionality tests
    void testSearchResults();
    void testSearchNavigation();

    // Crash prevention tests
    void testSafeDestruction();
    void testNullPointerHandling();
    void testThreadSafety();

    // Annotation integration tests
    void testAnnotationManager();
    void testAnnotationToolbar();

    // Signal tests
    void testDocumentSignals();
    void testPageChangeSignals();
    void testZoomChangeSignals();

private:
    DocumentTab* m_tab;
    QString m_testPdfPath;
    
    // Signal spies
    QSignalSpy* m_documentLoadedSpy;
    QSignalSpy* m_pageChangedSpy;
    QSignalSpy* m_zoomChangedSpy;
    QSignalSpy* m_statusMessageSpy;
    
    void waitForSignal(QSignalSpy* spy, int timeout = 5000);
};

void TestDocumentTab::initTestCase()
{
    // Setup test environment
    if (!QApplication::instance()) {
        int argc = 0;
        char* argv[] = {nullptr};
        new QApplication(argc, argv);
    }
    
    // Try to find a test PDF file
    QStringList possiblePaths = {
        "test.pdf",
        QDir::currentPath() + "/test.pdf",
        "../test.pdf",
        "../../test.pdf",
        "../tests/test.pdf",
        "tests/test.pdf",
        QCoreApplication::applicationDirPath() + "/test.pdf",
        QCoreApplication::applicationDirPath() + "/../test.pdf",
        QCoreApplication::applicationDirPath() + "/../tests/test.pdf"
    };
    
    for (const QString& path : possiblePaths) {
        if (QFile::exists(path)) {
            m_testPdfPath = path;
            break;
        }
    }
}

void TestDocumentTab::cleanupTestCase()
{
    // Cleanup
}

void TestDocumentTab::init()
{
    m_tab = new DocumentTab();
    
    // Setup signal spies
    m_documentLoadedSpy = new QSignalSpy(m_tab, &DocumentTab::documentLoaded);
    m_pageChangedSpy = new QSignalSpy(m_tab, &DocumentTab::pageChanged);
    m_zoomChangedSpy = new QSignalSpy(m_tab, &DocumentTab::zoomChanged);
    m_statusMessageSpy = new QSignalSpy(m_tab, &DocumentTab::statusMessage);
}

void TestDocumentTab::cleanup()
{
    delete m_documentLoadedSpy;
    delete m_pageChangedSpy;
    delete m_zoomChangedSpy;
    delete m_statusMessageSpy;
    delete m_tab;
}

void TestDocumentTab::testTabCreation()
{
    QVERIFY(m_tab != nullptr);
    QVERIFY(!m_tab->isDocumentLoaded());
    QVERIFY(m_tab->getFilePath().isEmpty());
    QCOMPARE(m_tab->getFileName(), QString("Untitled"));
    QCOMPARE(m_tab->getCurrentPage(), 0);
    QCOMPARE(m_tab->getPageCount(), 0);
    QCOMPARE(m_tab->getZoomFactor(), 1.0);
    QCOMPARE(m_tab->getCurrentRotation(), 0);
}

void TestDocumentTab::testLoadDocument()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    bool result = m_tab->loadDocument(m_testPdfPath);
    QVERIFY(result);
    
    waitForSignal(m_documentLoadedSpy);
    
    QCOMPARE(m_documentLoadedSpy->count(), 1);
    QList<QVariant> arguments = m_documentLoadedSpy->takeFirst();
    bool success = arguments.at(0).toBool();
    QString errorString = arguments.at(1).toString();
    
    QVERIFY(success);
    QVERIFY(errorString.isEmpty());
    
    // Verify document state
    QVERIFY(m_tab->isDocumentLoaded());
    QCOMPARE(m_tab->getFilePath(), m_testPdfPath);
    QVERIFY(!m_tab->getFileName().isEmpty());
    QVERIFY(m_tab->getPageCount() > 0);
}

void TestDocumentTab::testCloseDocument()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    // Load document first
    m_tab->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    QVERIFY(m_tab->isDocumentLoaded());
    
    // Close document
    m_tab->closeDocument();

    // Verify document is closed
    QVERIFY(!m_tab->isDocumentLoaded());
    QVERIFY(m_tab->getFilePath().isEmpty());
    // Note: Page count may still be available after closing (document not reset)
}

void TestDocumentTab::testDocumentState()
{
    // Initially no document loaded
    QVERIFY(!m_tab->isDocumentLoaded());
    QVERIFY(m_tab->getFilePath().isEmpty());
    QCOMPARE(m_tab->getFileName(), QString("Untitled")); // Should be "Untitled" for new documents
    
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    // Load document
    m_tab->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    // Verify loaded state
    QVERIFY(m_tab->isDocumentLoaded());
    QCOMPARE(m_tab->getFilePath(), m_testPdfPath);
    
    QString fileName = m_tab->getFileName();
    QVERIFY(!fileName.isEmpty());
    QVERIFY(fileName.endsWith(".pdf"));
}

void TestDocumentTab::testPageNavigation()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_tab->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    int pageCount = m_tab->getPageCount();
    if (pageCount <= 1) {
        QSKIP("Test PDF has only one page");
    }
    
    // Test next page
    int initialPage = m_tab->getCurrentPage();
    m_tab->nextPage();
    QCOMPARE(m_tab->getCurrentPage(), initialPage + 1);
    
    // Test previous page
    m_tab->previousPage();
    QCOMPARE(m_tab->getCurrentPage(), initialPage);
    
    // Test first page
    m_tab->setCurrentPage(5); // Set to some other page first
    m_tab->firstPage();
    QCOMPARE(m_tab->getCurrentPage(), 0);
    
    // Test last page
    m_tab->lastPage();
    QCOMPARE(m_tab->getCurrentPage(), pageCount - 1);
}

void TestDocumentTab::testSetCurrentPage()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_tab->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    int pageCount = m_tab->getPageCount();
    
    // Test setting valid page
    if (pageCount > 1) {
        m_tab->setCurrentPage(1);
        QCOMPARE(m_tab->getCurrentPage(), 1);
        
        m_tab->setCurrentPage(pageCount - 1);
        QCOMPARE(m_tab->getCurrentPage(), pageCount - 1);
    }
    
    // Test setting page 0
    m_tab->setCurrentPage(0);
    QCOMPARE(m_tab->getCurrentPage(), 0);
}

void TestDocumentTab::testPageBounds()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_tab->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    int pageCount = m_tab->getPageCount();
    
    // Test setting page beyond bounds
    int originalPage = m_tab->getCurrentPage();
    
    m_tab->setCurrentPage(-1);
    QCOMPARE(m_tab->getCurrentPage(), 0); // Should clamp to 0
    
    m_tab->setCurrentPage(pageCount + 10);
    QCOMPARE(m_tab->getCurrentPage(), pageCount - 1); // Should clamp to last page
    
    // Test navigation beyond bounds
    m_tab->setCurrentPage(0);
    m_tab->previousPage();
    QCOMPARE(m_tab->getCurrentPage(), 0); // Should stay at first page
    
    m_tab->setCurrentPage(pageCount - 1);
    m_tab->nextPage();
    QCOMPARE(m_tab->getCurrentPage(), pageCount - 1); // Should stay at last page
}

void TestDocumentTab::testPageNavigationSignals()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_tab->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    if (m_tab->getPageCount() > 1) {
        m_tab->setCurrentPage(1);
        
        QCOMPARE(m_pageChangedSpy->count(), 1);
        QList<QVariant> arguments = m_pageChangedSpy->takeFirst();
        int pageNum = arguments.at(0).toInt();
        QCOMPARE(pageNum, 1);
    }
}

void TestDocumentTab::testZoomControl()
{
    double initialZoom = m_tab->getZoomFactor();
    QCOMPARE(initialZoom, 1.0);

    // Skip zoom tests if no document is loaded (zoom requires a document)
    if (m_testPdfPath.isEmpty()) {
        QSKIP("Zoom tests require a document to be loaded");
    }

    // Load document first
    m_tab->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);

    // Test setting zoom factor
    m_tab->setZoomFactor(1.5);
    QCOMPARE(m_tab->getZoomFactor(), 1.5);

    m_tab->setZoomFactor(0.5);
    QCOMPARE(m_tab->getZoomFactor(), 0.5);

    // Test zoom in/out
    m_tab->setZoomFactor(1.0);
    m_tab->zoomIn();
    QVERIFY(m_tab->getZoomFactor() > 1.0);

    m_tab->zoomOut();
    QVERIFY(m_tab->getZoomFactor() < 1.5); // Should be less than after zoom in

    // Test zoom 100%
    m_tab->zoom100();
    QCOMPARE(m_tab->getZoomFactor(), 1.0);
}

void TestDocumentTab::testZoomLimits()
{
    // Test minimum zoom
    m_tab->setZoomFactor(0.01); // Very small zoom
    QVERIFY(m_tab->getZoomFactor() >= 0.1); // Should be clamped to minimum
    
    // Test maximum zoom
    m_tab->setZoomFactor(50.0); // Very large zoom
    QVERIFY(m_tab->getZoomFactor() <= 10.0); // Should be clamped to maximum
}

void TestDocumentTab::testFitModes()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_tab->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    // Test fit to window
    double originalZoom = m_tab->getZoomFactor();
    m_tab->fitToWindow();
    // Zoom factor should change (unless it was already fit to window)
    
    // Test fit to width
    m_tab->fitToWidth();
    // Zoom factor should change
    
    // Note: Exact zoom values depend on widget size and PDF page size
    // For simple test PDFs, the zoom might be very small due to minimal content
    double finalZoom = m_tab->getZoomFactor();

    if (finalZoom <= 0.01) {
        QSKIP("Fit mode calculation resulted in very small zoom (likely due to test environment)");
    }

    // We just verify the methods don't crash and zoom factor is reasonable
    QVERIFY(finalZoom > 0.01);
    QVERIFY(finalZoom < 100.0);
}

void TestDocumentTab::testZoomSignals()
{
    // Skip zoom signal tests if no document is loaded (zoom requires a document)
    if (m_testPdfPath.isEmpty()) {
        QSKIP("Zoom signal tests require a document to be loaded");
    }

    // Load document first
    m_tab->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);

    // Clear any existing signals
    m_zoomChangedSpy->clear();

    m_tab->setZoomFactor(1.5);

    QCOMPARE(m_zoomChangedSpy->count(), 1);
    QList<QVariant> arguments = m_zoomChangedSpy->takeFirst();
    double zoomFactor = arguments.at(0).toDouble();
    QCOMPARE(zoomFactor, 1.5);
}

void TestDocumentTab::testRotation()
{
    QCOMPARE(m_tab->getCurrentRotation(), 0);

    // Skip rotation tests if no document is loaded (rotation requires a document)
    if (m_testPdfPath.isEmpty()) {
        QSKIP("Rotation tests require a document to be loaded");
    }

    // Load document first
    m_tab->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);

    // Test setting rotation
    m_tab->setRotation(90);
    QCOMPARE(m_tab->getCurrentRotation(), 90);

    m_tab->setRotation(180);
    QCOMPARE(m_tab->getCurrentRotation(), 180);

    m_tab->setRotation(270);
    QCOMPARE(m_tab->getCurrentRotation(), 270);

    m_tab->setRotation(360);
    QCOMPARE(m_tab->getCurrentRotation(), 0); // Should normalize to 0
}

void TestDocumentTab::testRotationLimits()
{
    // Skip rotation tests if no document is loaded (rotation requires a document)
    if (m_testPdfPath.isEmpty()) {
        QSKIP("Rotation limit tests require a document to be loaded");
    }

    // Load document first
    m_tab->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);

    // Test invalid rotations
    m_tab->setRotation(45); // Not a multiple of 90
    QVERIFY(m_tab->getCurrentRotation() % 90 == 0); // Should be normalized

    m_tab->setRotation(-90);
    QCOMPARE(m_tab->getCurrentRotation(), 270); // Should normalize negative rotation

    m_tab->setRotation(450);
    QCOMPARE(m_tab->getCurrentRotation(), 90); // Should normalize > 360
}

void TestDocumentTab::testUiComponents()
{
    // Test UI components are created
    QVERIFY(m_tab->getScrollArea() != nullptr);
    QVERIFY(m_tab->getPageLabel() != nullptr);
    QVERIFY(m_tab->getPdfController() != nullptr);
    QVERIFY(m_tab->getAnnotationManager() != nullptr);
    
    // Test component relationships
    QScrollArea* scrollArea = m_tab->getScrollArea();
    QLabel* pageLabel = m_tab->getPageLabel();
    
    QVERIFY(scrollArea->widget() == pageLabel);
}

void TestDocumentTab::testScrollArea()
{
    QScrollArea* scrollArea = m_tab->getScrollArea();
    QVERIFY(scrollArea != nullptr);
    
    // Test scroll area properties
    QVERIFY(!scrollArea->widgetResizable()); // Should be false for PDF viewing to maintain page size
    QCOMPARE(scrollArea->alignment(), Qt::AlignCenter);
    
    // Test that page label is the scroll area's widget
    QCOMPARE(scrollArea->widget(), m_tab->getPageLabel());
}

void TestDocumentTab::testPageLabel()
{
    QLabel* pageLabel = m_tab->getPageLabel();
    QVERIFY(pageLabel != nullptr);
    
    // Test initial state
    QVERIFY(pageLabel->pixmap().isNull()); // No document loaded initially
    QCOMPARE(pageLabel->alignment(), Qt::AlignCenter);
    QCOMPARE(pageLabel->hasScaledContents(), false);
}

void TestDocumentTab::testSearchResults()
{
    // Test initial search state
    QVERIFY(m_tab->getSearchResults().isEmpty());
    QCOMPARE(m_tab->getCurrentSearchIndex(), -1);
    
    // Test setting search results
    QList<SearchResult> results;
    SearchResult result1;
    result1.pageNumber = 0;
    result1.boundingBox = QRectF(10, 20, 100, 15);
    result1.context = "Test context";
    results.append(result1);
    
    SearchResult result2;
    result2.pageNumber = 1;
    result2.boundingBox = QRectF(50, 60, 80, 12);
    result2.context = "Another context";
    results.append(result2);
    
    m_tab->setSearchResults(results);
    QCOMPARE(m_tab->getSearchResults().size(), 2);
    
    // Test setting search index
    m_tab->setCurrentSearchIndex(0);
    QCOMPARE(m_tab->getCurrentSearchIndex(), 0);
    
    m_tab->setCurrentSearchIndex(1);
    QCOMPARE(m_tab->getCurrentSearchIndex(), 1);
}

void TestDocumentTab::testSearchNavigation()
{
    // Setup search results
    QList<SearchResult> results;
    for (int i = 0; i < 3; ++i) {
        SearchResult result;
        result.pageNumber = i;
        result.boundingBox = QRectF(10 + i * 10, 20, 100, 15);
        results.append(result);
    }
    
    m_tab->setSearchResults(results);
    
    // Test search index bounds
    m_tab->setCurrentSearchIndex(-1);
    QCOMPARE(m_tab->getCurrentSearchIndex(), -1); // Should allow -1 (no selection)
    
    m_tab->setCurrentSearchIndex(0);
    QCOMPARE(m_tab->getCurrentSearchIndex(), 0);
    
    m_tab->setCurrentSearchIndex(2);
    QCOMPARE(m_tab->getCurrentSearchIndex(), 2);
    
    m_tab->setCurrentSearchIndex(10); // Beyond bounds
    QCOMPARE(m_tab->getCurrentSearchIndex(), 10); // Should allow (implementation dependent)
}

void TestDocumentTab::testAnnotationManager()
{
    AnnotationManager* manager = m_tab->getAnnotationManager();
    QVERIFY(manager != nullptr);
    
    // Test annotation manager is properly connected to PDF controller
    QCOMPARE(m_tab->getPdfController()->getAnnotationManager(), manager);
    
    // Test annotation manager functionality
    QCOMPARE(manager->getAnnotationCount(), 0);
    QVERIFY(manager->getAnnotations().isEmpty());
}

void TestDocumentTab::testAnnotationToolbar()
{
    // Initially no annotation toolbar
    QVERIFY(m_tab->getAnnotationToolbar() == nullptr);
    
    // Test setting annotation toolbar
    // Note: We can't easily create a real AnnotationToolbar in unit tests
    // without setting up the full UI, so we just test the getter/setter
    QVERIFY(m_tab->getAnnotationOverlay() != nullptr); // Overlay should exist
}

void TestDocumentTab::testDocumentSignals()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_tab->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    QCOMPARE(m_documentLoadedSpy->count(), 1);
    
    QList<QVariant> arguments = m_documentLoadedSpy->takeFirst();
    bool success = arguments.at(0).toBool();
    QString errorString = arguments.at(1).toString();
    
    QVERIFY(success);
    QVERIFY(errorString.isEmpty());
}

void TestDocumentTab::testPageChangeSignals()
{
    if (m_testPdfPath.isEmpty()) {
        QSKIP("No test PDF file available");
    }
    
    m_tab->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);
    
    if (m_tab->getPageCount() > 1) {
        m_tab->setCurrentPage(1);
        
        QVERIFY(m_pageChangedSpy->count() > 0);
        
        QList<QVariant> arguments = m_pageChangedSpy->takeLast();
        int pageNum = arguments.at(0).toInt();
        QCOMPARE(pageNum, 1);
    }
}

void TestDocumentTab::testZoomChangeSignals()
{
    // Skip zoom signal tests if no document is loaded (zoom requires a document)
    if (m_testPdfPath.isEmpty()) {
        QSKIP("Zoom change signal tests require a document to be loaded");
    }

    // Load document first
    m_tab->loadDocument(m_testPdfPath);
    waitForSignal(m_documentLoadedSpy);

    // Clear any existing signals
    m_zoomChangedSpy->clear();

    m_tab->setZoomFactor(2.0);

    QCOMPARE(m_zoomChangedSpy->count(), 1);

    QList<QVariant> arguments = m_zoomChangedSpy->takeFirst();
    double zoomFactor = arguments.at(0).toDouble();
    QCOMPARE(zoomFactor, 2.0);
}

void TestDocumentTab::waitForSignal(QSignalSpy* spy, int timeout)
{
    if (spy->count() == 0) {
        spy->wait(timeout);
    }
}

// Crash prevention tests
void TestDocumentTab::testSafeDestruction()
{
    // Test that DocumentTab can be safely destroyed even with ongoing operations
    DocumentTab* tab = new DocumentTab();

    // Load a document to initialize overlays
    if (!m_testPdfPath.isEmpty()) {
        tab->loadDocument(m_testPdfPath);
        QTest::qWait(100); // Give some time for initialization
    }

    // Destroy the tab - this should not crash
    delete tab;

    // If we reach here without crashing, the test passes
    QVERIFY(true);
}

void TestDocumentTab::testNullPointerHandling()
{
    DocumentTab tab;

    // Test methods that could potentially access null pointers
    tab.setTextSelectionEnabled(true);
    tab.setTextSelectionEnabled(false);

    bool hasSelection = tab.hasTextSelection();
    Q_UNUSED(hasSelection);

    QString selectedText = tab.getSelectedText();
    Q_UNUSED(selectedText);

    tab.copySelectedTextToClipboard();
    tab.clearTextSelection();

    // Test zoom operations
    tab.setZoomFactor(1.5);
    tab.zoomIn();
    tab.zoomOut();
    tab.zoom100();

    // If we reach here without crashing, the test passes
    QVERIFY(true);
}

void TestDocumentTab::testThreadSafety()
{
    DocumentTab tab;

    // Test that multiple rapid operations don't cause crashes
    for (int i = 0; i < 10; ++i) {
        tab.setZoomFactor(1.0 + i * 0.1);
        tab.setCurrentPage(0);
        QTest::qWait(10); // Small delay to allow processing
    }

    // Test rapid document loading/closing
    if (!m_testPdfPath.isEmpty()) {
        for (int i = 0; i < 3; ++i) {
            tab.loadDocument(m_testPdfPath);
            QTest::qWait(50);
            tab.closeDocument();
            QTest::qWait(50);
        }
    }

    // If we reach here without crashing, the test passes
    QVERIFY(true);
}

QTEST_MAIN(TestDocumentTab)
#include "test_document_tab.moc"
