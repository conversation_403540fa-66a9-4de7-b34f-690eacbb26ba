#include "CrashLogPackager.h"
#include "Logger.h"
#include <QApplication>
#include <QStandardPaths>
#include <QCryptographicHash>
#include <QTextStream>
#include <QDirIterator>
#include <QSysInfo>
#include <QCoreApplication>
#include <QLibraryInfo>
#include <QDebug>

const QString CrashLogPackager::MANIFEST_FILENAME = "package_manifest.json";
const QString CrashLogPackager::PACKAGE_VERSION = "1.0";

CrashLogPackager::CrashLogPackager(QObject *parent)
    : QObject(parent)
    , m_includeSystemInfo(true)
    , m_includeDebugSymbols(false)
    , m_includeApplicationBinary(false)
    , m_compressionEnabled(true)
    , m_compressionLevel(DEFAULT_COMPRESSION_LEVEL)
{
}

CrashLogPackager::~CrashLogPackager()
{
}

QString CrashLogPackager::createPackage(const QString& crashLogDirectory, const QString& outputPath)
{
    QMutexLocker locker(&m_mutex);
    
    if (!QDir(crashLogDirectory).exists()) {
        m_lastError = QString("Crash log directory does not exist: %1").arg(crashLogDirectory);
        emit packageError(m_lastError);
        return QString();
    }
    
    // Find crash reports
    QStringList crashReports = findCrashReports(crashLogDirectory);
    if (crashReports.isEmpty()) {
        m_lastError = "No crash reports found in the specified directory";
        emit packageError(m_lastError);
        return QString();
    }
    
    // Create temporary directory for package assembly
    QTemporaryDir tempDir;
    if (!tempDir.isValid()) {
        m_lastError = "Failed to create temporary directory for packaging";
        emit packageError(m_lastError);
        return QString();
    }
    
    emit packageProgress(10, "Creating package structure...");
    
    // Create package structure
    if (!createPackageStructure(tempDir.path())) {
        return QString();
    }
    
    emit packageProgress(20, "Copying crash reports...");
    
    // Copy crash reports
    QString crashReportsDir = QDir(tempDir.path()).absoluteFilePath("crash_reports");
    if (!copyFiles(crashReports, crashReportsDir)) {
        return QString();
    }
    
    emit packageProgress(40, "Gathering system information...");
    
    // Add system information if requested
    if (m_includeSystemInfo) {
        QString systemInfoPath = QDir(tempDir.path()).absoluteFilePath("system_info.json");
        QFile systemInfoFile(systemInfoPath);
        if (systemInfoFile.open(QIODevice::WriteOnly | QIODevice::Text)) {
            QTextStream out(&systemInfoFile);
            out << gatherSystemDiagnostics();
            systemInfoFile.close();
        }
    }
    
    emit packageProgress(60, "Adding application information...");
    
    // Add application information
    QString appInfoPath = QDir(tempDir.path()).absoluteFilePath("application_info.json");
    QFile appInfoFile(appInfoPath);
    if (appInfoFile.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&appInfoFile);
        out << gatherApplicationInfo();
        appInfoFile.close();
    }
    
    emit packageProgress(70, "Adding debug files...");
    
    // Add debug files if requested
    if (m_includeDebugSymbols) {
        QStringList debugFiles = findRelevantDebugFiles();
        if (!debugFiles.isEmpty()) {
            QString debugDir = QDir(tempDir.path()).absoluteFilePath("debug_files");
            QDir().mkpath(debugDir);
            copyFiles(debugFiles, debugDir);
        }
    }
    
    emit packageProgress(80, "Creating manifest...");
    
    // Create package info and manifest
    PackageInfo packageInfo;
    packageInfo.crashReportCount = crashReports.size();
    packageInfo.totalSize = getDirectorySize(tempDir.path());
    packageInfo.compressionType = m_compressionEnabled ? "zip" : "none";
    
    if (!createManifest(tempDir.path(), packageInfo)) {
        return QString();
    }
    
    emit packageProgress(90, "Finalizing package...");
    
    // Determine output path
    QString finalOutputPath = outputPath;
    if (finalOutputPath.isEmpty()) {
        QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
        QString filename = QString("crash_package_%1.%2")
            .arg(timestamp)
            .arg(m_compressionEnabled ? "zip" : "tar");
        finalOutputPath = QDir(crashLogDirectory).absoluteFilePath(filename);
    }
    
    // Compress or copy the package
    bool success = false;
    if (m_compressionEnabled) {
        success = compressPackage(tempDir.path(), finalOutputPath);
    } else {
        // For uncompressed packages, just copy the directory
        success = QDir().rename(tempDir.path(), finalOutputPath);
    }
    
    if (!success) {
        return QString();
    }
    
    emit packageProgress(100, "Package created successfully");
    
    // Calculate final checksum
    packageInfo.packagePath = finalOutputPath;
    packageInfo.checksum = calculateChecksum(finalOutputPath);
    
    emit packageCreated(finalOutputPath, packageInfo);
    
    Logger* logger = Logger::instance();
    logger->info(QString("Crash log package created: %1").arg(finalOutputPath), "CrashLogPackager");
    
    return finalOutputPath;
}

QString CrashLogPackager::createQuickPackage(const QString& crashLogDirectory)
{
    // Quick package with minimal options
    setIncludeSystemInfo(true);
    setIncludeDebugSymbols(false);
    setIncludeApplicationBinary(false);
    setCompressionEnabled(true);
    
    return createPackage(crashLogDirectory);
}

void CrashLogPackager::setIncludeSystemInfo(bool include)
{
    m_includeSystemInfo = include;
}

void CrashLogPackager::setIncludeDebugSymbols(bool include)
{
    m_includeDebugSymbols = include;
}

void CrashLogPackager::setIncludeApplicationBinary(bool include)
{
    m_includeApplicationBinary = include;
}

void CrashLogPackager::setCompressionEnabled(bool enabled)
{
    m_compressionEnabled = enabled;
}

void CrashLogPackager::setCompressionLevel(int level)
{
    m_compressionLevel = qBound(1, level, 9);
}

QString CrashLogPackager::getLastError() const
{
    return m_lastError;
}

QStringList CrashLogPackager::findCrashReports(const QString& directory)
{
    QStringList crashReports;
    QDir dir(directory);
    
    // Look for crash report files (JSON and TXT)
    QStringList filters;
    filters << "crash_*.json" << "crash_*.txt";
    
    QFileInfoList files = dir.entryInfoList(filters, QDir::Files | QDir::Readable);
    for (const QFileInfo& fileInfo : files) {
        crashReports.append(fileInfo.absoluteFilePath());
    }
    
    return crashReports;
}

QString CrashLogPackager::calculateChecksum(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return QString();
    }
    
    QCryptographicHash hash(QCryptographicHash::Sha256);
    if (hash.addData(&file)) {
        return hash.result().toHex();
    }
    
    return QString();
}

qint64 CrashLogPackager::getDirectorySize(const QString& dirPath)
{
    qint64 totalSize = 0;
    QDirIterator it(dirPath, QDirIterator::Subdirectories);
    
    while (it.hasNext()) {
        it.next();
        if (it.fileInfo().isFile()) {
            totalSize += it.fileInfo().size();
        }
    }
    
    return totalSize;
}

bool CrashLogPackager::createPackageStructure(const QString& tempDir)
{
    QDir dir(tempDir);
    
    // Create subdirectories
    if (!dir.mkpath("crash_reports") ||
        !dir.mkpath("system_info") ||
        !dir.mkpath("debug_files")) {
        m_lastError = "Failed to create package directory structure";
        emit packageError(m_lastError);
        return false;
    }
    
    return true;
}

bool CrashLogPackager::copyFiles(const QStringList& sourceFiles, const QString& destDir)
{
    QDir().mkpath(destDir);
    
    for (const QString& sourceFile : sourceFiles) {
        QFileInfo sourceInfo(sourceFile);
        QString destFile = QDir(destDir).absoluteFilePath(sourceInfo.fileName());
        
        if (!QFile::copy(sourceFile, destFile)) {
            m_lastError = QString("Failed to copy file: %1 to %2").arg(sourceFile, destFile);
            emit packageError(m_lastError);
            return false;
        }
    }
    
    return true;
}

QString CrashLogPackager::gatherSystemDiagnostics()
{
    QJsonObject systemInfo;
    
    systemInfo["os"] = QSysInfo::prettyProductName();
    systemInfo["kernel"] = QSysInfo::kernelType() + " " + QSysInfo::kernelVersion();
    systemInfo["architecture"] = QSysInfo::currentCpuArchitecture();
    systemInfo["hostname"] = QSysInfo::machineHostName();
    systemInfo["qt_version"] = QT_VERSION_STR;
    systemInfo["qt_runtime_version"] = qVersion();
    systemInfo["build_abi"] = QSysInfo::buildAbi();
    systemInfo["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    QJsonDocument doc(systemInfo);
    return doc.toJson(QJsonDocument::Indented);
}

QString CrashLogPackager::gatherApplicationInfo()
{
    QJsonObject appInfo;
    
    appInfo["name"] = QCoreApplication::applicationName();
    appInfo["version"] = QCoreApplication::applicationVersion();
    appInfo["organization"] = QCoreApplication::organizationName();
    appInfo["executable_path"] = QCoreApplication::applicationFilePath();
    appInfo["working_directory"] = QDir::currentPath();
    appInfo["arguments"] = QJsonArray::fromStringList(QCoreApplication::arguments());
    
    QJsonDocument doc(appInfo);
    return doc.toJson(QJsonDocument::Indented);
}

QStringList CrashLogPackager::findRelevantDebugFiles()
{
    QStringList debugFiles;

    // Look for common debug file extensions
    QStringList debugExtensions;
    debugExtensions << "*.pdb" << "*.dSYM" << "*.debug" << "*.sym";

    // Search in application directory
    QString appDir = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath();
    QDir dir(appDir);

    for (const QString& extension : debugExtensions) {
        QFileInfoList files = dir.entryInfoList(QStringList() << extension, QDir::Files);
        for (const QFileInfo& fileInfo : files) {
            debugFiles.append(fileInfo.absoluteFilePath());
        }
    }

    return debugFiles;
}

bool CrashLogPackager::compressPackage(const QString& sourceDir, const QString& outputPath)
{
    // Try ZIP compression first, fallback to TAR if needed
    if (outputPath.endsWith(".zip", Qt::CaseInsensitive)) {
        return compressWithZip(sourceDir, outputPath);
    } else {
        return compressWithTar(sourceDir, outputPath);
    }
}

bool CrashLogPackager::compressWithZip(const QString& sourceDir, const QString& outputPath)
{
    // For now, use a simple approach with QProcess to call system zip
    // In a production environment, you might want to use a library like QuaZip

    QProcess zipProcess;
    QStringList arguments;

#ifdef Q_OS_WIN
    // Use PowerShell Compress-Archive on Windows
    arguments << "-Command"
              << QString("Compress-Archive -Path '%1\\*' -DestinationPath '%2' -Force")
                 .arg(QDir::toNativeSeparators(sourceDir))
                 .arg(QDir::toNativeSeparators(outputPath));

    zipProcess.start("powershell", arguments);
#else
    // Use zip command on Unix-like systems
    arguments << "-r" << outputPath << ".";
    zipProcess.setWorkingDirectory(sourceDir);
    zipProcess.start("zip", arguments);
#endif

    if (!zipProcess.waitForStarted()) {
        m_lastError = "Failed to start compression process";
        emit packageError(m_lastError);
        return false;
    }

    if (!zipProcess.waitForFinished(30000)) { // 30 second timeout
        m_lastError = "Compression process timed out";
        emit packageError(m_lastError);
        zipProcess.kill();
        return false;
    }

    if (zipProcess.exitCode() != 0) {
        m_lastError = QString("Compression failed: %1").arg(QString::fromLocal8Bit(zipProcess.readAllStandardError()));
        emit packageError(m_lastError);
        return false;
    }

    return true;
}

bool CrashLogPackager::compressWithTar(const QString& sourceDir, const QString& outputPath)
{
    QProcess tarProcess;
    QStringList arguments;

    arguments << "-czf" << outputPath << "-C" << sourceDir << ".";
    tarProcess.start("tar", arguments);

    if (!tarProcess.waitForStarted()) {
        m_lastError = "Failed to start tar compression process";
        emit packageError(m_lastError);
        return false;
    }

    if (!tarProcess.waitForFinished(30000)) {
        m_lastError = "Tar compression process timed out";
        emit packageError(m_lastError);
        tarProcess.kill();
        return false;
    }

    if (tarProcess.exitCode() != 0) {
        m_lastError = QString("Tar compression failed: %1").arg(QString::fromLocal8Bit(tarProcess.readAllStandardError()));
        emit packageError(m_lastError);
        return false;
    }

    return true;
}

bool CrashLogPackager::createManifest(const QString& packageDir, const PackageInfo& info)
{
    QJsonObject manifest;

    manifest["package_version"] = PACKAGE_VERSION;
    manifest["created_at"] = info.createdAt.toString(Qt::ISODate);
    manifest["crash_report_count"] = info.crashReportCount;
    manifest["debug_file_count"] = info.debugFileCount;
    manifest["total_size"] = info.totalSize;
    manifest["compression_type"] = info.compressionType;
    manifest["application_name"] = QCoreApplication::applicationName();
    manifest["application_version"] = QCoreApplication::applicationVersion();

    // Add file listing
    QJsonArray fileList;
    QDirIterator it(packageDir, QDirIterator::Subdirectories);
    while (it.hasNext()) {
        it.next();
        if (it.fileInfo().isFile() && it.fileName() != MANIFEST_FILENAME) {
            QJsonObject fileInfo;
            fileInfo["path"] = it.filePath().mid(packageDir.length() + 1); // Relative path
            fileInfo["size"] = it.fileInfo().size();
            fileInfo["modified"] = it.fileInfo().lastModified().toString(Qt::ISODate);
            fileList.append(fileInfo);
        }
    }
    manifest["files"] = fileList;

    QString manifestPath = QDir(packageDir).absoluteFilePath(MANIFEST_FILENAME);
    QFile manifestFile(manifestPath);
    if (!manifestFile.open(QIODevice::WriteOnly | QIODevice::Text)) {
        m_lastError = QString("Failed to create manifest file: %1").arg(manifestPath);
        emit packageError(m_lastError);
        return false;
    }

    QJsonDocument doc(manifest);
    QTextStream out(&manifestFile);
    out << doc.toJson(QJsonDocument::Indented);
    manifestFile.close();

    return true;
}

bool CrashLogPackager::validatePackage(const QString& packagePath)
{
    // Basic validation - check if file exists and has reasonable size
    QFileInfo packageInfo(packagePath);
    if (!packageInfo.exists()) {
        m_lastError = "Package file does not exist";
        return false;
    }

    if (packageInfo.size() == 0) {
        m_lastError = "Package file is empty";
        return false;
    }

    // Additional validation could include:
    // - Checking file integrity
    // - Verifying manifest
    // - Validating checksums

    return true;
}

void CrashLogPackager::onCompressionProgress()
{
    // This would be called by compression process if we had progress monitoring
    // For now, it's a placeholder for future implementation
}
