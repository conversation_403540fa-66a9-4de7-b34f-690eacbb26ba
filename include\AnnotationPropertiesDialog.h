#ifndef ANNOTATIONPROPERTIESDIALOG_H
#define ANNOTATIONPROPERTIESDIALOG_H

#include <QDialog>
#include <QColor>
#include <QFont>
#include "Annotation.h"

class QLineEdit;
class QTextEdit;
class QSpinBox;
class QDoubleSpinBox;
class QSlider;
class QLabel;
class QPushButton;
class QComboBox;
class QFontComboBox;
class QGroupBox;
class QDateTimeEdit;

class AnnotationPropertiesDialog : public QDialog
{
    Q_OBJECT

public:
    explicit AnnotationPropertiesDialog(Annotation* annotation, QWidget *parent = nullptr);
    ~AnnotationPropertiesDialog();

    // Get modified properties
    QColor getColor() const { return m_color; }
    qreal getOpacity() const { return m_opacity; }
    qreal getLineWidth() const { return m_lineWidth; }
    QString getContent() const { return m_content; }
    QString getAuthor() const { return m_author; }
    QFont getFont() const { return m_font; }
    
    // Check if properties were modified
    bool wasModified() const { return m_modified; }

private slots:
    void onColorButtonClicked();
    void onOpacityChanged(int value);
    void onLineWidthChanged(double value);
    void onContentChanged();
    void onAuthorChanged();
    void onFontChanged();
    void onAccepted();
    void onRejected();

private:
    void setupUi();
    void setupGeneralTab();
    void setupAppearanceTab();
    void setupContentTab();
    void loadAnnotationProperties();
    void updateColorButton();
    void updatePreview();

    // Annotation reference
    Annotation* m_annotation;
    
    // Property values
    QColor m_color;
    qreal m_opacity;
    qreal m_lineWidth;
    QString m_content;
    QString m_author;
    QFont m_font;
    bool m_modified = false;
    
    // UI components
    QLineEdit* m_authorEdit;
    QTextEdit* m_contentEdit;
    QLineEdit* m_idEdit;
    QDateTimeEdit* m_createdEdit;
    QDateTimeEdit* m_modifiedEdit;
    
    // Appearance controls
    QPushButton* m_colorButton;
    QSlider* m_opacitySlider;
    QLabel* m_opacityLabel;
    QDoubleSpinBox* m_lineWidthSpinBox;
    
    // Font controls (for text annotations)
    QFontComboBox* m_fontComboBox;
    QSpinBox* m_fontSizeSpinBox;
    QPushButton* m_boldButton;
    QPushButton* m_italicButton;
    
    // Preview
    QLabel* m_previewLabel;
    
    // Groupboxes
    QGroupBox* m_generalGroup;
    QGroupBox* m_appearanceGroup;
    QGroupBox* m_contentGroup;
    QGroupBox* m_fontGroup;
};

#endif // ANNOTATIONPROPERTIESDIALOG_H
