#include "AnnotationOverlay.h"
#include "AnnotationManager.h"
#include "Annotation.h"
#include "AnnotationCommand.h"
#include "AnnotationPropertiesDialog.h"
#include <QLabel>
#include <QPainter>
#include <QMouseEvent>
#include <QKeyEvent>
#include <QContextMenuEvent>
#include <QApplication>
#include <QInputDialog>
#include <QMenu>
#include <QAction>
#include <QDebug>

AnnotationOverlay::AnnotationOverlay(QWidget *parent)
    : QWidget(parent)
{
    setFocusPolicy(Qt::StrongFocus);
    setAttribute(Qt::WA_TransparentForMouseEvents, false);
    setMouseTracking(true);
    updateCursor();
}

AnnotationOverlay::~AnnotationOverlay()
{
    cancelAnnotationCreation();
}

QPointF AnnotationOverlay::pixelToPdfCoordinates(const QPointF& pixelPoint) const
{
    if (!m_pageLabel) return pixelPoint;
    
    // Convert from widget coordinates to PDF coordinates (points)
    const double pointsToPixels = (m_dpi * m_zoomFactor) / 72.0;
    
    // Get the position relative to the page label
    QPointF labelPoint = pixelPoint;
    if (m_pageLabel->parent()) {
        labelPoint = m_pageLabel->mapFromParent(pixelPoint.toPoint());
    }
    
    // Convert to PDF coordinates
    return QPointF(labelPoint.x() / pointsToPixels, labelPoint.y() / pointsToPixels);
}

QPointF AnnotationOverlay::pdfToPixelCoordinates(const QPointF& pdfPoint) const
{
    if (!m_pageLabel) return pdfPoint;
    
    // Convert from PDF coordinates (points) to widget coordinates
    const double pointsToPixels = (m_dpi * m_zoomFactor) / 72.0;
    
    QPointF pixelPoint(pdfPoint.x() * pointsToPixels, pdfPoint.y() * pointsToPixels);
    
    // Convert to parent coordinates if needed
    if (m_pageLabel->parent()) {
        pixelPoint = m_pageLabel->mapToParent(pixelPoint.toPoint());
    }
    
    return pixelPoint;
}

QRectF AnnotationOverlay::pixelToPdfRect(const QRectF& pixelRect) const
{
    QPointF topLeft = pixelToPdfCoordinates(pixelRect.topLeft());
    QPointF bottomRight = pixelToPdfCoordinates(pixelRect.bottomRight());
    return QRectF(topLeft, bottomRight);
}

QRectF AnnotationOverlay::pdfToPixelRect(const QRectF& pdfRect) const
{
    QPointF topLeft = pdfToPixelCoordinates(pdfRect.topLeft());
    QPointF bottomRight = pdfToPixelCoordinates(pdfRect.bottomRight());
    return QRectF(topLeft, bottomRight);
}

void AnnotationOverlay::updateCursor()
{
    switch (m_currentTool) {
    case AnnotationTool::Select:
        setCursor(Qt::ArrowCursor);
        break;
    case AnnotationTool::Highlight:
        setCursor(Qt::IBeamCursor);
        break;
    case AnnotationTool::Note:
        setCursor(Qt::PointingHandCursor);
        break;
    case AnnotationTool::Drawing:
        setCursor(Qt::CrossCursor);
        break;
    case AnnotationTool::Rectangle:
    case AnnotationTool::Circle:
    case AnnotationTool::Arrow:
        setCursor(Qt::CrossCursor);
        break;
    case AnnotationTool::Text:
        setCursor(Qt::IBeamCursor);
        break;
    default:
        setCursor(Qt::ArrowCursor);
        break;
    }
}

void AnnotationOverlay::paintEvent(QPaintEvent* event)
{
    Q_UNUSED(event);
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Draw creation preview
    if (m_isCreating) {
        drawCreationPreview(&painter);
    }
    
    // Draw selection handles
    if (m_selectedAnnotation) {
        drawSelectionHandles(&painter);
    }
}

void AnnotationOverlay::mousePressEvent(QMouseEvent* event)
{
    if (event->button() != Qt::LeftButton) {
        QWidget::mousePressEvent(event);
        return;
    }
    
    setFocus(); // Ensure we receive key events
    
    QPointF position = event->position();
    m_startPosition = position;
    m_currentPosition = position;
    m_lastPosition = position;
    
    // Handle different tools
    switch (m_currentTool) {
    case AnnotationTool::Select:
        handleSelectionTool(position, true, false, false);
        break;
    case AnnotationTool::Highlight:
        handleHighlightTool(position, true, false, false);
        break;
    case AnnotationTool::Note:
        handleNoteTool(position, true, false, false);
        break;
    case AnnotationTool::Drawing:
        handleDrawingTool(position, true, false, false);
        break;
    case AnnotationTool::Rectangle:
    case AnnotationTool::Circle:
    case AnnotationTool::Arrow:
        handleShapeTool(position, true, false, false);
        break;
    case AnnotationTool::Text:
        handleTextTool(position, true, false, false);
        break;
    default:
        break;
    }
    
    update();
}

void AnnotationOverlay::mouseMoveEvent(QMouseEvent* event)
{
    QPointF position = event->position();
    m_currentPosition = position;
    
    if (event->buttons() & Qt::LeftButton) {
        // Handle different tools during drag
        switch (m_currentTool) {
        case AnnotationTool::Select:
            handleSelectionTool(position, false, true, false);
            break;
        case AnnotationTool::Highlight:
            handleHighlightTool(position, false, true, false);
            break;
        case AnnotationTool::Drawing:
            handleDrawingTool(position, false, true, false);
            break;
        case AnnotationTool::Rectangle:
        case AnnotationTool::Circle:
        case AnnotationTool::Arrow:
            handleShapeTool(position, false, true, false);
            break;
        default:
            break;
        }
    }
    
    m_lastPosition = position;
    update();
}

void AnnotationOverlay::mouseReleaseEvent(QMouseEvent* event)
{
    if (event->button() != Qt::LeftButton) {
        QWidget::mouseReleaseEvent(event);
        return;
    }
    
    QPointF position = event->position();
    m_currentPosition = position;
    
    // Handle different tools on release
    switch (m_currentTool) {
    case AnnotationTool::Select:
        handleSelectionTool(position, false, false, true);
        break;
    case AnnotationTool::Highlight:
        handleHighlightTool(position, false, false, true);
        break;
    case AnnotationTool::Note:
        handleNoteTool(position, false, false, true);
        break;
    case AnnotationTool::Drawing:
        handleDrawingTool(position, false, false, true);
        break;
    case AnnotationTool::Rectangle:
    case AnnotationTool::Circle:
    case AnnotationTool::Arrow:
        handleShapeTool(position, false, false, true);
        break;
    case AnnotationTool::Text:
        handleTextTool(position, false, false, true);
        break;
    default:
        break;
    }
    
    // Reset states
    m_isCreating = false;
    m_isDragging = false;
    m_isMovingAnnotation = false;
    m_isResizing = false;
    m_creatingAnnotation = nullptr;
    m_currentDrawing = nullptr;
    
    update();
}

void AnnotationOverlay::keyPressEvent(QKeyEvent* event)
{
    switch (event->key()) {
    case Qt::Key_Escape:
        cancelAnnotationCreation();
        deselectAllAnnotations();
        update();
        break;
    case Qt::Key_Delete:
        if (m_selectedAnnotation && m_annotationManager) {
            // Use undo command for deletion
            auto command = std::make_unique<DeleteAnnotationCommand>(
                m_annotationManager, m_selectedAnnotation->getId());
            m_annotationManager->pushCommand(command.release());
            m_selectedAnnotation = nullptr;
            emit requestPageRefresh();
            update();
        }
        break;
    case Qt::Key_Z:
        if (event->modifiers() & Qt::ControlModifier) {
            if (event->modifiers() & Qt::ShiftModifier) {
                // Ctrl+Shift+Z = Redo
                if (m_annotationManager && m_annotationManager->canRedo()) {
                    m_annotationManager->redo();
                    emit requestPageRefresh();
                    update();
                }
            } else {
                // Ctrl+Z = Undo
                if (m_annotationManager && m_annotationManager->canUndo()) {
                    m_annotationManager->undo();
                    emit requestPageRefresh();
                    update();
                }
            }
        }
        break;
    case Qt::Key_Y:
        if (event->modifiers() & Qt::ControlModifier) {
            // Ctrl+Y = Redo (alternative)
            if (m_annotationManager && m_annotationManager->canRedo()) {
                m_annotationManager->redo();
                emit requestPageRefresh();
                update();
            }
        }
        break;
    default:
        QWidget::keyPressEvent(event);
        break;
    }
}

void AnnotationOverlay::cancelAnnotationCreation()
{
    if (m_creatingAnnotation && m_annotationManager) {
        m_annotationManager->removeAnnotation(m_creatingAnnotation);
    }
    
    m_isCreating = false;
    m_creatingAnnotation = nullptr;
    m_currentDrawing = nullptr;
    m_previewPath = QPainterPath();
    m_previewRect = QRectF();
    m_drawingPoints.clear();
}

void AnnotationOverlay::selectAnnotationAt(const QPointF& position)
{
    if (!m_annotationManager) return;
    
    QPointF pdfPos = pixelToPdfCoordinates(position);
    Annotation* annotation = m_annotationManager->getAnnotationAt(pdfPos, m_currentPageNumber);
    
    if (annotation != m_selectedAnnotation) {
        deselectAllAnnotations();
        
        if (annotation) {
            m_selectedAnnotation = annotation;
            m_annotationManager->selectAnnotation(annotation);
            emit annotationSelected(annotation);
        }
    }
}

void AnnotationOverlay::deselectAllAnnotations()
{
    if (m_annotationManager) {
        m_annotationManager->deselectAll();
    }

    m_selectedAnnotation = nullptr;
    emit annotationDeselected();
}

void AnnotationOverlay::handleSelectionTool(const QPointF& position, bool isPress, bool isMove, bool isRelease)
{
    if (isPress) {
        // Check if clicking on a resize handle first
        ResizeHandle handle = getResizeHandleAt(position);
        if (handle != ResizeHandle::None && m_selectedAnnotation) {
            startResize(handle, position);
        } else {
            selectAnnotationAt(position);
            if (m_selectedAnnotation) {
                m_isMovingAnnotation = true;
                QPointF pdfPos = pixelToPdfCoordinates(position);
                QRectF bounds = m_selectedAnnotation->getBoundingRect();
                m_selectionOffset = pdfPos - bounds.topLeft();
            }
        }
    } else if (isMove) {
        if (m_isResizing && m_selectedAnnotation) {
            updateResize(position);
        } else if (m_isMovingAnnotation && m_selectedAnnotation) {
            QPointF pdfPos = pixelToPdfCoordinates(position);
            QPointF lastPdfPos = pixelToPdfCoordinates(m_lastPosition);
            QPointF offset = pdfPos - lastPdfPos;
            m_selectedAnnotation->move(offset);
        } else if (m_selectedAnnotation) {
            // Update cursor based on resize handle
            ResizeHandle handle = getResizeHandleAt(position);
            if (handle != ResizeHandle::None) {
                setCursor(getResizeCursor(handle));
            } else {
                setCursor(Qt::ArrowCursor);
            }
        }
    } else if (isRelease) {
        if (m_isResizing) {
            finishResize();
        } else if (m_isMovingAnnotation) {
            emit requestPageRefresh();
        }
        m_isMovingAnnotation = false;
        m_isResizing = false;
    }
}

void AnnotationOverlay::handleHighlightTool(const QPointF& position, bool isPress, bool isMove, bool isRelease)
{
    if (isPress) {
        m_isCreating = true;
        m_previewRect = QRectF(position, QSizeF(0, 0));
    } else if (isMove && m_isCreating) {
        m_previewRect = QRectF(m_startPosition, position).normalized();
    } else if (isRelease && m_isCreating) {
        QRectF pixelRect = QRectF(m_startPosition, position).normalized();
        if (pixelRect.width() > 5 && pixelRect.height() > 5) {
            QRectF pdfRect = pixelToPdfRect(pixelRect);
            QList<QRectF> quads;
            quads.append(pdfRect);

            if (m_annotationManager) {
                HighlightAnnotation* highlight = m_annotationManager->createHighlight(m_currentPageNumber, quads);
                highlight->setColor(m_annotationColor);
                highlight->setOpacity(m_annotationOpacity);
                emit annotationCreated(highlight);
                emit requestPageRefresh();
            }
        }
        m_previewRect = QRectF();
    }
}

void AnnotationOverlay::handleNoteTool(const QPointF& position, bool isPress, bool isMove, bool isRelease)
{
    Q_UNUSED(isMove);

    if (isPress) {
        // Note creation happens on press, not drag
    } else if (isRelease) {
        QPointF pdfPos = pixelToPdfCoordinates(position);

        if (m_annotationManager) {
            NoteAnnotation* note = m_annotationManager->createNote(m_currentPageNumber, pdfPos);
            note->setColor(m_annotationColor);
            note->setOpacity(m_annotationOpacity);

            // Optionally prompt for note content
            bool ok;
            QString text = QInputDialog::getText(this, tr("Add Note"),
                                               tr("Note content:"), QLineEdit::Normal,
                                               QString(), &ok);
            if (ok) {
                note->setContent(text);
            }

            emit annotationCreated(note);
            emit requestPageRefresh();
        }
    }
}

void AnnotationOverlay::handleDrawingTool(const QPointF& position, bool isPress, bool isMove, bool isRelease)
{
    if (isPress) {
        m_isCreating = true;
        m_drawingPoints.clear();
        m_drawingPoints.append(position);
        m_previewPath = QPainterPath();
        m_previewPath.moveTo(position);

        if (m_annotationManager) {
            m_currentDrawing = m_annotationManager->createDrawing(m_currentPageNumber);
            m_currentDrawing->setColor(m_annotationColor);
            m_currentDrawing->setOpacity(m_annotationOpacity);
            m_currentDrawing->setLineWidth(m_annotationLineWidth);

            QPointF pdfPos = pixelToPdfCoordinates(position);
            m_currentDrawing->moveTo(pdfPos);
        }
    } else if (isMove && m_isCreating) {
        m_drawingPoints.append(position);
        m_previewPath.lineTo(position);

        if (m_currentDrawing) {
            QPointF pdfPos = pixelToPdfCoordinates(position);
            m_currentDrawing->addPoint(pdfPos);
        }
    } else if (isRelease && m_isCreating) {
        if (m_currentDrawing) {
            emit annotationCreated(m_currentDrawing);
            emit requestPageRefresh();
        }
        m_previewPath = QPainterPath();
        m_drawingPoints.clear();
    }
}

void AnnotationOverlay::handleShapeTool(const QPointF& position, bool isPress, bool isMove, bool isRelease)
{
    if (isPress) {
        m_isCreating = true;
        m_previewRect = QRectF(position, QSizeF(0, 0));
    } else if (isMove && m_isCreating) {
        m_previewRect = QRectF(m_startPosition, position).normalized();
    } else if (isRelease && m_isCreating) {
        QRectF pixelRect = QRectF(m_startPosition, position).normalized();
        if (pixelRect.width() > 5 && pixelRect.height() > 5) {
            QRectF pdfRect = pixelToPdfRect(pixelRect);

            ShapeAnnotation::ShapeType shapeType;
            switch (m_currentTool) {
            case AnnotationTool::Rectangle:
                shapeType = ShapeAnnotation::ShapeType::Rectangle;
                break;
            case AnnotationTool::Circle:
                shapeType = ShapeAnnotation::ShapeType::Circle;
                break;
            case AnnotationTool::Arrow:
                shapeType = ShapeAnnotation::ShapeType::Arrow;
                break;
            default:
                shapeType = ShapeAnnotation::ShapeType::Rectangle;
                break;
            }

            if (m_annotationManager) {
                ShapeAnnotation* shape = m_annotationManager->createShape(m_currentPageNumber, shapeType, pdfRect);
                shape->setColor(m_annotationColor);
                shape->setOpacity(m_annotationOpacity);
                shape->setLineWidth(m_annotationLineWidth);
                emit annotationCreated(shape);
                emit requestPageRefresh();
            }
        }
        m_previewRect = QRectF();
    }
}

void AnnotationOverlay::handleTextTool(const QPointF& position, bool isPress, bool isMove, bool isRelease)
{
    Q_UNUSED(isMove);

    if (isPress) {
        // Text creation happens on press, not drag
    } else if (isRelease) {
        // Create a default text box size
        QRectF pixelRect(position.x() - 50, position.y() - 15, 100, 30);
        QRectF pdfRect = pixelToPdfRect(pixelRect);

        // Prompt for text content
        bool ok;
        QString text = QInputDialog::getText(this, tr("Add Text"),
                                           tr("Text content:"), QLineEdit::Normal,
                                           QString(), &ok);
        if (ok && !text.isEmpty()) {
            if (m_annotationManager) {
                TextAnnotation* textAnnotation = m_annotationManager->createText(m_currentPageNumber, pdfRect, text);
                textAnnotation->setColor(m_annotationColor);
                textAnnotation->setOpacity(m_annotationOpacity);
                emit annotationCreated(textAnnotation);
                emit requestPageRefresh();
            }
        }
    }
}

void AnnotationOverlay::drawCreationPreview(QPainter* painter)
{
    painter->save();

    QColor previewColor = m_annotationColor;
    previewColor.setAlphaF(0.3); // Make preview semi-transparent

    QPen previewPen(previewColor, m_annotationLineWidth);
    previewPen.setStyle(Qt::DashLine);
    painter->setPen(previewPen);

    switch (m_currentTool) {
    case AnnotationTool::Highlight:
        if (!m_previewRect.isEmpty()) {
            painter->fillRect(m_previewRect, QBrush(previewColor));
        }
        break;

    case AnnotationTool::Drawing:
        if (!m_previewPath.isEmpty()) {
            QPen drawPen(previewColor, m_annotationLineWidth);
            drawPen.setCapStyle(Qt::RoundCap);
            drawPen.setJoinStyle(Qt::RoundJoin);
            painter->setPen(drawPen);
            painter->drawPath(m_previewPath);
        }
        break;

    case AnnotationTool::Rectangle:
        if (!m_previewRect.isEmpty()) {
            painter->drawRect(m_previewRect);
        }
        break;

    case AnnotationTool::Circle:
        if (!m_previewRect.isEmpty()) {
            painter->drawEllipse(m_previewRect);
        }
        break;

    case AnnotationTool::Arrow:
        if (!m_previewRect.isEmpty()) {
            // Draw arrow from top-left to bottom-right
            QPointF start = m_previewRect.topLeft();
            QPointF end = m_previewRect.bottomRight();
            painter->drawLine(start, end);

            // Draw arrowhead
            QPointF direction = end - start;
            double length = sqrt(direction.x() * direction.x() + direction.y() * direction.y());
            if (length > 10) {
                direction /= length;
                QPointF perpendicular(-direction.y(), direction.x());

                double arrowLength = 15;
                QPointF arrowPoint1 = end - direction * arrowLength + perpendicular * arrowLength * 0.5;
                QPointF arrowPoint2 = end - direction * arrowLength - perpendicular * arrowLength * 0.5;

                painter->drawLine(end, arrowPoint1);
                painter->drawLine(end, arrowPoint2);
            }
        }
        break;

    default:
        break;
    }

    painter->restore();
}

void AnnotationOverlay::drawSelectionHandles(QPainter* painter)
{
    if (!m_selectedAnnotation) return;

    painter->save();

    // Get annotation bounds in pixel coordinates
    QRectF pdfBounds = m_selectedAnnotation->getBoundingRect();
    QRectF pixelBounds = pdfToPixelRect(pdfBounds);

    // Draw selection rectangle
    QPen selectionPen(Qt::blue, 1);
    selectionPen.setStyle(Qt::DashLine);
    painter->setPen(selectionPen);
    painter->setBrush(Qt::NoBrush);
    painter->drawRect(pixelBounds);

    // Draw corner handles
    const double handleSize = 8;
    QBrush handleBrush(Qt::blue);
    painter->setBrush(handleBrush);
    painter->setPen(QPen(Qt::blue, 1));

    // Top-left handle
    painter->drawRect(pixelBounds.topLeft().x() - handleSize/2,
                     pixelBounds.topLeft().y() - handleSize/2,
                     handleSize, handleSize);

    // Top-right handle
    painter->drawRect(pixelBounds.topRight().x() - handleSize/2,
                     pixelBounds.topRight().y() - handleSize/2,
                     handleSize, handleSize);

    // Bottom-left handle
    painter->drawRect(pixelBounds.bottomLeft().x() - handleSize/2,
                     pixelBounds.bottomLeft().y() - handleSize/2,
                     handleSize, handleSize);

    // Bottom-right handle
    painter->drawRect(pixelBounds.bottomRight().x() - handleSize/2,
                     pixelBounds.bottomRight().y() - handleSize/2,
                     handleSize, handleSize);

    painter->restore();
}

AnnotationOverlay::ResizeHandle AnnotationOverlay::getResizeHandleAt(const QPointF& position) const
{
    if (!m_selectedAnnotation) return ResizeHandle::None;

    // Get annotation bounds in pixel coordinates
    QRectF pdfBounds = m_selectedAnnotation->getBoundingRect();
    QRectF pixelBounds = pdfToPixelRect(pdfBounds);

    const double handleSize = 8;
    const double tolerance = handleSize / 2;

    // Check corner handles first (they have priority)
    if (QRectF(pixelBounds.topLeft().x() - tolerance, pixelBounds.topLeft().y() - tolerance,
               handleSize, handleSize).contains(position)) {
        return ResizeHandle::TopLeft;
    }
    if (QRectF(pixelBounds.topRight().x() - tolerance, pixelBounds.topRight().y() - tolerance,
               handleSize, handleSize).contains(position)) {
        return ResizeHandle::TopRight;
    }
    if (QRectF(pixelBounds.bottomLeft().x() - tolerance, pixelBounds.bottomLeft().y() - tolerance,
               handleSize, handleSize).contains(position)) {
        return ResizeHandle::BottomLeft;
    }
    if (QRectF(pixelBounds.bottomRight().x() - tolerance, pixelBounds.bottomRight().y() - tolerance,
               handleSize, handleSize).contains(position)) {
        return ResizeHandle::BottomRight;
    }

    return ResizeHandle::None;
}

void AnnotationOverlay::startResize(ResizeHandle handle, const QPointF& position)
{
    if (!m_selectedAnnotation) return;

    m_isResizing = true;
    m_currentResizeHandle = handle;
    m_originalBounds = m_selectedAnnotation->getBoundingRect();
    m_resizeStartPosition = pixelToPdfCoordinates(position);
    setCursor(getResizeCursor(handle));
}

void AnnotationOverlay::updateResize(const QPointF& position)
{
    if (!m_isResizing || !m_selectedAnnotation) return;

    QPointF pdfPos = pixelToPdfCoordinates(position);
    QPointF delta = pdfPos - m_resizeStartPosition;

    QRectF newBounds = m_originalBounds;

    switch (m_currentResizeHandle) {
    case ResizeHandle::TopLeft:
        newBounds.setTopLeft(m_originalBounds.topLeft() + delta);
        break;
    case ResizeHandle::TopRight:
        newBounds.setTopRight(m_originalBounds.topRight() + QPointF(delta.x(), delta.y()));
        break;
    case ResizeHandle::BottomLeft:
        newBounds.setBottomLeft(m_originalBounds.bottomLeft() + QPointF(delta.x(), delta.y()));
        break;
    case ResizeHandle::BottomRight:
        newBounds.setBottomRight(m_originalBounds.bottomRight() + delta);
        break;
    default:
        return;
    }

    // Ensure minimum size
    const double minSize = 10.0; // Minimum size in PDF coordinates
    if (newBounds.width() < minSize) {
        if (m_currentResizeHandle == ResizeHandle::TopLeft ||
            m_currentResizeHandle == ResizeHandle::BottomLeft) {
            newBounds.setLeft(newBounds.right() - minSize);
        } else {
            newBounds.setRight(newBounds.left() + minSize);
        }
    }
    if (newBounds.height() < minSize) {
        if (m_currentResizeHandle == ResizeHandle::TopLeft ||
            m_currentResizeHandle == ResizeHandle::TopRight) {
            newBounds.setTop(newBounds.bottom() - minSize);
        } else {
            newBounds.setBottom(newBounds.top() + minSize);
        }
    }

    m_selectedAnnotation->resize(newBounds);
    update();
}

void AnnotationOverlay::finishResize()
{
    if (m_isResizing) {
        emit requestPageRefresh();
        m_isResizing = false;
        m_currentResizeHandle = ResizeHandle::None;
        setCursor(Qt::ArrowCursor);
    }
}

QCursor AnnotationOverlay::getResizeCursor(ResizeHandle handle) const
{
    switch (handle) {
    case ResizeHandle::TopLeft:
    case ResizeHandle::BottomRight:
        return Qt::SizeFDiagCursor;
    case ResizeHandle::TopRight:
    case ResizeHandle::BottomLeft:
        return Qt::SizeBDiagCursor;
    default:
        return Qt::ArrowCursor;
    }
}

void AnnotationOverlay::contextMenuEvent(QContextMenuEvent* event)
{
    QPointF position = event->pos();
    showContextMenu(position);
}

void AnnotationOverlay::showContextMenu(const QPointF& position)
{
    if (!m_annotationManager) return;

    QPointF pdfPos = pixelToPdfCoordinates(position);
    Annotation* annotation = m_annotationManager->getAnnotationAt(pdfPos, m_currentPageNumber);

    if (annotation) {
        createAnnotationContextMenu(annotation, position);
    } else {
        createGeneralContextMenu(position);
    }
}

void AnnotationOverlay::createAnnotationContextMenu(Annotation* annotation, const QPointF& position)
{
    QMenu contextMenu(this);

    // Select annotation if not already selected
    if (annotation != m_selectedAnnotation) {
        selectAnnotationAt(position);
    }

    // Annotation properties action
    QAction* propertiesAction = contextMenu.addAction(tr("Properties..."));
    connect(propertiesAction, &QAction::triggered, [this, annotation]() {
        AnnotationPropertiesDialog dialog(annotation, this);
        if (dialog.exec() == QDialog::Accepted && dialog.wasModified()) {
            // Apply changes using undo commands
            if (dialog.getColor() != annotation->getColor()) {
                auto command = std::make_unique<ChangeAnnotationPropertyCommand>(
                    m_annotationManager, annotation->getId(),
                    ChangeAnnotationPropertyCommand::PropertyType::Color,
                    QVariant::fromValue(annotation->getColor()),
                    QVariant::fromValue(dialog.getColor()));
                m_annotationManager->pushCommand(command.release());
            }

            if (dialog.getOpacity() != annotation->getOpacity()) {
                auto command = std::make_unique<ChangeAnnotationPropertyCommand>(
                    m_annotationManager, annotation->getId(),
                    ChangeAnnotationPropertyCommand::PropertyType::Opacity,
                    annotation->getOpacity(),
                    dialog.getOpacity());
                m_annotationManager->pushCommand(command.release());
            }

            if (dialog.getLineWidth() != annotation->getLineWidth()) {
                auto command = std::make_unique<ChangeAnnotationPropertyCommand>(
                    m_annotationManager, annotation->getId(),
                    ChangeAnnotationPropertyCommand::PropertyType::LineWidth,
                    annotation->getLineWidth(),
                    dialog.getLineWidth());
                m_annotationManager->pushCommand(command.release());
            }

            if (dialog.getContent() != annotation->getContent()) {
                auto command = std::make_unique<ChangeAnnotationPropertyCommand>(
                    m_annotationManager, annotation->getId(),
                    ChangeAnnotationPropertyCommand::PropertyType::Content,
                    annotation->getContent(),
                    dialog.getContent());
                m_annotationManager->pushCommand(command.release());
            }

            if (dialog.getAuthor() != annotation->getAuthor()) {
                annotation->setAuthor(dialog.getAuthor());
            }

            emit requestPageRefresh();
        }
    });

    contextMenu.addSeparator();

    // Edit content action (for notes and text)
    if (annotation->getType() == AnnotationType::Note || annotation->getType() == AnnotationType::Text) {
        QAction* editAction = contextMenu.addAction(tr("Edit Content..."));
        connect(editAction, &QAction::triggered, [this, annotation]() {
            bool ok;
            QString currentContent = annotation->getContent();
            QString newContent = QInputDialog::getText(this, tr("Edit Content"),
                                                     tr("Content:"), QLineEdit::Normal,
                                                     currentContent, &ok);
            if (ok) {
                annotation->setContent(newContent);
                emit requestPageRefresh();
            }
        });
        contextMenu.addSeparator();
    }

    // Color submenu
    QMenu* colorMenu = contextMenu.addMenu(tr("Color"));

    QList<QColor> colors = {Qt::yellow, Qt::red, Qt::green, Qt::blue, Qt::cyan, Qt::magenta, QColor(255, 165, 0)}; // Orange
    QStringList colorNames = {tr("Yellow"), tr("Red"), tr("Green"), tr("Blue"), tr("Cyan"), tr("Magenta"), tr("Orange")};

    for (int i = 0; i < colors.size(); ++i) {
        QAction* colorAction = colorMenu->addAction(colorNames[i]);
        QPixmap colorPixmap(16, 16);
        colorPixmap.fill(colors[i]);
        colorAction->setIcon(QIcon(colorPixmap));

        connect(colorAction, &QAction::triggered, [this, annotation, colors, i]() {
            annotation->setColor(colors[i]);
            emit requestPageRefresh();
        });
    }

    contextMenu.addSeparator();

    // Copy action
    QAction* copyAction = contextMenu.addAction(tr("Copy"));
    connect(copyAction, &QAction::triggered, [this]() {
        // TODO: Implement annotation copying
        qDebug() << "Copy annotation";
    });

    // Delete action
    QAction* deleteAction = contextMenu.addAction(tr("Delete"));
    connect(deleteAction, &QAction::triggered, [this, annotation]() {
        if (m_annotationManager) {
            m_annotationManager->removeAnnotation(annotation);
            m_selectedAnnotation = nullptr;
            emit requestPageRefresh();
        }
    });

    // Show the context menu
    contextMenu.exec(mapToGlobal(position.toPoint()));
}

void AnnotationOverlay::createGeneralContextMenu(const QPointF& position)
{
    QMenu contextMenu(this);

    // Quick annotation creation actions
    QAction* highlightAction = contextMenu.addAction(tr("Add Highlight"));
    connect(highlightAction, &QAction::triggered, [this, position]() {
        // Create a small highlight at the clicked position
        QRectF pixelRect(position.x() - 25, position.y() - 5, 50, 10);
        QRectF pdfRect = pixelToPdfRect(pixelRect);
        QList<QRectF> quads;
        quads.append(pdfRect);

        if (m_annotationManager) {
            HighlightAnnotation* highlight = m_annotationManager->createHighlight(m_currentPageNumber, quads);
            highlight->setColor(m_annotationColor);
            highlight->setOpacity(m_annotationOpacity);
            emit annotationCreated(highlight);
            emit requestPageRefresh();
        }
    });

    QAction* noteAction = contextMenu.addAction(tr("Add Note"));
    connect(noteAction, &QAction::triggered, [this, position]() {
        QPointF pdfPos = pixelToPdfCoordinates(position);

        if (m_annotationManager) {
            NoteAnnotation* note = m_annotationManager->createNote(m_currentPageNumber, pdfPos);
            note->setColor(m_annotationColor);
            note->setOpacity(m_annotationOpacity);

            // Prompt for note content
            bool ok;
            QString text = QInputDialog::getText(this, tr("Add Note"),
                                               tr("Note content:"), QLineEdit::Normal,
                                               QString(), &ok);
            if (ok) {
                note->setContent(text);
            }

            emit annotationCreated(note);
            emit requestPageRefresh();
        }
    });

    contextMenu.addSeparator();

    // Paste action (if clipboard has annotations)
    QAction* pasteAction = contextMenu.addAction(tr("Paste"));
    pasteAction->setEnabled(false); // TODO: Enable when clipboard has annotations
    connect(pasteAction, &QAction::triggered, [this]() {
        // TODO: Implement annotation pasting
        qDebug() << "Paste annotations";
    });

    // Show the context menu
    contextMenu.exec(mapToGlobal(position.toPoint()));
}
