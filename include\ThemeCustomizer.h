#ifndef THEMECUSTOMIZER_H
#define THEMECUSTOMIZER_H

#include "ElaIntegration.h"
#include <QColorDialog>

class ThemeCustomizer : public ElaContentDialog
{
    Q_OBJECT

public:
    explicit ThemeCustomizer(QWidget *parent = nullptr);
    ~ThemeCustomizer();

    // Theme management
    void loadCurrentTheme();
    void applyCustomTheme();
    void resetToDefaults();
    
    // Accent color management
    void setAccentColor(const QColor& color);
    QColor getAccentColor() const;
    
    // Window effects
    void setWindowTransparency(int transparency);
    int getWindowTransparency() const;
    
    void setBlurEffect(bool enabled);
    bool getBlurEffect() const;

signals:
    void themeChanged();
    void accentColorChanged(const QColor& color);
    void transparencyChanged(int transparency);

private slots:
    void onAccentColorClicked();
    void onTransparencyChanged(int value);
    void onBlurToggled(bool enabled);
    void onPresetSelected(int index);
    void onApplyClicked();
    void onResetClicked();

private:
    void setupUI();
    void createColorSection();
    void createEffectsSection();
    void createPresetsSection();
    void updatePreview();
    void updateAccentColorButton();
    
    // UI Components
    ElaTab* m_tabWidget;
    
    // Color customization
    QWidget* m_colorTab;
    ElaPushButton* m_accentColorButton;
    ElaText* m_accentColorLabel;
    QWidget* m_colorPreview;
    
    // Effects
    QWidget* m_effectsTab;
    ElaSliderWidget* m_transparencySlider;
    ElaText* m_transparencyLabel;
    ElaCheck* m_blurEffectCheck;
    ElaCheck* m_shadowEffectCheck;
    ElaCheck* m_animationsCheck;
    
    // Presets
    QWidget* m_presetsTab;
    ElaCombo* m_presetCombo;
    QScrollArea* m_presetPreview;
    
    // Preview
    QWidget* m_previewWidget;
    ElaText* m_previewTitle;
    ElaPushButton* m_previewButton;
    ElaProgress* m_previewProgress;
    
    // Current settings
    QColor m_currentAccentColor;
    int m_currentTransparency;
    bool m_currentBlurEffect;
    bool m_currentShadowEffect;
    bool m_currentAnimations;
    
    // Theme presets
    struct ThemePreset {
        QString name;
        QColor accentColor;
        int transparency;
        bool blurEffect;
        QString description;
    };
    
    QList<ThemePreset> m_presets;
    
    // Constants
    static const int PREVIEW_WIDTH = 300;
    static const int PREVIEW_HEIGHT = 200;
    static const int COLOR_BUTTON_SIZE = 40;
};

#endif // THEMECUSTOMIZER_H
