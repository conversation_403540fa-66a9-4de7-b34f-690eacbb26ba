# PDF Viewer - Developer Guide

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Build System](#build-system)
3. [Code Structure](#code-structure)
4. [ElaWidgetTools Integration](#elawidgettools-integration)
5. [Theme System](#theme-system)
6. [Logging and Error Handling](#logging-and-error-handling)
7. [Testing Framework](#testing-framework)
8. [Contributing Guidelines](#contributing-guidelines)

## Architecture Overview

### Design Principles

- **Modern UI**: Microsoft Office-style ribbon interface using ElaWidgetTools
- **Modular Design**: Separation of concerns with clear component boundaries
- **Performance**: Efficient PDF rendering with caching and preloading
- **Extensibility**: Plugin-ready architecture for future enhancements
- **Cross-Platform**: Qt6-based for Windows, macOS, and Linux support

### Core Components

#### MainWindow (ElaWindow)

- **Purpose**: Main application window with ribbon interface
- **Key Features**:
  - ElaWindow-based modern window frame
  - Ribbon-style navigation bar
  - Tabbed document interface
  - Dockable panels (thumbnails, outline, search)
  - Modern status bar with progress indication

#### DocumentTab

- **Purpose**: Individual PDF document container
- **Responsibilities**:
  - PDF rendering and display
  - Page navigation and zoom controls
  - Annotation overlay management
  - Search result highlighting

#### PdfController

- **Purpose**: PDF processing and rendering engine
- **Features**:
  - Asynchronous PDF loading
  - Page caching system
  - Thumbnail generation
  - Text extraction for search

#### WelcomeScreen

- **Purpose**: VS Code-inspired welcome interface
- **Components**:
  - Recent files list with metadata
  - Quick action cards
  - Modern card-based layout
  - Branding and version information

## Build System

### CMake Configuration

```cmake
cmake_minimum_required(VERSION 3.16)
project(optimized-pdf-viewer)

# Qt6 and ElaWidgetTools integration
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Gui PrintSupport)
find_package(PkgConfig REQUIRED)

# ElaWidgetTools library
set(ELA_WIDGETS_ENABLED ON)
target_compile_definitions(${PROJECT_NAME} PRIVATE ELA_WIDGETS_ENABLED)
```

### Dependencies

- **Qt6**: Core, Widgets, Gui, PrintSupport modules
- **ElaWidgetTools**: Modern UI component library
- **Poppler-Qt6**: PDF rendering engine
- **CMake 3.16+**: Build system

### Build Instructions

```bash
# Clone repository
git clone <repository-url>
cd qt-pdf-render

# Create build directory
mkdir build && cd build

# Configure with CMake
cmake .. -DCMAKE_BUILD_TYPE=Release

# Build
cmake --build . --config Release

# Run tests (optional)
ctest --output-on-failure
```

## Code Structure

### Directory Layout

```
qt-pdf-render/
├── src/                    # Source files
│   ├── main.cpp           # Application entry point
│   ├── MainWindow.cpp     # Main window implementation
│   ├── DocumentTab.cpp    # Document tab widget
│   ├── PdfController.cpp  # PDF processing engine
│   ├── WelcomeScreen.cpp  # Welcome screen component
│   ├── LoadingOverlay.cpp # Loading animation overlay
│   ├── Logger.cpp         # Logging system
│   └── ElaIntegration.cpp # ElaWidgetTools integration
├── include/               # Header files
├── docs/                  # Documentation
├── tests/                 # Unit and integration tests
├── resources/             # Application resources
└── CMakeLists.txt        # Build configuration
```

### Key Classes

#### MainWindow Class

```cpp
class MainWindow : public ElaWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void openDocument();
    void onDocumentLoaded(bool success, const QString& errorString);
    void onThemeChanged();

private:
    void setupUI();
    void createRibbonInterface();
    void createDockPanels();
    void applyTheme(ElaThemeType::ThemeMode themeMode);
    
    // UI Components
    ElaTab* m_tabWidget;
    WelcomeScreen* m_welcomeScreen;
    LoadingOverlay* m_loadingOverlay;
};
```

#### ElaIntegration Module

```cpp
class ElaIntegration
{
public:
    static void initializeElaApplication();
    static void applyElaTheme();
    static void configureElaComponents();
    
private:
    static bool s_initialized;
};
```

## ElaWidgetTools Integration

### Component Mapping

| Qt Widget | ElaWidget | Usage |
|-----------|-----------|-------|
| QMainWindow | ElaWindow | Main application window |
| QDialog | ElaContentDialog | Settings, document info |
| QTabWidget | ElaTab | Document tabs |
| QPushButton | ElaPushButton | Action buttons |
| QLabel | ElaText/ElaLabel | Text display |
| QProgressBar | ElaProgress | Loading indicators |
| QMenuBar | ElaMenuBar | Application menu |
| QToolBar | ElaToolBar | Annotation toolbar |
| QStatusBar | ElaStatusBar | Status information |

### Theme Integration

```cpp
// Apply theme changes
void MainWindow::applyTheme(ElaThemeType::ThemeMode themeMode)
{
    eTheme->setThemeMode(themeMode);
    
    // Theme change automatically propagates to all Ela components
    // Custom styling may need manual updates
}
```

### Custom Components

```cpp
// Modern context menu
ModernContextMenu* menu = new ModernContextMenu(this);
menu->addActionWithIcon(ElaIconType::FolderOpen, tr("Open"), "Ctrl+O");
menu->addStyledSeparator();
menu->exec(QCursor::pos());
```

## Theme System

### Theme Architecture

- **ElaTheme Integration**: Leverages ElaWidgetTools theme system
- **Dynamic Switching**: Runtime theme changes without restart
- **Persistence**: Theme preferences saved in QSettings
- **Custom Themes**: Support for user-defined accent colors and effects

### Theme Customization

```cpp
// Theme customizer dialog
ThemeCustomizer customizer(this);
customizer.setAccentColor(QColor(0, 103, 192));
customizer.setWindowTransparency(5);
customizer.setBlurEffect(true);

if (customizer.exec() == QDialog::Accepted) {
    applyCustomTheme(customizer.getThemeSettings());
}
```

### User Profiles

```cpp
// User profile management
UserProfile* profile = new UserProfile(this);
profile->createProfile("Work", "Professional theme settings");
profile->saveThemeProfile("DarkBlue", themeSettings);
profile->switchToProfile("Work");
```

## Logging and Error Handling

### Logger System

```cpp
// Singleton logger instance
Logger* logger = Logger::instance();
logger->setLogLevel(LogLevel::Info);
logger->setLogToFile(true);

// Logging macros
LOG_INFO("Document loaded successfully");
LOG_ERROR("Failed to open PDF file");
LOG_TIMER_START("PDF_RENDERING");
// ... operation ...
LOG_TIMER_END("PDF_RENDERING");
```

### Error Handling

```cpp
// Error reporting system
ErrorHandler* errorHandler = ErrorHandler::instance();
errorHandler->setShowUserNotifications(true);

// Report errors with context
REPORT_ERROR(ErrorSeverity::Error, ErrorCategory::PDF, 
            "Load Failed", "Could not open PDF file");

REPORT_FILE_ERROR(filePath, "open");
```

### Performance Monitoring

```cpp
// Built-in performance tracking
void PdfController::loadDocument(const QString& filePath)
{
    LOG_TIMER_START("DOCUMENT_LOAD");
    
    // PDF loading logic...
    
    LOG_TIMER_END("DOCUMENT_LOAD");
    LOG_INFO(QString("Document loaded: %1").arg(filePath));
}
```

## Testing Framework

### Test Structure

```cpp
class TestPdfViewer : public QObject
{
    Q_OBJECT

private slots:
    void testMainWindowCreation();
    void testDocumentLoading();
    void testThemeApplication();
    void testAnnotationSystem();
};
```

### Running Tests

```bash
# Build tests
cmake --build . --target pdf_viewer_tests

# Run all tests
ctest --output-on-failure

# Run specific test
./pdf_viewer_tests -test TestPdfViewer::testMainWindowCreation

# Memory leak detection (Linux)
valgrind --tool=memcheck ./pdf_viewer_tests
```

### Test Coverage

```bash
# Generate coverage report (GCC)
cmake --build . --target coverage
# Report available in coverage_html/index.html
```

## Contributing Guidelines

### Code Style

- **Qt Conventions**: Follow Qt coding conventions
- **Modern C++**: Use C++17 features where appropriate
- **Documentation**: Document public APIs with Doxygen comments
- **Error Handling**: Use RAII and proper exception handling

### Commit Guidelines

```
feat: add new PDF annotation tools
fix: resolve memory leak in page cache
docs: update developer guide
test: add unit tests for theme system
refactor: modernize dialog components
```

### Pull Request Process

1. **Fork Repository**: Create personal fork
2. **Feature Branch**: Create feature-specific branch
3. **Implementation**: Implement changes with tests
4. **Documentation**: Update relevant documentation
5. **Testing**: Ensure all tests pass
6. **Review**: Submit pull request for review

### Development Environment

```bash
# Install dependencies (Ubuntu/Debian)
sudo apt-get install qt6-base-dev qt6-tools-dev cmake build-essential
sudo apt-get install libpoppler-qt6-dev

# Install ElaWidgetTools
# Follow ElaWidgetTools installation instructions

# Configure IDE (VS Code recommended)
# Install Qt extension and CMake tools
```

### Debugging

```cpp
// Debug builds include additional logging
#ifdef QT_DEBUG
    logger->setLogLevel(LogLevel::Debug);
    logger->debug("Entering function", Q_FUNC_INFO);
#endif

// Memory debugging
#ifdef QT_DEBUG
    // Enable Qt object debugging
    qputenv("QT_LOGGING_RULES", "qt.qpa.fonts.debug=true");
#endif
```

---

*PDF Viewer Developer Guide v1.0 - Modern Qt6 application with ElaWidgetTools*
